{
  "$schema": "https://json.schemastore.org/tsconfig",
  "compilerOptions": {
    "target": "ES6",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "baseUrl": "..",
    "paths": {
      "@/*": [
        "src/*"
      ]
    },
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
  },
  "files": [
    "./global.d.ts",
    "./css.d.ts"
  ],
  "include": [
    "${configDir}/src",
    "${configDir}/test",
    "${configDir}/tests"
  ],
  "exclude": [
    "${configDir}/node_modules"
  ]
}
