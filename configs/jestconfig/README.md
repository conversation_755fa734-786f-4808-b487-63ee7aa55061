## 单测配置

### 安装
pnpm install jestconfig --filter xxx

### 配置
1、如果是ts项目，tsconfig中includes添加 "./test"
2、scripts中增加test: run-jest

### 写单测
在src同级目录创建test目录

test/config目录存放某些文件的mock文件
test/unit目录存在单测文件，目录结构同src，文件命名为xxx.test.(ts|tsx)

### 单测工具

使用@testing-library，可在业务项目中按需引入[文档](https://testing-library.com/docs/react-testing-library/intro)

### 断言工具

使用jest原生expect[文档](https://jest-archive-august-2023.netlify.app/docs/27.x/expect)

### 示例

可参考pc-components中的单测示例
