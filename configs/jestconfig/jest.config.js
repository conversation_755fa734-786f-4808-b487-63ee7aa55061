const fs = require('fs');
const path = require('path');

const rootDir = fs.realpathSync(process.cwd());
const babelConfigPath = path.resolve(__dirname, './babel.config.json');

const env = process.env.JEST_ENV || 'jsdom';

const isSetupFileExist = fs.existsSync(path.resolve(rootDir, './test/setup.ts'));

module.exports = {
  rootDir,
  verbose: true,
  testEnvironment: env,
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageProvider: 'v8',
  collectCoverageFrom: [
    '<rootDir>/src/**/*.{js,jsx,ts,tsx}',
  ],
  coveragePathIgnorePatterns: [
    '/node_modules/',
  ],
  coverageReporters: ['json', 'text', 'lcov', 'clover'],
  moduleFileExtensions: ['js', 'jsx', 'ts', 'tsx'],
  testMatch: [
    '<rootDir>/test/**/?(*.)+(test).[tj]s?(x)',
    '<rootDir>/src/**/tests/**/*.(spec|test).[jt]s?(x)',
  ],
  transformIgnorePatterns: [
    'no-disable',
  ],
  transform: {
    '^.+\\.[tj]sx?$': [
      'babel-jest',
      {
        configFile: babelConfigPath,
      },
    ],
  },
  moduleNameMapper: {
    'swiper/(.*)$': 'identity-obj-proxy',
    '\\.(css|less|scss)$': 'identity-obj-proxy',
    '\\.(jpg|jpeg|png|gif|svg)$': '<rootDir>/test/config/image-mock.ts',
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@components/(.*)$': '<rootDir>/src/components/$1',
    '^@tool/(.*)$': '<rootDir>/src/components/tool/$1',
    '^@test/(.*)$': '<rootDir>/test/$1',
    '^@static/(.*)$': '<rootDir>/static/$1',
  },
  setupFiles: isSetupFileExist ? [
    '<rootDir>/test/setup.ts',
  ] : [],
  reporters: [
    'default',
    [
      '@tencent/dwt-reporter',
      {
        publicPath: `${rootDir}/coverage`,
      },
    ],
  ],
  testRunner: '@tencent/dwt-runner/runner',
};
