# eslint-config-custom-qqnews

大仓 *eslint* 基础配置。

遵循 *eslint* 规范，目录名设置为 `eslint-config-custom-qqnews`。

> ESLint provides [many mechanisms](https://eslint.org/docs/latest/use/configure/configuration-files) for sharing configuration. However, it seems that when a monorepo delegates ESLint configuration to one of its own workspaces, the workspace directory must start with the prefix `eslint-config-`. Moreover, it appears to be convention for an unpublished ESLint configuration workspace to have either the name `eslint-config-custom` or a name starting with `eslint-config-custom-`.

## 说明

组件大仓中提供了基础的 `@tencent/eslint-config-qn` 规则，此配置为对组件大仓基础规则的进一步包装，供业务大仓各项目使用。

### 1. pure.js  

基础规则，适用于工具项目等 UI 无关项目，如 `@packages/model`。

> 对应 `@tencent/eslint-config-qn` 中的 *pure.js*

使用方式：

```js
// .eslintrc.js
module.exports = {
  extends: ['eslint-config-custom-qqnews/pure'],
};
```

### 2. react.js

React 项目规则，适用于使用 React 的项目，如 `@modules/article-content`。

> 对应 `@tencent/eslint-config-qn` 中的 *index.js*。（`注意`：并不是 *react.js*，因为 `@tencent/eslint-config-qn` 中的 *react.js* 仅包含 React 相关规则）。

使用方式：

```js
// .eslintrc.js
module.exports = {
  extends: ['eslint-config-custom-qqnews/react'],
};
```

### 3. index.js

默认导出版本，由于大仓中主要项目均使用 React，目前直接按 *react.js* 规则导出。

使用方式：

```js
// .eslintrc.js
module.exports = {
  extends: ['eslint-config-custom-qqnews'],
};
```

## Reference

<https://javascript.plainenglish.io/sharing-typescript-with-nx-and-turborepo-part-3-configuring-a-monorepo-2e4608701964>
