{"name": "qqnews-platform", "version": "1.0.0", "description": "平台项目大仓", "scripts": {"prepare": "husky install", "preinstall": "npx only-allow pnpm", "change": "pnpm changeset", "version": "pnpm changeset version", "test": "turbo run test --parallel", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix --parallel", "eslint": "eslint . --ext ts,tsx,js,jsx,mjs", "eslint:fix": "eslint . --fix --ext ts,tsx,js,jsx,mjs", "stylelint": "stylelint '**/*.{css,scss}'", "stylelint:fix": "stylelint --fix '**/*.{css,scss}'", "tscheck": "turbo run tscheck --parallel", "pub": "turbo run pub --parallel", "pub:test": "turbo run pub:test --parallel", "dev:field": "turbo dev --filter field-management", "build:field": "pnpm --filter field-management run build", "dev:pmonitor": "turbo dev --filter pmonitor-client", "build:pmonitor": "pnpm --filter pmonitor-client run build", "build:pmonitor-server": "pnpm --filter pmonitor-server run build", "dev:render-server": "turbo dev --filter render-server", "build:render-server": "pnpm --filter render-server run build", "dev": "turbo run dev", "build": "turbo run build", "create-app": "node packages/create-app/bin/cli.js", "new": "pnpm create-app", "pack-template": "pnpm --filter @packages/create-app run pack-template"}, "devDependencies": {"@babel/preset-react": "^7.18.6", "@changesets/cli": "2.27.1", "@commitlint/cli": "^17.6.3", "@commitlint/config-conventional": "^17.6.3", "@commitlint/config-pnpm-scopes": "^17.6.3", "@tencent/stylelint-config-qn": "^1.1.5", "@tencent/tupload2": "^2.0.1", "@types/jest": "^27.5.1", "@types/lodash": "^4.17.16", "@types/node": "^20.16.0", "@types/ua-parser-js": "^0.7.36", "cross-env": "^7.0.3", "eslint": "^8.40.0", "fs-extra": "^11.2.0", "husky": "^8.0.3", "identity-obj-proxy": "3.0.0", "jest": "^27.5.1", "jest-environment-jsdom": "^27.5.1", "lint-staged": "^11.2.6", "pnpm": "10.5.2", "shelljs": "^0.9.2", "stylelint": "^15.7.0", "syncpack": "^13.0.0", "tsx": "^4.19.3", "turbo": "^1.10.1", "typescript": "^5.7.2", "typescript-plugin-css-modules": "^5.0.1"}, "volta": {"node": "20.16.0", "pnpm": "10.5.2"}}