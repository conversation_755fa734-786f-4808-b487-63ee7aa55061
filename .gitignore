# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js

# next.js
.next/
out/
build
dist
.vscode

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env.local
.env.development.local
.env.test.local
.env.production.local

# turbo
.turbo

# IDE
.idea

# typescript
*.tsbuildinfo

# testing
coverage
dwt*
result.json

# vercel
.vercel

# logs
logs

# 忽略本地hel启动项目生成的中间目录
.pages
