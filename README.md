# 新闻平台业务大仓

## 项目介绍
腾讯新闻前平台业务大仓，包括以下业务：

- render-server 统一渲染服务
- field-management 字段管理系统
- tupload 上传服务前端

## 快速上手

```shell
# install volta or upgrade volta latest version >= 1.1.1
curl https://get.volta.sh | bash
 
# clone repo
git clone https://git.woa.com/qqnews_web/qqnews-platform.git 

# install dependencies 
cd qqnews && pnpm install 

# start dev 
pnpm dev --filter tupload
```

## 新建前端项目

```shell
pnpm create-app projectName
```

## 开发

```shell
# 前端项目
pnpm dev --filter tupload  # localhost:3006
pnpm dev --filter feild-management  # localhost:3007
pnpm dev --filter client-template  # localhost:3008

# Node项目
pnpm dev --filter render-server  # localhost:3001
```

## 部署

> 正式环境使用Git Tag来发布。Tag的命名规则使用 [当前子项目].[\w+].[\w+].[\w+] 四段式（如 h5.7.1.88），
> 其中第一位为子项目package.json中的name字段值，后3位数字为当前迭代，最后一位可以根据情况来递增。
### 前端项目

非正式环境发布请参考 [流水线](https://devops.woa.com/console/pipeline/news-h5/p-78263acedc30495fa189b842ec372321/history/history/5?page=1&pageSize=20)
正式环境发布请参考 [流水线](https://devops.woa.com/console/pipeline/news-h5/p-78263acedc30495fa189b842ec372321/history/history/5?page=1&pageSize=20)

```shell
# 前端项目
pnpm build --filter tupload
pnpm build --filter feild-management
pnpm build  --filter client-template

# Node项目
pnpm build  --filter render-server
```

### 渲染服务
Node渲染服务在123进行部署
[123](https://123.woa.com/v2/test#/server-manage/index?app=render_server&server=common_render&_tab_=nodeManage)

## 如何接入通用渲染服务

通用渲染服务支持多域名接入

### 第一步
前往[TNEWS](https://tnews.woa.com/console/project/list)平台注册项目

### 第二步
修改[流水线](https://devops.woa.com/console/pipeline/news-h5/p-78263acedc30495fa189b842ec372321/history/history/5?page=1&pageSize=20)

在流水线应用环境变量设置中添加应用配置
```js
const apps = [
  {
    // 应用名称（tnews平台获取）
    name: 'newscontent-test',
    // 应用token(tnews平台获取)
    token: 'xxxxxxxx',
    // 应用目录名称
    dirName: 'field-management',
    // 应用package.json名称
    packageName: 'field-management',
    // 应用描述
    desc: '总库字段管理'
  },
];
```

然后在流水线CMS_APP_DIRNAME变量中添加应用选项，选项的值和dirName一致

### 第三步
参考client-template项目中的vite.config.ts文件配置修改原项目配置

```js
// vite.config.ts的base设置
base: process.env.NODE_ENV === 'production' ? `https://mat1.gtimg.com/qqcdn/${process.env.CMS_APP_NAME}` : env.VITE_BASE_URL,

build: {
  // 设置最终构建的浏览器兼容目标
  target: 'es2015',
  // 构建后是否生成 source map 文件
  sourcemap: false,
  //  chunk 大小警告的限制（以 kbs 为单位）
  chunkSizeWarningLimit: 2000,
  // 启用/禁用 gzip 压缩大小报告
  reportCompressedSize: true,
  // 构建输出目录设置为BUILD
  outDir: resolve(__dirname, 'build'),
},
```
不管是webpack项目还是vite项目，主要变更有两个：

1. 修改vite.config.ts的base设置，流水线在执行时会注入CMS_APP_NAME参数，而且默认会将打包的静态资源上传到CDN，CDN地址如上
2. 修改build的outDir设置为build

### 第四步
前往[七彩石平台添加项目配置](https://rainbow.woa.com/console/143fe199-d56b-4fff-a2c8-7bbfe5664b6d/prod/list?group_name=hosts)

在hosts分组下添加配置，Key是项目域名，value格式如下：
```js
{
  // 应用名称
  "appName": "newscontent",
  // 盘古配置
  "pangu": {
    "projectId": "123456",
    "loginType": "IOA",
    "token": "xxxxx",
    "domain": "woa",
    "isProd": true,
  },
  // 太湖配置
  "taihu": {
    "token": "xxxxxx"
  }
}
```

⚠️ 注意：在dev、test、prod三个环境均需要添加配置，请务必确保配置完整性！

## 常见问题
 * 子仓版本发布参考:[workspace 本地依赖管理](https://doc.weixin.qq.com/doc/w3_AIAAZwZ8AAoq1096CdaTo0Xrp0Smv?scode=AJEAIQdfAAo12MCr5dAIAAZwZ8AAo)

## 行为准则

- [JS代码安全规范](https://git.woa.com/standards/security/blob/master/JavaScript%E5%AE%89%E5%85%A8%E8%A7%84%E8%8C%83.md)
- [CSS编码规范](https://git.woa.com/standards/css)
- [JavaScript/TypeScript 编码规范](https://git.woa.com/standards/javascript)
- [谷歌工程实践Code Review](https://jimmysong.io/eng-practices/docs/review/reviewer/standard/)
- [CR规范](https://iwiki.woa.com/p/4007749550)

## 如何加入

我们十分期待您的任何贡献，无论是修复错别字、提 Bug 还是提交一个新的特性。

如果您使用过程中发现 Bug，请通过 [issues](/issues) 来提交并描述相关的问题，您也可以在这里查看其它的 issue，通过解决这些 issue 来贡献代码。

如果您是第一次贡献代码，请阅读 [CONTRIBUTING](https://iwiki.woa.com/p/4009057084) 了解我们的贡献流程，并提交 Merge Request 给我们。

## 团队介绍

+ [团队 iWiki 文档空间](https://iwiki.woa.com/p/656936388)
+ [团队 KM 空间](https://km.woa.com/group/45856)

## 咨询

联系项目负责人lreliawu