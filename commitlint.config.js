const pnpmConfig = require('@commitlint/config-pnpm-scopes');

// 添加自定义 scope
// reference: https://github.com/conventional-changelog/commitlint/blob/master/%40commitlint/config-pnpm-scopes/index.js
const extraScopes = ['root'];
const getScopeEnum = (extraScopes = []) => ctx => pnpmConfig.utils
  .getProjects(ctx)
  .then(scopes => extraScopes.concat(scopes))
  .then(scopeList => [2, 'always', scopeList]);

// reference: https://commitlint.js.org/reference/rules-configuration.html
module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'scope-empty': [2, 'never'],
    'scope-enum': ctx => getScopeEnum(extraScopes)(ctx),
  },
};
