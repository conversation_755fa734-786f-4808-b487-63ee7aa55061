#!/usr/bin/env sh

# 拉取最新的master分支
git fetch origin master

# git diff, 筛选出src有变更的组件库目录
changed_dirs=$(git diff origin/master --name-only | grep -E "^packages/[^/]+/src|^modules/[^/]+/src" | awk -F/ '{print $2}' | uniq)

# 遍历所有变更的组件库，找出package.json中version字段没更新的组件库
need_update_dirs=$(
for dir in $changed_dirs; do
  pkg_json_packages=packages/"$dir"/package.json
  pkg_json_modules=modules/"$dir"/package.json
  if [ -f "$pkg_json_packages" ]; then
    pkg_json=$pkg_json_packages
  elif [ -f "$pkg_json_modules" ]; then
    pkg_json=$pkg_json_modules
  else
    continue
  fi
  if ! git diff origin/master $pkg_json | grep -q "^+.*\"version\""; then
    echo "$dir"
  fi
done
)

# 设置颜色
set_colors() {
  red=`tput setaf 1`
  green=`tput setaf 2`
  orange=`tput setaf 3`
  reset=`tput sgr0`
}

print_line() {
  if [ "$need_update_dirs" ]; then
    echo
    echo ${orange}~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  fi
}

# 打印终止信息
print_termination_info() {
  if [ "$need_update_dirs" ]; then
    echo ${green}
    echo 发布版本
    echo - \`pnpm run change\` 选择要发布的子仓并输入发布内容
    echo - \`pnpm run version\` 更新子仓版本
    echo ${orange}
    echo 检测误报
    echo 检测工具与 origin/master 分支对比，请按以下步骤同步 master 代码
    echo - \`git stash\`
    echo - \`git checkout master\`
    echo - \`git pull --rebase\`
    echo - \`git checkout -\`
    echo - \`git rebase origin/master\`
    echo - \`git stash pop\`
    echo ${red} 
    echo 绕过检测 
    echo \`git commit -m "xx" --no-verify\`${green}
    echo - 修复eslint报错
    echo - 不影响组件打包产物的改动
    echo
    exit 1
  fi
}

# 打印提示
print_warning() {
  for dir in $need_update_dirs; do
    echo
    echo ${red}【注意】子仓 \""$dir"\" 代码有变更，但版本号未更新！
  done
}

# 主函数
main() {
  set_colors
  print_line
  print_warning
  print_line
  print_termination_info
}

# 调用主函数
main