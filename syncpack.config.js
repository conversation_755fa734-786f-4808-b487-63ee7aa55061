const config = {
  dependencyTypes: [
    'prod',
  ],
  versionGroups: [
    {
      label: 'classnames固定版本2.3.1',
      dependencies: [
        'classnames',
      ],
      pinVersion: '2.3.1',
    },
    {
      label: 'dayjs固定版本1.11.13',
      dependencies: [
        'dayjs',
      ],
      pinVersion: '1.11.13',
    },
    {
      label: 'lodash固定版本4.17.21',
      dependencies: [
        'lodash',
      ],
      pinVersion: '4.17.21',
    },
    {
      label: 'qs固定版本6.12.1',
      dependencies: [
        'qs',
      ],
      pinVersion: '6.12.1',
    },
    {
      label: '其余依赖暂时不做处理,如果需要升级版本,修改上述pinVersion后执行pnpm exec syncpack fix-mismatches可自动修复所有仓库的版本',
      dependencies: [
        '**',
      ],
      isIgnored: true,
    },
  ],
};

module.exports = config;
