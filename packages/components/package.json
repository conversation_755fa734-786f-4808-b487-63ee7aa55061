{"name": "@packages/components", "version": "1.0.3", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "license": "MIT", "scripts": {"lint": "eslint . --ext ts,tsx,js,jsx,mjs", "lint:fix": "eslint . --fix --ext ts,tsx,js,jsx,mjs"}, "peerDependencies": {"@monaco-editor/react": "^4.7.0", "monaco-editor": "^0.52.2", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "eslint-config-custom-qqnews": "workspace:^", "tsconfig": "workspace:^"}, "volta": {"node": "20.16.0", "pnpm": "10.5.2"}}