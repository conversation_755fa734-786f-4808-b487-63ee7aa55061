import React, { useEffect, useRef, memo, useCallback, useState } from 'react';
import { editor } from 'monaco-editor';
import Editor from '@monaco-editor/react';
import styles from './index.module.scss';

export interface CodeEditorProps {
  value?: string;
  onChange?: (value?: string) => void;
  onMount?: (editor: editor.IStandaloneCodeEditor) => void;
  height?: number;
  width?: string | number;
  language?: string;
  theme?: string;
  disabled?: boolean;
  id?: string;
  className?: string;
}

export const CodeEditor: React.FC<CodeEditorProps> = ({
  value,
  onChange,
  onMount,
  height = 200,
  language = 'go',
  theme = 'light',
  className,
  width = '100%',
}) => {
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);
  const [isEditorReady, setIsEditorReady] = useState(false);

  const handleMount = useCallback((editor: editor.IStandaloneCodeEditor) => {
    editorRef.current = editor;
    setIsEditorReady(true);

    // 先调用外部的 onMount 回调
    onMount?.(editor);

    // 然后设置初始值
    if (value !== undefined && value !== editor.getValue()) {
      editor.setValue(value);
      // 强制刷新编辑器显示
      editor.layout();
    }
  }, [value, onMount]);

  useEffect(() => {
    if (editorRef.current && isEditorReady && value !== undefined) {
      const currentValue = editorRef.current.getValue();
      if (currentValue !== value) {
        editorRef.current.setValue(value);
        // 强制刷新编辑器显示
        editorRef.current.layout();
      }
    }
  }, [value, isEditorReady]);

  return (
    <Editor
      width={width}
      height={height}
      onMount={handleMount}
      onChange={onChange}
      language={language}
      theme={theme}
      className={`${styles.codeEditor} ${className}`}
      value={value} // 确保传递 value 属性
      options={{
        automaticLayout: true, // 自动布局
        scrollBeyondLastLine: false,
        minimap: { enabled: false },
        wordWrap: 'on',
      }}
    />
  );
};

export default memo(CodeEditor);
