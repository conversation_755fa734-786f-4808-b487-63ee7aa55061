import type { Res } from './common';

export interface UserInfo {
  name: string;
  avatar: string;
  auths: number[];
  roles: number[];
  authLoading: boolean;
  [key: string]: any;
}

export interface AuthResData {
  id: number;
  nodeType: number;
  parentId: number;
  name: string;
  parentName: string;
  projectId: number;
  apiPath: string;
  sensitiveLevel: number;
  updateUser: string;
  insertUser: string;
  extension: string;
  remark: string;
  insertTime: string;
  updateTime: string;
  excludesAuthCreator: string;
  excludesAuthUpdator: string;
  excludesAuthComment: string;
  key: string;
  title: string;
  children: AuthResData[];
}

export type AuthRes = Res<AuthResData[]>;

export interface RoleResDataUser {
  id: number;
  projectId: number;
  name: string;
  loginId: string;
  loginType: number;
  inUse: number;
  isAdmin: number;
  department: string;
  insertTime: string;
  updateTime: string;
  allCnt: number;
  groupType: number;
  mailGroup: string;
}

export interface RoleResDataRoleGroupList {
  id: number;
  receiptId: string;
  userId: number;
  name: string;
  projectId: number;
  roleId: number;
  approvedBy: string;
  approverGroup: string;
  approved: number;
  insertTime: string;
  validTime: string;
  updateTime: string;
  blocked: number;
  blockedReason: number;
  authList: string;
  canApply: number;
  pid: number;
  updateUser: string;
  insertUser: string;
  adminUser: string;
  remark: string;
  templateId: number;
  roleSensitiveLevel: number;
  usable: number;
  tenantKey: string;
  extension: string;
  renewTemplateId: number;
}

export interface RoleResData {
  user: RoleResDataUser;
  roleGroupList: RoleResDataRoleGroupList[];
}

export type RoleRes = Res<RoleResData[]>;

export interface UserValue {
  value: string;
  label: string;
}

export interface UsersResData {
  en: string;
  full: string;
}

export type UsersRes = Res<UsersResData[]>;

export interface Role {
  id: number;
  projectId: number;
  name: string;
  authList: string;
  canApply: number;
  pid: number;
  updateUser: string;
  insertUser: string;
  adminUser: string;
  remark: string;
  templateId: number;
  roleSensitiveLevel: number;
  usable: number;
  insertTime: string;
  updateTime: string;
  extension: string;
  tenantKey: string;
}

export type GetAllRolesRes = Res<Role[]>;
export type GetAllAuthsRes = Res<AuthResData[]>;

