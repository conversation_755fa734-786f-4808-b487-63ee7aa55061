{"name": "@packages/services", "version": "1.0.4", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "license": "MIT", "scripts": {"lint": "eslint . --ext ts,tsx,js,jsx,mjs", "lint:fix": "eslint . --fix --ext ts,tsx,js,jsx,mjs"}, "devDependencies": {"eslint-config-custom-qqnews": "workspace:^", "tsconfig": "workspace:^"}, "dependencies": {"@packages/utils": "workspace:*", "@packages/types": "workspace:~"}, "volta": {"node": "20.16.0", "pnpm": "10.5.2"}}