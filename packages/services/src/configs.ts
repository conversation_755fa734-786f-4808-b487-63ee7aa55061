import type { Res } from '@packages/types';
import { requestGet } from '@packages/utils';

export async function fetchConfigs<T>(group: string, key: string) {
  const res = await requestGet<Res<T>>('/renderapi/config/get_configs', {
    group,
    key,
  });
  return res.data;
}

export async function fetchGroupConfigs<T>(group: string) {
  const res = await requestGet<Res<T>>('/renderapi/config/get_group_configs', {
    group,
  });
  return res.data;
}
