import type { RoleRes, AuthRes, UsersRes, GetAllAuthsRes, GetAllRolesRes, Res } from '@packages/types';
import { requestGet } from '@packages/utils';

/**
 * 获取用户名
 * @returns 用户名字符串，失败返回空字符串
 */
export const fetchUserName = async () => {
  try {
    const res = await requestGet<Res<string>>('/renderapi/user/user_name');
    return res.data;
  } catch (error) {
    return '';
  }
};

/**
 * 获取角色列表
 * @returns 角色组列表，失败返回空数组
 */
export const fetchRoleList = async () => {
  try {
    const res = await requestGet<RoleRes>('/renderapi/user/role_list');
    const { roleGroupList } = res.data?.[0] || {};
    return roleGroupList;
  } catch (error) {
    return [];
  }
};

/**
 * 获取用户权限
 * @returns 权限列表，失败返回空数组
 */
export const fetchAuths = async () => {
  try {
    const res = await requestGet<AuthRes>('/renderapi/user/auths');
    // 打平权限树结构的函数
    const flattenAuthTree = (authItems: any[], result: any[] = []) => {
      if (!authItems || !Array.isArray(authItems)) return result;

      for (const item of authItems) {
        result.push(item);
        if (item.children && Array.isArray(item.children)) {
          flattenAuthTree(item.children, result);
        }
      }
      return result;
    };
    // 返回打平后的权限列表
    return flattenAuthTree(res.data);
  } catch (error) {
    return [];
  }
};

/**
 * 根据输入字符串搜索用户
 * @param toMatchStr 搜索关键字
 * @returns 用户列表，包含label和value，失败返回空数组
 */
export const fetchUsers = async (toMatchStr: string) => {
  if (toMatchStr.length < 2) {
    return [];
  }
  try {
    const res = await requestGet<UsersRes>('//xc.woa.com/api/user/searchUser', { name: toMatchStr });
    if (res.data?.length) {
      return res.data.map(item => ({
        label: item.full,
        value: item.en,
      })).slice(0, 10);
    }
    return [{
      label: toMatchStr,
      value: toMatchStr,
    }];
  } catch (error) {
    return [];
  }
};

/**
 * 获取所有权限
 * @returns 权限列表，失败返回空数组
 */
export const fetchAllAuths = async () => {
  try {
    const res = await requestGet<GetAllAuthsRes>('/renderapi/user/all_auths');
    return res.data;
  } catch (error) {
    return [];
  }
};

/**
 * 获取所有角色
 * @returns 角色列表，失败返回空数组
 */
export const fetchAllRoles = async () => {
  try {
    const res = await requestGet<GetAllRolesRes>('/renderapi/user/all_roles');
    return res.data;
  } catch (error) {
    return [];
  }
};
