{"name": "@packages/utils", "version": "1.0.3", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "license": "MIT", "scripts": {"lint": "eslint . --ext ts,tsx,js,jsx,mjs", "lint:fix": "eslint . --fix --ext ts,tsx,js,jsx,mjs"}, "peerDependencies": {"@tencent/qn-request": "^3.0.3"}, "devDependencies": {"eslint-config-custom-qqnews": "workspace:^", "tsconfig": "workspace:^"}, "volta": {"node": "20.16.0", "pnpm": "10.5.2"}}