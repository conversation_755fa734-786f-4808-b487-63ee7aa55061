import { Request, RequestConfig } from '@tencent/qn-request';

export const request = new Request({
  retryCount: 3,
  timeout: 10000,
});

export const requestGet = async <T>(url: string, data?: Record<string, any>, config?: RequestConfig) => {
  const res = await request.get<T>({ url, data, config });
  return res.data;
};

export const requestPost = async <T>(url: string, data?: Record<string, any>, config?: RequestConfig) => {
  const res = await request.post<T>({ url, data, config });
  return res.data;
};
