{"name": "@packages/create-app", "version": "1.0.1", "description": "快速创建基于 client-template 的新项目", "main": "dist/index.js", "bin": {"create-app": "./bin/cli.js"}, "scripts": {"build": "tsc", "dev": "tsc -w", "pack-template": "ts-node src/pack-template.ts", "prepare": "pnpm build"}, "dependencies": {"archiver": "^5.3.1", "chalk": "^4.1.2", "commander": "^11.1.0", "extract-zip": "^2.0.1", "fs-extra": "^11.2.0", "inquirer": "^8.2.6", "ora": "^5.4.1"}, "devDependencies": {"@types/archiver": "^5.3.4", "@types/extract-zip": "^2.0.1", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^8.2.10", "eslint-config-custom-qqnews": "workspace:^", "rimraf": "^5.0.5", "ts-node": "^10.9.2", "typescript": "^5.2.2", "tsconfig-paths": "^4.2.0", "tsconfig": "workspace:*"}}