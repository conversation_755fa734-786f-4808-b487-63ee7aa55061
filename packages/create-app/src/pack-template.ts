import path from 'path';
import fs from 'fs-extra';
import archiver from 'archiver';

async function packTemplate() {
  const templateDir = path.join(__dirname, '../../../apps/client-template');
  const outputDir = path.join(__dirname, '../template');
  const outputFile = path.join(outputDir, 'template.zip');

  // 检查模板目录是否存在
  if (!fs.existsSync(templateDir)) {
    throw new Error(`模板目录不存在: ${templateDir}`);
  }

  // 检查模板目录是否为空
  const files = await fs.readdir(templateDir);
  if (files.length === 0) {
    throw new Error(`模板目录为空: ${templateDir}`);
  }

  console.log('开始打包模板...');
  console.log('模板目录:', templateDir);
  console.log('输出文件:', outputFile);

  // 确保输出目录存在
  await fs.ensureDir(outputDir);

  // 如果输出文件已存在，先删除
  if (fs.existsSync(outputFile)) {
    await fs.remove(outputFile);
  }

  // 创建写入流
  const output = fs.createWriteStream(outputFile);
  const archive = archiver('zip', {
    zlib: { level: 9 }, // 最大压缩级别
  });

  return new Promise<void>((resolve, reject) => {
    output.on('close', () => {
      console.log('模板打包成功！');
      console.log('文件大小:', archive.pointer(), '字节');
      resolve();
    });

    output.on('error', (err) => {
      console.error('写入错误:', err);
      reject(err);
    });

    archive.on('error', (err) => {
      console.error('压缩错误:', err);
      reject(err);
    });

    archive.on('warning', (err) => {
      if (err.code === 'ENOENT') {
        console.warn('警告:', err);
      } else {
        reject(err);
      }
    });

    archive.on('entry', (entry) => {
      console.log('添加文件:', entry.name);
    });

    // 管道连接
    archive.pipe(output);

    // 添加文件到压缩包，使用相对路径
    archive.directory(templateDir, '.', (entry) => {
      const filename = entry.name;
      // 排除不需要的文件和目录
      if (
        filename.startsWith('node_modules')
        || filename.startsWith('.git')
        || filename.startsWith('dist')
        || filename.startsWith('build')
        || filename === '.DS_Store'
      ) {
        console.log('排除文件:', filename);
        return false;
      }
      return entry;
    });

    // 完成压缩
    void archive.finalize();
  });
}

// 如果直接运行此文件则执行打包
if (require.main === module) {
  packTemplate().catch((err) => {
    console.error('模板打包失败:', err);
    process.exit(1);
  });
}

export { packTemplate };
