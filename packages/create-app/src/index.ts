import path from 'path';
import fs from 'fs-extra';
import chalk from 'chalk';
import inquirer from 'inquirer';
import ora from 'ora';
import extract from 'extract-zip';

interface CreateOptions {
  name: string;
  description?: string;
}

export async function create(options: CreateOptions) {
  const { name, description = '' } = options;

  // 验证项目名称
  if (!/^[a-zA-Z][\w-]*$/.test(name)) {
    console.error(chalk.red('项目名称必须以字母开头，且只能包含字母、数字、下划线和中划线'));
    process.exit(1);
  }

  const cwd = process.cwd();
  const targetDir = path.join(cwd, 'apps', name);

  // 检查目标目录是否存在
  if (fs.existsSync(targetDir)) {
    const { overwrite } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'overwrite',
        message: `目标目录 ${chalk.cyan(targetDir)} 已存在。是否覆盖？`,
        default: false,
      },
    ]);

    if (!overwrite) {
      console.log(chalk.yellow('操作取消'));
      process.exit(1);
    }

    await fs.remove(targetDir);
  }

  const spinner = ora('正在创建项目...').start();

  try {
    // 解压模板
    const templatePath = path.join(__dirname, '../template/template.zip');
    await extract(templatePath, { dir: targetDir });

    // 更新 package.json
    const pkgPath = path.join(targetDir, 'package.json');
    const pkg = await fs.readJson(pkgPath);
    pkg.name = name;
    pkg.description = description;
    pkg.version = '0.1.0';
    await fs.writeJson(pkgPath, pkg, { spaces: 2 });

    // 更新 README.md
    const readmePath = path.join(targetDir, 'README.md');
    let readmeContent = await fs.readFile(readmePath, 'utf-8');
    readmeContent = readmeContent.replace(/# Client Template/, `# ${name}`);
    readmeContent = readmeContent.replace(/基于 React 19 \+ TypeScript \+ Vite 的后台管理系统模板。/, description || '基于 client-template 创建的项目。');
    await fs.writeFile(readmePath, readmeContent);

    spinner.succeed(chalk.green('项目创建成功！'));

    console.log('\n执行以下命令启动项目：\n');
    console.log(chalk.cyan('  pnpm install'));
    console.log(chalk.cyan(`  cd apps/${name}`));
    console.log(chalk.cyan('  pnpm dev\n'));
  } catch (error) {
    spinner.fail(chalk.red('项目创建失败！'));
    console.error(error);
    process.exit(1);
  }
}
