/**
 * Common types used across the application
 */

export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

export interface McpRequest {
  projectId: string;
  apiId?: string;
  token?: string;
  baseUrl?: string;
  page?: number;
  limit?: number;
  outputType?: 'api' | 'ts' | 'both';
}

export interface McpResponse {
  code: number;
  message: string;
  data: {
    apiInfo?: ApiInfo;
    tsTypes?: string;
  };
}

export interface ApiInfo {
  name: string;
  description?: string;
  url?: string;
  method?: string;
  requestParams?: any;
  responseData?: any;
  headers?: Record<string, string>;
  tags?: string[];
  status?: string;
  createdAt?: string;
  updatedAt?: string;
}
