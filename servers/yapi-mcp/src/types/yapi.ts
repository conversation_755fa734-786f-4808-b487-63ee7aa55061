/**
 * Types for Yapi platform API
 */

export interface YapiApiResponse {
  errcode: number;
  errmsg: string;
  data: YapiApiData | YapiApiListData | YapiMenuListData | YapiCatMenuData | YapiAutoTestResponse | YapiImportDataResponse | YapiProjectCat;
}

/**
 * Response for a single API interface
 */
export interface YapiApiData {
  _id: number;
  project_id: number;
  catid: number;
  title: string;
  path: string;
  method: string;
  req_params: YapiReqParam[];
  req_body_form: YapiReqBodyForm[];
  req_headers: YapiReqHeader[];
  req_query: YapiReqQuery[];
  req_body_type: string;
  res_body_type: string;
  res_body: string;
  req_body_other?: string;
  desc: string;
  markdown: string;
  tag: string[];
  status: string;
  add_time: number;
  up_time: number;
  type: string;
  uid: number;
  username: string;
}

/**
 * Response for a list of API interfaces
 */
export interface YapiApiListData {
  count: number;
  total: number;
  list: YapiApiListItem[];
}

/**
 * Item in the API list
 */
export interface YapiApiListItem {
  _id: number;
  project_id: number;
  catid: number;
  title: string;
  path: string;
  method: string;
  tag: string[];
  status: string;
  add_time: number;
  up_time: number;
}

/**
 * Project information
 */
export interface YapiProjectInfo {
  _id: number;
  name: string;
  desc: string;
  basepath: string;
  members: YapiProjectMember[];
  env: YapiProjectEnv[];
  group_id: number;
  icon: string;
  color: string;
  add_time: number;
  up_time: number;
  is_json5: boolean;
  tag: string[];
  cat: YapiProjectCat[];
}

/**
 * Project member
 */
export interface YapiProjectMember {
  uid: number;
  username: string;
  email: string;
  role: string;
  add_time: number;
  up_time: number;
}

/**
 * Project environment
 */
export interface YapiProjectEnv {
  _id: string;
  name: string;
  domain: string;
  header: YapiReqHeader[];
  global: YapiGlobalParam[];
}

/**
 * Global parameter
 */
export interface YapiGlobalParam {
  name: string;
  value: string;
}

/**
 * Project category
 */
export interface YapiProjectCat {
  _id: number;
  name: string;
  project_id: number;
  desc: string;
  add_time: number;
  up_time: number;
}

export interface YapiReqParam {
  name: string;
  desc: string;
  example: string;
  required: string;
}

export interface YapiReqBodyForm {
  name: string;
  type: string;
  required: string;
  desc: string;
  example: string;
}

export interface YapiReqHeader {
  name: string;
  value: string;
  desc: string;
  required: string;
}

export interface YapiReqQuery {
  name: string;
  desc: string;
  required: string;
  example: string;
}

/**
 * Menu list data
 */
export interface YapiMenuListData {
  count: number;
  total: number;
  list: YapiMenuItem[];
}

/**
 * Menu item
 */
export interface YapiMenuItem {
  _id: number;
  name: string;
  project_id: number;
  desc: string;
  add_time: number;
  up_time: number;
  index: number;
}

/**
 * Category menu data
 */
export interface YapiCatMenuData {
  cat: YapiProjectCat[];
}

/**
 * Auto test response
 */
export interface YapiAutoTestResponse {
  message: string;
  list: YapiAutoTestResult[];
}

/**
 * Auto test result
 */
export interface YapiAutoTestResult {
  name: string;
  path: string;
  status: string;
  code: number;
  validRes: boolean;
  params: Record<string, any>;
  res_body: string;
  req_body: string;
  req_headers: Record<string, string>;
  statusText: string;
}

/**
 * Import data response
 */
export interface YapiImportDataResponse {
  message: string;
  data: {
    n: number; // Number of imported items
  };
}

/**
 * Interface add/save/update request
 */
export interface YapiInterfaceAddRequest {
  title: string;
  path: string;
  method: string;
  catid: number;
  project_id: number;
  req_params?: YapiReqParam[];
  req_body_form?: YapiReqBodyForm[];
  req_headers?: YapiReqHeader[];
  req_query?: YapiReqQuery[];
  req_body_type?: string;
  res_body_type?: string;
  res_body?: string;
  req_body_other?: string;
  desc?: string;
  markdown?: string;
  tag?: string[];
  status?: string;
}

/**
 * Category add request
 */
export interface YapiCatAddRequest {
  name: string;
  project_id: number;
  desc?: string;
}
