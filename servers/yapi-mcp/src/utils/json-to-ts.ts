import json2TypesModule from '@tencent/gencode-json-to-types';
import { logger } from './logger.js';

// Extract the actual function from the module
const json2Types = json2TypesModule.default || json2TypesModule;

/**
 * Generates TypeScript types from JSON
 * @param jsonData - The JSON object
 * @returns Promise with generated TypeScript types as string
 */
export async function generateTsFromJson(jsonData: any): Promise<string> {
  try {
    // Generate TS types directly using the library
    // The library can process the JSON object directly
    const result = json2Types(jsonData, {
      useType: false,
    });

    // Return the generated code
    return result || '// No types generated from JSON';
  } catch (error) {
    logger.error('Error in generateTsFromJson:', error);
    throw new Error(`Failed to generate TypeScript from JSON: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
