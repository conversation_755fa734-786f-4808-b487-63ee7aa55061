import { swagger2Types } from '@tencent/gencode-swagger-to-types';
import { logger } from './logger.js';

/**
 * Generates TypeScript types from Swagger JSON
 * @param swaggerJson - The Swagger JSON object
 * @returns Promise with generated TypeScript types as string
 */
export async function generateTsFromSwagger(swaggerJson: any): Promise<string> {
  try {
    // Generate TS types directly using the library
    // The library can process the Swagger JSON object directly
    const result = await swagger2Types({
      schema: swaggerJson,
      useType: false,
    });

    // Extract the generated code from the result
    if (result?.codes) {
      return result.codes.toString();
    }

    return '// No types generated from Swagger';
  } catch (error) {
    logger.error('Error in generateTsFromSwagger:', error);
    throw new Error(`Failed to generate TypeScript from Swagger: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
