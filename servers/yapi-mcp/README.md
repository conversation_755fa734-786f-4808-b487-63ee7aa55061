# YAPI-MCP 服务

YAPI-MCP 是一个基于 Model Context Protocol (MCP) 的服务，用于从 YAPI 平台获取 API 信息并生成 TypeScript 类型定义。该服务为 AI 编程助手（如 Cursor、Cline、Tico 和 Augment Code）提供了强大的 YAPI 平台交互能力。

## 功能描述

YAPI-MCP 是一个专门用于处理 YAPI 平台相关逻辑的 MCP（Model Context Protocol）服务。该服务提供以下主要功能：

- 从 YAPI 平台获取 API 信息
- 在 YAPI 平台搜索特定关键词相关的 API
- 将 Swagger/OpenAPI JSON 内容转换为 TypeScript 类型定义
- 将普通 JSON 内容转换为 TypeScript 类型定义
- 生成 TypeScript 类型和 Axios 请求代码
- 基于关键词、分类或特定API生成TypeScript类型和请求函数
- 支持将整个项目的API生成到指定目录
- 管理 YAPI 平台的 API 接口（添加、保存、更新）
- 管理 YAPI 平台的 API 分类（添加、列表）
- 获取 YAPI 平台的接口菜单列表

YAPI-MCP 服务通过与 AI 编程助手的无缝集成，帮助开发者在 AI 编程过程中快速获取和理解 YAPI 平台中的 API 信息，提高开发效率。

## 配置与启动

### 环境变量

YAPI-MCP 服务支持以下环境变量：

- `YAPI_TOKEN`：YAPI 平台认证令牌
- `YAPI_BASE_URL`：YAPI 平台基础 URL，默认为 `http://yapi.pl.woa.com`
- `YAPI_PROJECT_ID`：默认的 YAPI 项目 ID
- `LOG_LEVEL`：日志级别，默认为 `info`
- `LOG_FILE`：日志文件路径，不设置则只输出到控制台

### 安装依赖

```bash
npm install
```

### 开发环境启动

```bash
npm run dev
```

### 生产环境部署

```bash
npm run build
npm start
```

## AI 编程工具接入方式

### Cursor 接入

```json
{
  "mcpServers": {
    "yapi-mcp": {
      "command": "npx",
      "args": ["-y", "@tencent/yapi-mcp"],
      "env": {
        "YAPI_PROJECT_ID": "<your yapi projectId>",
        "YAPI_TOKEN": "<your yapi project token>",
        "YAPI_BASE_URL": "http://yapi.pl.woa.com"
      }
    }
  }
}
```

### cline 接入

在 cline 配置文件中添加以下内容：

```json
{
  "mcpServers": {
    "yapi-mcp": {
      "command": "npx",
      "args": ["-y", "@tencent/yapi-mcp"],
      "env": {
        "YAPI_PROJECT_ID": "<your yapi projectId>",
        "YAPI_TOKEN": "<your yapi project token>",
        "YAPI_BASE_URL": "http://yapi.pl.woa.com"
      }
    }
  }
}
```

### Tico 接入

在 Tico 设置中：

```json
{
  "mcpServers": {
    "yapi-mcp": {
      "command": "npx",
      "args": ["-y", "@tencent/yapi-mcp"],
      "env": {
        "YAPI_PROJECT_ID": "<your yapi projectId>",
        "YAPI_TOKEN": "<your yapi project token>",
        "YAPI_BASE_URL": "http://yapi.pl.woa.com"
      }
    }
  }
}
```
### Augment Code 接入

在 Augment Code 配置文件中添加：

```json
{
  "mcpServers": {
    "yapi-mcp": {
      "command": "npx",
      "args": ["-y", "@tencent/yapi-mcp"],
      "env": {
        "YAPI_PROJECT_ID": "<your yapi projectId>",
        "YAPI_TOKEN": "<your yapi project token>",
        "YAPI_BASE_URL": "http://yapi.pl.woa.com"
      }
    }
  }
}
```

## 使用示例

以下是通过 AI 编程助手使用 YAPI-MCP 服务的示例。请注意，YAPI-MCP 服务需要结合 AI 编程助手使用，而非直接调用。

### 与 AI 助手结合的示例 Prompt

**示例 1：查找 API 信息**

```
请帮我查找 YAPI 平台中项目 ID 为 12345 中与用户登录和注册相关的 API。我需要了解这些 API 的请求和响应类型，并生成对应的 TypeScript 类型定义。
```

AI 助手会使用 `mcp.search-apis` 工具搜索相关 API，并展示结果。

**示例 2：生成 API 调用代码**

```
我需要实现用户登录功能。请帮我在 YAPI 平台中查找登录 API，并生成一个完整的 React 组件，包含表单和 API 调用逻辑。
```

AI 助手会使用 `mcp.get-api-info` 和 `mcp.search-apis` 工具获取 API 信息，然后生成所需代码。

**示例 3：理解 API 结构**

```
请帮我分析 YAPI 平台中项目 ID 为 12345 的 API 结构。我需要了解该项目提供了哪些接口，每个接口的输入和输出参数是什么，以及它们之间的关系。
```

AI 助手会使用 `mcp.get-api-info` 工具获取 API 信息并进行分析。

**示例 4：将 Swagger 转换为 TypeScript**

```
我有一个 Swagger 文档，需要将其转换为 TypeScript 类型定义，以便在前端项目中使用。请帮我完成转换。
```

AI 助手会使用 `mcp.convert-swagger-to-ts` 工具完成转换。

**示例 5：将 JSON 转换为 TypeScript**

```
我有一个 JSON 样例数据，请帮我生成对应的 TypeScript 接口定义。
```

AI 助手会使用 `mcp.convert-json-to-ts` 工具完成转换。

**示例 6：添加新的 API 接口**

```
我需要在 YAPI 平台的项目中添加一个新的用户注册 API 接口。这个接口接收用户名、密码和邮箱，返回用户 ID 和状态。请帮我创建这个接口并设置好请求和响应格式。
```

AI 助手会使用 `mcp.manage-interface` 工具添加新的 API 接口。

**示例 7：管理 API 分类**

```
请帮我在 YAPI 平台的项目中创建一个新的 API 分类，用于组织所有与用户管理相关的接口。分类名称为"用户管理"，描述为"用户注册、登录、信息管理等接口"。
```

AI 助手会使用 `mcp.manage-categories` 工具创建新的 API 分类。

**示例 8：获取接口菜单列表**

```
请帮我查看 YAPI 平台中项目 ID 为 12345 的所有接口菜单，我需要了解该项目的 API 组织结构和分类情况。
```

AI 助手会使用 `mcp.get-menu-list` 工具获取并展示接口菜单列表。

**示例 9：生成特定API的TypeScript类型和请求函数**

```
请帮我为YAPI平台中项目ID为12345的API ID为67890生成TypeScript类型定义和请求函数，我需要在前端项目中调用这个API。
```

AI 助手会使用 `mcp.generate-yapi-types` 工具生成TypeScript类型和请求函数。

**示例 10：为整个项目生成API类型库**

```
请帮我为YAPI平台中项目ID为12345生成完整的TypeScript API类型库，包含所有接口的类型定义和请求函数，输出到src/api目录。
```

AI 助手会使用 `mcp.generate-yapi-types` 工具生成完整的API类型库。

## 工具列表

### get-api-info

获取 YAPI 平台的 API 信息和 TypeScript 类型定义。

**参数：**
- `projectId`：YAPI 项目 ID
- `apiId`：特定 API 的 ID（可选）
- `token`：YAPI 平台认证令牌（可选，如未提供则使用环境变量）
- `baseUrl`：YAPI 平台基础 URL（可选，如未提供则使用环境变量）
- `page`：分页参数，页码（可选，默认为 1）
- `limit`：分页参数，每页项目数（可选，默认为 10）
- `outputType`：输出类型，可选值为 `api`、`ts` 或 `both`，默认为 `both`

### convert-swagger-to-ts

将 Swagger/OpenAPI JSON 内容转换为 TypeScript 类型定义。

**参数：**
- `swaggerJson`：Swagger/OpenAPI JSON 内容

### convert-json-to-ts

将普通 JSON 内容转换为 TypeScript 类型定义。

**参数：**
- `jsonContent`：JSON 内容

### search-apis

在 YAPI 平台中搜索 API。

**参数：**
- `keyword`：搜索关键词
- `projectId`：YAPI 项目 ID（可选，如未提供则使用环境变量）
- `token`：YAPI 平台认证令牌（可选，如未提供则使用环境变量）
- `baseUrl`：YAPI 平台基础 URL（可选，如未提供则使用环境变量）
- `page`：分页参数，页码（可选，默认为 1）
- `limit`：分页参数，每页项目数（可选，默认为 10）
- `findMostRelevant`：是否只返回最相关的结果（可选，默认为 false）

该工具会在 YAPI 平台中搜索与指定关键词相关的 API，并返回匹配结果和对应的 TypeScript 类型定义。

### manage-interface

管理 YAPI 平台的 API 接口（添加、保存、更新）。

**参数：**
- `action`：操作类型，可选值为 `add`、`save` 或 `update`
- `interfaceData`：接口数据（JSON 字符串）
- `token`：YAPI 平台认证令牌（可选，如未提供则使用环境变量）
- `baseUrl`：YAPI 平台基础 URL（可选，如未提供则使用环境变量）

该工具允许通过 AI 编程助手在 YAPI 平台上管理 API 接口，包括创建新接口、更新现有接口或保存接口变更。

### manage-categories

管理 YAPI 平台的 API 分类（添加、列表）。

**参数：**
- `action`：操作类型，可选值为 `add` 或 `list`
- `projectId`：YAPI 项目 ID（可选，如未提供则使用环境变量）
- `categoryData`：分类数据（JSON 字符串，仅在 `action` 为 `add` 时需要）
- `token`：YAPI 平台认证令牌（可选，如未提供则使用环境变量）
- `baseUrl`：YAPI 平台基础 URL（可选，如未提供则使用环境变量）

该工具允许通过 AI 编程助手在 YAPI 平台上管理 API 分类，包括创建新分类或获取分类列表。

### get-menu-list

获取 YAPI 平台的接口菜单列表。

**参数：**
- `projectId`：YAPI 项目 ID（可选，如未提供则使用环境变量）
- `token`：YAPI 平台认证令牌（可选，如未提供则使用环境变量）
- `baseUrl`：YAPI 平台基础 URL（可选，如未提供则使用环境变量）

该工具允许通过 AI 编程助手获取 YAPI 平台的接口菜单列表，帮助开发者了解 API 的组织结构和分类情况。

### generate-yapi-types

生成 TypeScript 类型和请求函数。

**参数：**
- `projectId`：YAPI 项目 ID（可选，如未提供则使用环境变量）
- `apiId`：特定 API 的 ID（可选，用于生成单个 API 的类型）
- `categoryId`：分类 ID（可选，用于生成分类下所有 API 的类型）
- `keyword`：搜索关键词（可选，用于生成匹配关键词的 API 的类型）
- `token`：YAPI 平台认证令牌（可选，如未提供则使用环境变量）
- `baseUrl`：YAPI 平台基础 URL（可选，如未提供则使用环境变量）
- `genRequest`：是否生成请求函数（可选，默认为 true）
- `outputDir`：输出目录（可选，仅在生成整个项目的类型时需要）
- `outputName`：输出文件名（可选，默认为 api）
- `byCat`：是否按分类组织文件（可选，默认为 false）

该工具使用 @tencent/gencode-yapi2ts 包，可以根据不同的参数组合生成不同范围的 TypeScript 类型和请求函数：
1. 提供 `apiId` 时，生成单个 API 的类型和请求函数
2. 提供 `categoryId` 时，生成分类下所有 API 的类型和请求函数
3. 提供 `keyword` 时，生成匹配关键词的 API 的类型和请求函数
4. 提供 `outputDir` 时，生成整个项目的类型和请求函数到指定目录