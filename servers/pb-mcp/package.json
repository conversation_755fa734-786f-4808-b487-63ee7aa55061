{"name": "@tencent/pb-mcp", "version": "1.0.1", "description": "MCP service for processing Protobuf files and generating TypeScript types", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --fix --ext .ts"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.10.0", "@tencent/gencode-pb-types": "latest", "protobufjs": "^7.2.5", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}, "engines": {"node": ">=20.0.0"}}