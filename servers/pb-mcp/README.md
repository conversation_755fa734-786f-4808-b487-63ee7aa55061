# PB-MCP 服务

PB-MCP 是一个基于 Model Context Protocol (MCP) 的服务，用于从本地 Protobuf 文件中获取 API 信息并生成 TypeScript 类型定义。该服务为 AI 编程助手（如 Cursor、Cline、Tico 和 Augment Code）提供了强大的 Protobuf 文件处理能力。

## 功能描述

PB-MCP 是一个专门用于处理 Protobuf 相关逻辑的 MCP（Model Context Protocol）服务。该服务提供以下主要功能：

- 从本地 Protobuf 文件中读取 API 信息
- 使用 protobuf.js 解析 Protobuf 文件并提取服务定义
- 在 Protobuf 文件中搜索特定关键词相关的 API
- 将 Protobuf 内容转换为 TypeScript 类型定义
- 生成 TypeScript 类型和 Axios 请求代码

PB-MCP 服务通过简单的工具接口，帮助开发者在 AI 编程过程中快速获取和理解 Protobuf 文件中的 API 信息，提高开发效率。

## 配置与启动

### 环境变量

PB-MCP 服务支持以下环境变量：

- `PB_FILE_NAME`：默认的 Protobuf 文件路径，可以是相对路径或绝对路径
- `PB_FILES_DIR`：Protobuf 文件的根目录，默认为 `./pb-files`
- `LOG_LEVEL`：日志级别，默认为 `info`
- `LOG_FILE`：日志文件路径，不设置则只输出到控制台

### 安装依赖

```bash
npm install
```

### 开发环境启动

```bash
npm run dev
```

### 生产环境部署

```bash
npm run build
npm start
```

## AI 编程工具接入方式

### Cursor 接入

```json
{
  "mcpServers": {
    "pb-mcp": {
      "command": "npx",
      "args": ["-y", "@tencent/pb-mcp"],
      "env": {
        "PB_FILES_DIR": "/Users/<USER>/codes/qqnews-platform/servers/pb-mcp/pb-files",
        "PB_FILE_NAME": "pet_management.proto"
      }
    },
  }
}
```

### cline 接入

在 cline 配置文件中添加以下内容：

```json
{
  "mcpServers": {
    "pb-mcp": {
      "command": "npx",
      "args": ["-y", "@tencent/pb-mcp"],
      "env": {
        "PB_FILES_DIR": "/Users/<USER>/codes/qqnews-platform/servers/pb-mcp/pb-files",
        "PB_FILE_NAME": "pet_management.proto"
      }
    },
  }
}
```

### Tico 接入

```json
{
  "mcpServers": {
    "pb-mcp": {
      "command": "npx",
      "args": ["-y", "@tencent/pb-mcp"],
      "env": {
        "PB_FILES_DIR": "/Users/<USER>/codes/qqnews-platform/servers/pb-mcp/pb-files",
        "PB_FILE_NAME": "pet_management.proto"
      }
    },
  }
}
```

### Augment Code 接入

```json
{
  "mcpServers": {
    "pb-mcp": {
      "command": "npx",
      "args": ["-y", "@tencent/pb-mcp"],
      "env": {
        "PB_FILES_DIR": "/Users/<USER>/codes/qqnews-platform/servers/pb-mcp/pb-files",
        "PB_FILE_NAME": "pet_management.proto"
      }
    },
  }
}
```
```

## 使用示例

以下是在各 AI 编程工具中使用 PB-MCP 服务的示例。

### 获取 Protobuf 文件中的 API 信息

```
获取 user.proto 文件中的 API 信息，并生成对应的 TypeScript 类型

mcp.get-api-info(projectId="path/to/user.proto", outputType="both")
```

### 将 Protobuf 内容转换为 TypeScript 类型

```
请将以下 Protobuf 内容转换为 TypeScript 类型：

message User {
  string id = 1;
  string name = 2;
  int32 age = 3;
}

mcp.convert-pb-to-ts(pbContent="message User { string id = 1; string name = 2; int32 age = 3; }")
```

### 在 Protobuf 文件中搜索 API

```
在 user.proto 文件中搜索与 "login" 相关的 API

mcp.search-apis(filePath="path/to/user.proto", keyword="login")
```

这将返回以下信息：

1. Protobuf 文件的基本信息
2. 使用 protobuf.js 解析出的服务定义，包括与关键词匹配的服务和方法
3. 在文件内容中搜索关键词的结果
4. 生成的 TypeScript 类型定义

### 与 AI 助手结合的示例 Prompt

以下是一些与 AI 编程助手结合使用 PB-MCP 服务的示例 Prompt：

**示例 1：查找用户服务相关的 API**

```
请帮我查找 user_service.proto 文件中与用户登录和注册相关的 API。我需要了解这些 API 的请求和响应类型，并生成对应的 TypeScript 类型定义。
```

**示例 2：生成 API 调用代码**

```
我需要实现用户登录功能。请帮我查找 order_service.proto 文件中的登录 API，并生成一个完整的 React 组件，包含表单和 API 调用逻辑。
```

**示例 3：理解服务结构**

```
请帮我分析 product_service.proto 文件中的服务结构。我需要了解该服务提供了哪些方法，每个方法的输入和输出参数是什么，以及它们之间的关系。
```

**示例 4：实现客户端代码**

```
我正在开发一个 React 应用，需要调用后端的 gRPC 服务。请帮我查看 notification_service.proto 文件，并生成一个完整的 TypeScript 客户端库，包含所有必要的类型定义和 API 调用函数。
```

**示例 5：扩展现有服务**

```
我需要在现有的 user_service.proto 文件中添加一个新的方法，用于重置用户密码。请帮我查看现有的服务定义，并提供一个合适的方法定义，包括请求和响应消息类型。
```

## 工具列表

### get-api-info

获取 Protobuf 文件的 API 信息和 TypeScript 类型定义。

**参数：**
- `projectId`：Protobuf 文件路径（相对于 PB_FILES_DIR 或绝对路径）
- `outputType`：输出类型，可选值为 `api`、`ts` 或 `both`，默认为 `both`

### convert-pb-to-ts

将 Protobuf 内容转换为 TypeScript 类型定义。

**参数：**
- `pbContent`：Protobuf 内容
- `importedPbContents`：导入的 Protobuf 内容数组（可选）

### search-apis

在 Protobuf 文件中搜索 API。

**参数：**
- `filePath`：Protobuf 文件路径（相对于 PB_FILES_DIR 或绝对路径）
- `keyword`：搜索关键词

该工具使用 protobuf.js 库解析 Protobuf 文件，提取服务定义和方法，并匹配关键词。返回结果包括文件信息、服务定义、内容搜索结果和 TypeScript 类型。

**示例 Prompt：**

```
请帮我在 user_service.proto 文件中搜索与 "profile" 相关的 API。我需要了解这些 API 的请求和响应类型，以及它们的用途。
```