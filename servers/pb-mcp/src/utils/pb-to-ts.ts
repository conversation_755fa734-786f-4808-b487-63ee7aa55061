import { pb2Types } from '@tencent/gencode-pb-types';
import { logger } from './logger.js';

/**
 * Generates TypeScript types from Protobuf content
 * @param pbContent - The main Protobuf content
 * @param importedPbContents - Array of imported Protobuf contents
 * @returns Promise with generated TypeScript types as string
 */
export async function generateTsFromPb(
  pbContent: string,
  importedPbContents: string[] = [],
): Promise<string> {
  try {
    // Generate TS types directly using the library
    // The library can process the Protobuf content directly
    // Combine main PB content with imported PB content if any
    const combinedPbContent = importedPbContents.length > 0
      ? `${pbContent}\n${importedPbContents.join('\n')}`
      : pbContent;

    const result = await pb2Types({
      pbStr: combinedPbContent,
      useType: false, // Use type aliases instead of interfaces
    });

    // Return the result directly as it's already a string
    return result || '// No types generated from Protobuf';
  } catch (error) {
    logger.error('Error in generateTsFromPb:', error);
    throw new Error(`Failed to generate TypeScript from Protobuf: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
