import protobuf from 'protobufjs';
import { logger } from './logger.js';

/**
 * 表示 Protobuf 服务中的方法
 */
export interface PbMethod {
  /**
   * 方法名称
   */
  name: string;

  /**
   * 请求消息类型
   */
  requestType: string;

  /**
   * 响应消息类型
   */
  responseType: string;

  /**
   * 方法注释（如果有）
   */
  comment?: string;

  /**
   * 是否是客户端流
   */
  requestStream?: boolean;

  /**
   * 是否是服务端流
   */
  responseStream?: boolean;

  /**
   * 方法选项
   */
  options?: Record<string, any>;
}

/**
 * 表示 Protobuf 服务
 */
export interface PbService {
  /**
   * 服务名称
   */
  name: string;

  /**
   * 服务中的方法列表
   */
  methods: PbMethod[];

  /**
   * 服务注释（如果有）
   */
  comment?: string;

  /**
   * 服务选项
   */
  options?: Record<string, any>;
}

/**
 * 表示 Protobuf 消息类型
 */
export interface PbMessage {
  /**
   * 消息名称
   */
  name: string;

  /**
   * 消息字段
   */
  fields: {
    name: string;
    type: string;
    id: number;
    rule?: string;
    options?: Record<string, any>;
  }[];

  /**
   * 嵌套消息
   */
  nestedMessages?: PbMessage[];

  /**
   * 嵌套枚举
   */
  nestedEnums?: PbEnum[];

  /**
   * 消息注释（如果有）
   */
  comment?: string;
}

/**
 * 表示 Protobuf 枚举类型
 */
export interface PbEnum {
  /**
   * 枚举名称
   */
  name: string;

  /**
   * 枚举值
   */
  values: {
    name: string;
    id: number;
    options?: Record<string, any>;
  }[];

  /**
   * 枚举注释（如果有）
   */
  comment?: string;
}

/**
 * 表示解析后的 Protobuf 文件
 */
export interface ParsedProto {
  /**
   * 包名
   */
  package?: string;

  /**
   * 服务列表
   */
  services: PbService[];

  /**
   * 消息列表
   */
  messages: PbMessage[];

  /**
   * 枚举列表
   */
  enums: PbEnum[];

  /**
   * 导入列表
   */
  imports: string[];

  /**
   * 选项
   */
  options: Record<string, any>;
}

/**
 * 解析 Protobuf 文件
 * @param filePath - Protobuf 文件路径
 * @returns 解析后的 Protobuf 结构
 */
export async function parseProtoContent(filePath: string): Promise<ParsedProto> {
  try {
    logger.info('Parsing Protobuf content');

    // 使用 protobufjs 解析内容
    const root = await protobuf.load(filePath);

    // 初始化结果对象
    const result: ParsedProto = {
      services: [],
      messages: [],
      enums: [],
      imports: [],
      options: {},
    };

    // 从文件读取内容以提取包名和导入
    const fs = await import('fs');
    const content = fs.readFileSync(filePath, 'utf-8');

    // 提取包名
    const packageMatch = content.match(/package\s+([\w.]+)\s*;/);
    if (packageMatch?.[1]) {
      result.package = packageMatch[1];
    }

    // 提取导入
    const importMatches = content.matchAll(/import\s+"([^"]+)"\s*;/g);
    if (importMatches) {
      for (const match of importMatches) {
        if (match[1]) {
          result.imports.push(match[1]);
        }
      }
    }

    // 提取选项
    if (root.options) {
      result.options = root.options;
    }

    // 递归处理所有命名空间和嵌套类型
    processNamespace(root, result);

    logger.info(`Parsed Protobuf content: found ${result.services.length} services, ${result.messages.length} messages, ${result.enums.length} enums`);
    return result;
  } catch (error) {
    logger.error('Error parsing Protobuf content:', error);
    // 返回一个空的结果
    return {
      services: [],
      messages: [],
      enums: [],
      imports: [],
      options: {},
    };
  }
}

/**
 * 递归处理命名空间和嵌套类型
 * @param namespace - 当前命名空间
 * @param result - 结果对象
 * @param prefix - 类型名称前缀
 */
function processNamespace(namespace: any, result: ParsedProto, prefix = ''): void {
  // 处理服务
  if (namespace.nested) {
    Object.keys(namespace.nested).forEach((key) => {
      const nestedType = namespace.nested[key];
      const fullName = prefix ? `${prefix}.${key}` : key;
      // 处理服务
      if (nestedType.methods) {
        const service: PbService = {
          name: fullName,
          methods: [],
          comment: nestedType.comment,
        };
        // 处理服务选项
        if (nestedType.options) {
          service.options = nestedType.options;
        }
        // 处理服务方法
        Object.keys(nestedType.methods).forEach((methodName) => {
          const method = nestedType.methods[methodName];
          const pbMethod: PbMethod = {
            name: methodName,
            requestType: method.requestType,
            responseType: method.responseType,
            comment: method.comment,
          };

          // 处理流类型
          if (method.requestStream) {
            pbMethod.requestStream = true;
          }

          if (method.responseStream) {
            pbMethod.responseStream = true;
          }

          // 处理方法选项
          if (method.options) {
            pbMethod.options = method.options;
          }

          service.methods.push(pbMethod);
        });
        result.services.push(service);
      } else if (nestedType.fields) {
        // 处理消息
        const message = processMessage(key, nestedType, fullName);
        result.messages.push(message);
      } else if (nestedType.values) {
        const pbEnum = processEnum(key, nestedType);
        result.enums.push(pbEnum);
      } else if (nestedType.nested) {
        processNamespace(nestedType, result, fullName);
      }
    });
  }
}

/**
 * 处理消息类型
 * @param _name - 消息名称
 * @param message - 消息对象
 * @param fullName - 完整名称
 * @returns 处理后的消息对象
 */
function processMessage(_name: string, message: any, fullName: string): PbMessage {
  const pbMessage: PbMessage = {
    name: fullName,
    fields: [],
    comment: message.comment,
  };

  // 处理字段
  if (message.fields) {
    Object.keys(message.fields).forEach((fieldName) => {
      const field = message.fields[fieldName];
      pbMessage.fields.push({
        name: fieldName,
        type: field.type,
        id: field.id,
        rule: field.rule,
        options: field.options,
      });
    });
  }

  // 处理嵌套类型
  if (message.nested) {
    pbMessage.nestedMessages = [];
    pbMessage.nestedEnums = [];

    Object.keys(message.nested).forEach((nestedName) => {
      const nestedType = message.nested[nestedName];
      const nestedFullName = `${fullName}.${nestedName}`;

      if (nestedType.fields) {
        const nestedMessage = processMessage(nestedName, nestedType, nestedFullName);
        pbMessage.nestedMessages!.push(nestedMessage);
      } else if (nestedType.values) {
        const nestedEnum = processEnum(nestedName, nestedType);
        pbMessage.nestedEnums!.push(nestedEnum);
      }
    });
  }

  return pbMessage;
}

/**
 * 处理枚举类型
 * @param name - 枚举名称
 * @param enumObj - 枚举对象
 * @returns 处理后的枚举对象
 */
function processEnum(name: string, enumObj: any): PbEnum {
  const pbEnum: PbEnum = {
    name,
    values: [],
    comment: enumObj.comment,
  };

  // 处理枚举值
  if (enumObj.values) {
    Object.keys(enumObj.values).forEach((valueName) => {
      pbEnum.values.push({
        name: valueName,
        id: enumObj.values[valueName],
        options: enumObj.options,
      });
    });
  }

  return pbEnum;
}

/**
 * 搜索 Protobuf 内容中与关键词匹配的服务和方法
 * @param parsedProto - 解析后的 Protobuf 结构
 * @param keyword - 搜索关键词
 * @returns 匹配的服务和方法
 */
export function searchProtoServices(parsedProto: ParsedProto, keyword: string): {
  matchedServices: PbService[];
  matchedMethods: { service: string; method: PbMethod }[];
} {
  const matchedServices: PbService[] = [];
  const matchedMethods: { service: string; method: PbMethod }[] = [];
  const lowercaseKeyword = keyword.toLowerCase();

  // 搜索服务
  parsedProto.services.forEach((service) => {
    let serviceMatched = false;

    // 检查服务名称是否匹配
    if (service.name.toLowerCase().includes(lowercaseKeyword)) {
      serviceMatched = true;
      matchedServices.push(service);
    }

    // 检查服务方法是否匹配
    service.methods.forEach((method) => {
      if (
        method.name.toLowerCase().includes(lowercaseKeyword)
        || (method.comment?.toLowerCase().includes(lowercaseKeyword))
      ) {
        matchedMethods.push({ service: service.name, method });

        // 如果服务还没有被添加到匹配列表中，添加它
        if (!serviceMatched) {
          serviceMatched = true;
          matchedServices.push(service);
        }
      }
    });
  });

  return { matchedServices, matchedMethods };
}
