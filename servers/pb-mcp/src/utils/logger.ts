import winston from 'winston';

/**
 * Logger configuration for the application
 */
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.colorize(),
    winston.format.printf(({ timestamp, level, message }) => `${timestamp} [${level}]: ${message}`),
  ),
  transports: [
    new winston.transports.Console({
      stderrLevels: ['error', 'warn'],
    }),
  ],
});

// Add a transport to write logs to a file
if (process.env.LOG_FILE) {
  logger.add(new winston.transports.File({ filename: process.env.LOG_FILE }));
}

export { logger };
