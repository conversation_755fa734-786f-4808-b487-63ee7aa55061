import fs from 'fs/promises';
import path from 'path';
import { PbApiResponse, PbFileInfo } from '../types/pb.js';
import { generateTsFromPb } from '../utils/pb-to-ts.js';
import { logger } from '../utils/logger.js';
import { parseProtoContent, searchProtoServices, ParsedProto } from '../utils/pb-parser.js';

/**
 * Service for interacting with Protobuf files
 */
export class PbService {
  // 本地 Protobuf 文件目录
  private pbFilesDir = process.env.PB_FILES_DIR || './pb-files';

  /**
   * Searches for APIs by keyword in local Protobuf files
   * @param filePath - The path to the Protobuf file
   * @param keyword - The keyword to search for
   * @returns Promise with the PB file information and parsed services
   */
  async searchApis(filePath: string, keyword: string): Promise<{ fileInfo: PbFileInfo; parsedProto: ParsedProto; searchResults: { matchedServices: any[]; matchedMethods: any[] } }> {
    try {
      // Read the Protobuf file
      const response = await this.fetchPbInfo(filePath);

      if (response.code !== 0) {
        logger.error(`Error reading Protobuf file: ${response.message}`);
        throw new Error(`Error reading Protobuf file: ${response.message}`);
      }

      // Add search information to the log
      logger.info(`Search results for "${keyword}" in Protobuf file: ${filePath}`);

      // Determine the full path to the Protobuf file
      const fullPath = path.isAbsolute(filePath) ? filePath : path.join(this.pbFilesDir, filePath);

      // Parse the Protobuf file to extract services
      const parsedProto = await parseProtoContent(fullPath);

      // Search for services and methods matching the keyword
      const searchResults = searchProtoServices(parsedProto, keyword);

      logger.info(`Found ${searchResults.matchedServices.length} services and ${searchResults.matchedMethods.length} methods matching "${keyword}"`);

      // Return the file info, parsed proto, and search results
      return {
        fileInfo: response.data,
        parsedProto,
        searchResults,
      };
    } catch (error) {
      logger.error(`Error searching Protobuf file with keyword "${keyword}":`, error);
      throw new Error(`Failed to search Protobuf file with keyword "${keyword}": ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Fetches PB information from local file
   * @param filePath - The path to the Protobuf file (relative to pbFilesDir or absolute)
   * @returns Promise with the PB file information
   */
  async fetchPbInfo(filePath: string): Promise<PbApiResponse> {
    try {
      // Determine if the path is absolute or relative
      const fullPath = path.isAbsolute(filePath) ? filePath : path.join(this.pbFilesDir, filePath);
      logger.info(`Reading Protobuf file from: ${fullPath}`);

      // Read the file content
      const pbContent = await fs.readFile(fullPath, 'utf-8');
      logger.info(`Read ${pbContent.length} bytes from file`);

      // Get file name without extension
      const fileName = path.basename(filePath, path.extname(filePath));

      // Create a simplified PB file info object
      const pbFileInfo: PbFileInfo = {
        name: fileName,
        filePath,
        content: pbContent,
        imports: [], // 默认为空数组
      };

      // Create a response object with the simplified structure
      const response: PbApiResponse = {
        code: 0,
        message: 'Success',
        data: pbFileInfo,
      };

      return response;
    } catch (error) {
      logger.error(`Error reading Protobuf file: ${filePath}`, error);

      // Return error response
      return {
        code: 1,
        message: `Failed to read Protobuf file: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: {
          name: path.basename(filePath, path.extname(filePath)),
          filePath,
          content: '',
        },
      };
    }
  }

  /**
   * Generates TypeScript types from PB content
   * @param response - The Protobuf API response
   * @returns Generated TypeScript types as string
   */
  async generateTsTypes(response: PbApiResponse): Promise<string> {
    try {
      logger.info('Generating TypeScript types from Protobuf response');

      // Check if the PB content is available
      if (!response.data?.content) {
        logger.error('PB content is empty');
        return '// Error: PB content is empty\nexport type EmptyPbType = any;';
      }

      const pbContent = response.data.content;
      logger.info(`PB content length: ${pbContent.length} characters`);

      // Generate imports for imported PBs if needed
      let importedPbContents: string[] = [];
      if (response.data.imports && Array.isArray(response.data.imports)) {
        importedPbContents = response.data.imports
          .filter(imp => imp?.content) // Filter out empty imports
          .map(imp => imp.content);

        logger.info(`Found ${importedPbContents.length} imported PB contents`);
      } else {
        logger.info('No imported PB contents found');
      }

      logger.info('Calling generateTsFromPb');
      return await generateTsFromPb(pbContent, importedPbContents);
    } catch (error) {
      logger.error('Error generating TS types from PB:', error);
      throw new Error(`Failed to generate TypeScript types from Protobuf: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
