import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { z } from 'zod';
import { logger } from './utils/logger.js';
import { PbService } from './services/pb-service.js';
import { generateTsFromPb } from './utils/pb-to-ts.js';

// Initialize services
const pbService = new PbService();

// Get configuration from environment variables
const envPbFileName = process.env.PB_FILE_NAME;
const envPbFilesDir = process.env.PB_FILES_DIR || './pb-files';

// Log configuration status
if (envPbFileName) {
  logger.info(`Protobuf file name found in environment variables: ${envPbFileName} (will be used as default)`);
} else {
  logger.info('No Protobuf file name found in environment variables');
}

if (envPbFilesDir) {
  logger.info(`Protobuf files directory found in environment variables: ${envPbFilesDir}`);
} else {
  logger.info('Using default Protobuf files directory: ./pb-files');
}

// Create an MCP server
const server = new McpServer({
  name: 'PB-MCP',
  version: '1.0.0',
  capabilities: {
    resources: {},
    tools: {},
  },
});

// Add a tool to get API information and generate TypeScript types
server.tool(
  'get-api-info',
  'Get API information from local Protobuf files',
  {
    projectId: z.string().optional()
      .describe('The path to the Protobuf file (relative to PB_FILES_DIR or absolute).'),
    outputType: z.enum(['api', 'ts', 'both']).default('both')
      .describe('The type of output to return (api, ts, or both)'),
  },
  async ({ projectId: inputProjectId, outputType }: {
    projectId?: string;
    outputType: 'api' | 'ts' | 'both'
  }) => {
    // Determine the project ID to use (priority: user-provided > environment variable)
    let projectId = inputProjectId;
    if (!projectId) {
      if (envPbFileName) {
        projectId = envPbFileName;
      } else {
        return {
          content: [{
            type: 'text',
            text: 'No project ID specified. Please provide a project ID using one of these methods:\n1. Set PB_FILE_NAME environment variable in MCP server configuration\n2. Provide projectId parameter in your request',
          }],
          isError: true,
        };
      }
    }
    logger.info(`Processing API info request for projectId: ${projectId}`);

    let apiInfo;
    let tsTypes;

    try {
      // Process request
      const response = await pbService.fetchPbInfo(projectId);

      if (response.code !== 0) {
        return {
          content: [{ type: 'text', text: `Protobuf API error: ${response.message}` }],
          isError: true,
        };
      }

      if (outputType === 'api' || outputType === 'both') {
        apiInfo = response.data; // 直接使用原始数据
      }

      if (outputType === 'ts' || outputType === 'both') {
        tsTypes = await pbService.generateTsTypes(response);
      }

      // Format the response
      let resultText = '';

      if (apiInfo) {
        resultText += '## Protobuf File Information\n\n';
        resultText += `**File Name:** ${apiInfo.name}\n`;
        resultText += `**File Path:** ${apiInfo.filePath}\n`;
        resultText += `**Content Length:** ${apiInfo.content.length} characters\n`;

        if (apiInfo.imports && apiInfo.imports.length > 0) {
          resultText += `**Imported Files:** ${apiInfo.imports.length}\n`;
          resultText += '\n### Imported Files\n\n';
          apiInfo.imports.forEach((imp, index) => {
            resultText += `${index + 1}. ${imp.name} (${imp.filePath})\n`;
          });
        }

        resultText += '\n### Protobuf Content Preview\n\n';
        // 只显示前 500 个字符作为预览
        const contentPreview = apiInfo.content.length > 500
          ? `${apiInfo.content.substring(0, 500)}...`
          : apiInfo.content;
        resultText += `\`\`\`protobuf\n${contentPreview}\n\`\`\`\n`;
      }

      if (tsTypes) {
        resultText += '\n## TypeScript Types\n\n';
        resultText += `\`\`\`typescript\n${tsTypes}\n\`\`\`\n`;
      }

      return {
        content: [{ type: 'text', text: resultText }],
      };
    } catch (error) {
      logger.error('Error processing API info request:', error);
      return {
        content: [{ type: 'text', text: `Error: ${error instanceof Error ? error.message : 'Unknown error'}` }],
        isError: true,
      };
    }
  },
);

// Add a tool to convert PB to TypeScript
server.tool(
  'convert-pb-to-ts',
  'Convert Protobuf content to TypeScript types',
  {
    pbContent: z.string().describe('The Protobuf content to convert'),
    importedPbContents: z.array(z.string()).optional()
      .describe('Optional array of imported Protobuf contents'),
  },
  async ({ pbContent, importedPbContents = [] }: { pbContent: string; importedPbContents?: string[] }) => {
    try {
      const tsTypes = await generateTsFromPb(pbContent, importedPbContents);

      return {
        content: [{ type: 'text', text: tsTypes }],
      };
    } catch (error) {
      logger.error('Error converting PB to TS:', error);
      return {
        content: [{ type: 'text', text: `Error: ${error instanceof Error ? error.message : 'Unknown error'}` }],
        isError: true,
      };
    }
  },
);

// Add a tool to search Rick APIs by keyword
server.tool(
  'search-apis',
  'Search for APIs in local Protobuf files by keyword',
  {
    filePath: z.string().optional()
      .describe('The path to the Protobuf file (relative to PB_FILES_DIR or absolute, can be set via PB_FILE_NAME environment variable)'),
    keyword: z.string().describe('The keyword to search for'),
  },
  async ({ filePath: inputFilePath, keyword }: {
    filePath?: string;
    keyword: string;
  }) => {
    // Determine the file path to use (priority: user-provided > environment variable)
    const filePath = inputFilePath || envPbFileName;
    if (!filePath) {
      return {
        content: [{
          type: 'text',
          text: 'No Protobuf file path specified. Please provide a file path using one of these methods:\n1. Set PB_FILE_NAME environment variable in MCP server configuration\n2. Provide filePath parameter in your request',
        }],
        isError: true,
      };
    }
    logger.info(`Searching Protobuf file: ${filePath}, keyword: "${keyword}"`);

    try {
      // Search for APIs matching the keyword
      const { fileInfo, parsedProto, searchResults } = await pbService.searchApis(filePath, keyword);

      // Format the response
      let resultText = `# Search for "${keyword}" in Protobuf file

`;
      resultText += `## Protobuf File Information

`;
      resultText += `**File Name:** ${fileInfo.name}\n`;
      resultText += `**File Path:** ${fileInfo.filePath}\n`;
      resultText += `**Content Length:** ${fileInfo.content.length} characters\n`;

      if (fileInfo.imports && fileInfo.imports.length > 0) {
        resultText += `**Imported Files:** ${fileInfo.imports.length}\n`;
        resultText += '\n### Imported Files\n\n';
        fileInfo.imports.forEach((imp, index) => {
          resultText += `${index + 1}. ${imp.name} (${imp.filePath})\n`;
        });
      }

      // 搜索关键词在内容中的位置
      const contentLines = fileInfo.content.split('\n');
      const matchingLines: Array<{ lineNumber: number; content: string }> = [];

      contentLines.forEach((line: string, index: number) => {
        if (line.toLowerCase().includes(keyword.toLowerCase())) {
          matchingLines.push({ lineNumber: index + 1, content: line });
        }
      });

      // 使用搜索结果
      const { matchedServices, matchedMethods } = searchResults;

      // 添加服务信息部分
      resultText += '\n## Protobuf Services\n\n';

      if (parsedProto.services.length === 0) {
        resultText += 'No services found in this Protobuf file.\n';
      } else if (matchedServices.length === 0) {
        resultText += `Found ${parsedProto.services.length} services, but none match the keyword "${keyword}".\n`;

        // 显示所有服务的概要
        resultText += '\n### All Services\n\n';
        parsedProto.services.forEach((service, index) => {
          resultText += `${index + 1}. **${service.name}** (${service.methods.length} methods)\n`;
        });
      } else {
        resultText += `Found ${matchedServices.length} services matching "${keyword}":\n\n`;

        // 详细显示匹配的服务
        matchedServices.forEach((service, index) => {
          resultText += `### ${index + 1}. ${service.name}\n\n`;

          if (service.comment) {
            resultText += `**Description:** ${service.comment}\n\n`;
          }

          resultText += '**Methods:**\n\n';

          service.methods.forEach((method: any) => {
            const isMatched = matchedMethods.some(m => m.service === service.name && m.method.name === method.name);

            const methodPrefix = isMatched ? '* ' : '  ';
            resultText += `${methodPrefix}**${method.name}**`;

            if (method.requestStream || method.responseStream) {
              const streamInfo = [];
              if (method.requestStream) streamInfo.push('client streaming');
              if (method.responseStream) streamInfo.push('server streaming');
              resultText += ` (${streamInfo.join(', ')})`;
            }

            resultText += `\n    - Request: ${method.requestType}\n    - Response: ${method.responseType}\n`;

            if (method.comment) {
              resultText += `    - Description: ${method.comment}\n`;
            }

            resultText += '\n';
          });
        });
      }

      // 显示内容搜索结果
      resultText += `\n## Content Search Results for "${keyword}"\n\n`;

      if (matchingLines.length === 0) {
        resultText += `No matches found for "${keyword}" in file content.\n`;
      } else {
        resultText += `Found ${matchingLines.length} matching lines:\n\n`;
        matchingLines.forEach((match) => {
          resultText += `Line ${match.lineNumber}: ${match.content.trim()}\n`;
        });

        // 显示匹配行的上下文
        resultText += '\n### Context of First Match\n\n';
        const firstMatch = matchingLines[0];
        const startLine = Math.max(1, firstMatch.lineNumber - 5);
        const endLine = Math.min(contentLines.length, firstMatch.lineNumber + 5);

        resultText += '```protobuf\n';
        for (let i = startLine - 1; i < endLine; i++) {
          const linePrefix = i + 1 === firstMatch.lineNumber ? '> ' : '  ';
          resultText += `${linePrefix}${i + 1}: ${contentLines[i]}\n`;
        }
        resultText += '```\n';
      }

      // Generate TypeScript types
      const response = await pbService.fetchPbInfo(filePath);
      const tsTypes = await pbService.generateTsTypes(response);

      // 如果有匹配的行，尝试提取相关的类型
      if (matchingLines.length > 0 && tsTypes) {
        resultText += '\n### Related TypeScript Types\n\n';
        const tsLines = tsTypes.split('\n');
        const relatedTypes: string[] = [];

        // 尝试匹配关键词相关的 TS 类型
        for (let i = 0; i < tsLines.length; i++) {
          if (tsLines[i].toLowerCase().includes(keyword.toLowerCase())) {
            // 找到类型定义的开始
            let start = i;
            while (start > 0 && !tsLines[start].includes('export ')) {
              start -= 1;
            }

            // 找到类型定义的结束
            let end = i;
            while (end < tsLines.length && !tsLines[end].includes('}') && !tsLines[end].includes(';')) {
              end += 1;
            }

            // 收集相关类型
            const typeDefinition = tsLines.slice(start, end + 1).join('\n');
            if (!relatedTypes.includes(typeDefinition)) {
              relatedTypes.push(typeDefinition);
            }
          }
        }

        if (relatedTypes.length > 0) {
          resultText += '```typescript\n';
          resultText += relatedTypes.join('\n\n');
          resultText += '\n```\n';
        } else {
          resultText += `No TypeScript types directly related to "${keyword}" found.\n`;
        }
      }

      resultText += '\n## TypeScript Types\n\n';
      resultText += `\`\`\`typescript\n${tsTypes}\n\`\`\`\n`;

      return {
        content: [{ type: 'text', text: resultText }],
      };
    } catch (error) {
      logger.error('Error searching Protobuf APIs:', error);
      return {
        content: [{ type: 'text', text: `Error: ${error instanceof Error ? error.message : 'Unknown error'}` }],
        isError: true,
      };
    }
  },
);

// Start the MCP server with stdio transport
async function main() {
  try {
    logger.info('Starting PB-MCP server...');

    // Create a transport using standard IO for server communication
    const transport = new StdioServerTransport();

    // Connect the server to the transport
    await server.connect(transport);

    logger.info('PB-MCP server started successfully');
  } catch (error) {
    logger.error('Failed to start PB-MCP server:', error);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// Run the server
main();
