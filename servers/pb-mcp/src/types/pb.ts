/**
 * Types for Protobuf API
 */

/**
 * 简化的 PB 文件信息
 */
export interface PbFileInfo {
  /**
   * 文件名（不含扩展名）
   */
  name: string;

  /**
   * 文件路径
   */
  filePath: string;

  /**
   * 文件内容
   */
  content: string;

  /**
   * 导入的其他 PB 文件信息
   */
  imports?: PbFileInfo[];
}

/**
 * PB API 响应
 */
export interface PbApiResponse {
  /**
   * 状态码，0 表示成功
   */
  code: number;

  /**
   * 消息
   */
  message: string;

  /**
   * PB 文件信息
   */
  data: PbFileInfo;
}
