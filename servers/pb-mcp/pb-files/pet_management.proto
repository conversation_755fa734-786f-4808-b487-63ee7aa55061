syntax = "proto3";

package pet_management;

option go_package = "github.com/example/pet_management";
option java_package = "com.example.pet_management";
option java_multiple_files = true;

// 导入通用定义
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

// 服务定义
service PetManagementService {
  // 宠物相关接口
  rpc CreatePet (CreatePetRequest) returns (Pet);
  rpc GetPet (GetPetRequest) returns (Pet);
  rpc UpdatePet (UpdatePetRequest) returns (Pet);
  rpc DeletePet (DeletePetRequest) returns (google.protobuf.Empty);
  rpc ListPets (ListPetsRequest) returns (ListPetsResponse);
  rpc SearchPets (SearchPetsRequest) returns (ListPetsResponse);

  // 主人相关接口
  rpc CreateOwner (CreateOwnerRequest) returns (Owner);
  rpc GetOwner (GetOwnerRequest) returns (Owner);
  rpc UpdateOwner (UpdateOwnerRequest) returns (Owner);
  rpc DeleteOwner (DeleteOwnerRequest) returns (google.protobuf.Empty);
  rpc ListOwners (ListOwnersRequest) returns (ListOwnersResponse);

  // 预约相关接口
  rpc CreateAppointment (CreateAppointmentRequest) returns (Appointment);
  rpc GetAppointment (GetAppointmentRequest) returns (Appointment);
  rpc UpdateAppointment (UpdateAppointmentRequest) returns (Appointment);
  rpc CancelAppointment (CancelAppointmentRequest) returns (google.protobuf.Empty);
  rpc ListAppointments (ListAppointmentsRequest) returns (ListAppointmentsResponse);
  rpc GetUpcomingAppointments (GetUpcomingAppointmentsRequest) returns (ListAppointmentsResponse);
}

// 宠物相关消息定义
enum PetType {
  PET_TYPE_UNSPECIFIED = 0;
  PET_TYPE_DOG = 1;
  PET_TYPE_CAT = 2;
  PET_TYPE_BIRD = 3;
  PET_TYPE_REPTILE = 4;
  PET_TYPE_FISH = 5;
  PET_TYPE_OTHER = 6;
}

enum Gender {
  GENDER_UNSPECIFIED = 0;
  GENDER_MALE = 1;
  GENDER_FEMALE = 2;
  GENDER_UNKNOWN = 3;
}

message Pet {
  string id = 1;
  string name = 2;
  PetType type = 3;
  string breed = 4;
  int32 age = 5;
  float weight = 6;
  Gender gender = 7;
  string owner_id = 8;
  bool is_vaccinated = 9;
  string medical_history = 10;
  string notes = 11;
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Timestamp updated_at = 13;
}

message CreatePetRequest {
  string name = 1;
  PetType type = 2;
  string breed = 3;
  int32 age = 4;
  float weight = 5;
  Gender gender = 6;
  string owner_id = 7;
  bool is_vaccinated = 8;
  string medical_history = 9;
  string notes = 10;
}

message GetPetRequest {
  string id = 1;
}

message UpdatePetRequest {
  string id = 1;
  string name = 2;
  PetType type = 3;
  string breed = 4;
  int32 age = 5;
  float weight = 6;
  Gender gender = 7;
  string owner_id = 8;
  bool is_vaccinated = 9;
  string medical_history = 10;
  string notes = 11;
}

message DeletePetRequest {
  string id = 1;
}

message ListPetsRequest {
  int32 page_size = 1;
  string page_token = 2;
  string owner_id = 3;
}

message ListPetsResponse {
  repeated Pet pets = 1;
  string next_page_token = 2;
  int32 total_size = 3;
}

message SearchPetsRequest {
  string query = 1;
  PetType type = 2;
  string breed = 3;
  bool is_vaccinated = 4;
  int32 page_size = 5;
  string page_token = 6;
}

// 主人相关消息定义
message Owner {
  string id = 1;
  string name = 2;
  string email = 3;
  string phone = 4;
  string address = 5;
  repeated string pet_ids = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
}

message CreateOwnerRequest {
  string name = 1;
  string email = 2;
  string phone = 3;
  string address = 4;
}

message GetOwnerRequest {
  string id = 1;
}

message UpdateOwnerRequest {
  string id = 1;
  string name = 2;
  string email = 3;
  string phone = 4;
  string address = 5;
}

message DeleteOwnerRequest {
  string id = 1;
}

message ListOwnersRequest {
  int32 page_size = 1;
  string page_token = 2;
}

message ListOwnersResponse {
  repeated Owner owners = 1;
  string next_page_token = 2;
  int32 total_size = 3;
}

// 预约相关消息定义
enum AppointmentType {
  APPOINTMENT_TYPE_UNSPECIFIED = 0;
  APPOINTMENT_TYPE_CHECKUP = 1;
  APPOINTMENT_TYPE_VACCINATION = 2;
  APPOINTMENT_TYPE_GROOMING = 3;
  APPOINTMENT_TYPE_SURGERY = 4;
  APPOINTMENT_TYPE_DENTAL = 5;
  APPOINTMENT_TYPE_OTHER = 6;
}

enum AppointmentStatus {
  APPOINTMENT_STATUS_UNSPECIFIED = 0;
  APPOINTMENT_STATUS_SCHEDULED = 1;
  APPOINTMENT_STATUS_CONFIRMED = 2;
  APPOINTMENT_STATUS_IN_PROGRESS = 3;
  APPOINTMENT_STATUS_COMPLETED = 4;
  APPOINTMENT_STATUS_CANCELLED = 5;
  APPOINTMENT_STATUS_NO_SHOW = 6;
}

message Appointment {
  string id = 1;
  string pet_id = 2;
  string owner_id = 3;
  google.protobuf.Timestamp appointment_time = 4;
  int32 duration_minutes = 5;
  AppointmentType type = 6;
  AppointmentStatus status = 7;
  string veterinarian = 8;
  string notes = 9;
  google.protobuf.Timestamp created_at = 10;
  google.protobuf.Timestamp updated_at = 11;
}

message CreateAppointmentRequest {
  string pet_id = 1;
  string owner_id = 2;
  google.protobuf.Timestamp appointment_time = 3;
  int32 duration_minutes = 4;
  AppointmentType type = 5;
  string veterinarian = 6;
  string notes = 7;
}

message GetAppointmentRequest {
  string id = 1;
}

message UpdateAppointmentRequest {
  string id = 1;
  google.protobuf.Timestamp appointment_time = 2;
  int32 duration_minutes = 3;
  AppointmentType type = 4;
  AppointmentStatus status = 5;
  string veterinarian = 6;
  string notes = 7;
}

message CancelAppointmentRequest {
  string id = 1;
  string reason = 2;
}

message ListAppointmentsRequest {
  string pet_id = 1;
  string owner_id = 2;
  AppointmentStatus status = 3;
  int32 page_size = 4;
  string page_token = 5;
}

message ListAppointmentsResponse {
  repeated Appointment appointments = 1;
  string next_page_token = 2;
  int32 total_size = 3;
}

message GetUpcomingAppointmentsRequest {
  string pet_id = 1;
  string owner_id = 2;
  int32 days_ahead = 3;
  int32 page_size = 4;
  string page_token = 5;
} 