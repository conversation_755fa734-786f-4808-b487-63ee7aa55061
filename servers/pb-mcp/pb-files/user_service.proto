syntax = "proto3";

package user;

// 用户服务定义
service UserService {
  // 用户登录
  rpc Login (LoginRequest) returns (LoginResponse);
  
  // 用户注册
  rpc Register (RegisterRequest) returns (RegisterResponse);
  
  // 获取用户信息
  rpc GetUserInfo (GetUserInfoRequest) returns (GetUserInfoResponse);
  
  // 更新用户信息
  rpc UpdateUserInfo (UpdateUserInfoRequest) returns (UpdateUserInfoResponse);
  
  // 用户列表流
  rpc ListUsers (ListUsersRequest) returns (stream UserInfo);
}

// 登录请求
message LoginRequest {
  string username = 1;
  string password = 2;
}

// 登录响应
message LoginResponse {
  bool success = 1;
  string token = 2;
  string error_message = 3;
  UserInfo user_info = 4;
}

// 注册请求
message RegisterRequest {
  string username = 1;
  string password = 2;
  string email = 3;
  string phone = 4;
}

// 注册响应
message RegisterResponse {
  bool success = 1;
  string user_id = 2;
  string error_message = 3;
}

// 获取用户信息请求
message GetUserInfoRequest {
  string user_id = 1;
}

// 获取用户信息响应
message GetUserInfoResponse {
  bool success = 1;
  UserInfo user_info = 2;
  string error_message = 3;
}

// 更新用户信息请求
message UpdateUserInfoRequest {
  string user_id = 1;
  UserInfo user_info = 2;
}

// 更新用户信息响应
message UpdateUserInfoResponse {
  bool success = 1;
  string error_message = 2;
}

// 用户列表请求
message ListUsersRequest {
  int32 page = 1;
  int32 page_size = 2;
  string filter = 3;
}

// 用户信息
message UserInfo {
  string user_id = 1;
  string username = 2;
  string email = 3;
  string phone = 4;
  UserStatus status = 5;
  repeated string roles = 6;
  UserProfile profile = 7;
  int64 created_at = 8;
  int64 updated_at = 9;
}

// 用户状态枚举
enum UserStatus {
  UNKNOWN = 0;
  ACTIVE = 1;
  INACTIVE = 2;
  SUSPENDED = 3;
  DELETED = 4;
}

// 用户个人资料
message UserProfile {
  string nickname = 1;
  string avatar_url = 2;
  string bio = 3;
  Gender gender = 4;
  int32 age = 5;
  
  // 用户性别枚举
  enum Gender {
    UNSPECIFIED = 0;
    MALE = 1;
    FEMALE = 2;
    OTHER = 3;
  }
}
