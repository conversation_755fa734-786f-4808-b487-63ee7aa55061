import path from 'path';
import { fileURLToPath } from 'url';
import { parseProtoContent, searchProtoServices } from './dist/utils/pb-parser.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function testSearch() {
  try {
    // 读取 Protobuf 文件
    const filePath = path.join(__dirname, 'pb-files', 'user_service.proto');

    console.log('Parsing Protobuf file:', filePath);
    const parsedProto = await parseProtoContent(filePath);

    console.log('Searching for "login"...');
    const { matchedServices, matchedMethods } = searchProtoServices(parsedProto, 'login');

    console.log('\nMatched Services:', matchedServices.length);
    matchedServices.forEach((service) => {
      console.log(`- ${service.name}`);
    });

    console.log('\nMatched Methods:', matchedMethods.length);
    matchedMethods.forEach((method) => {
      console.log(`- ${method.service}.${method.method.name}`);
      console.log(`  Request: ${method.method.requestType}`);
      console.log(`  Response: ${method.method.responseType}`);
    });

    console.log('\nAll Services:', parsedProto.services.length);
    parsedProto.services.forEach((service) => {
      console.log(`- ${service.name} (${service.methods.length} methods)`);
      service.methods.forEach((method) => {
        console.log(`  - ${method.name}`);
      });
    });
  } catch (error) {
    console.error('Error:', error);
  }
}

testSearch();
