# Render Server

基于 Node.js + QnNode + TypeScript 的服务端渲染项目。

## 技术栈

- 🚀 Node.js 18+
- 🌐 Koa 框架
- 🔧 TypeScript
- 📝 EJS 模板引擎
- 📊 Winston 日志系统
- 🔄 Node-cron 定时任务
- 🔐 Jose JWT 认证

## 特性

- ✨ TypeScript 全面支持
- 📱 服务端渲染
- 🔒 完整的错误处理
- 📊 详细的日志记录
- ⚡ 高性能
- 🔄 自动化的定时任务

## 快速开始

### 环境要求

- Node.js 18.12.1+
- pnpm 8.6.8+

### 安装依赖

```bash
pnpm install
```

### 开发

```bash
pnpm dev
```

### 构建

```bash
pnpm build
```

### 启动服务

```bash
pnpm start
```

## 项目结构

```
src/
├── config/          # 配置文件
├── controllers/     # 控制器
├── middlewares/    # 中间件
├── models/         # 数据模型
├── routes/         # 路由配置
├── services/       # 业务服务
├── types/          # 类型定义
├── utils/          # 工具函数
└── views/          # 视图模板
```

## 开发规范

- 遵循 ESLint 配置的代码规范
- 使用 TypeScript 强类型开发
- 遵循 RESTful API 设计规范
- 统一的错误处理和响应格式

## 部署说明

渲染服务直接在123进行部署

[123](https://123.woa.com/v2/test#/server-manage/index?app=render_server&server=common_render&_tab_=nodeManage)

## 常见问题

### 1. 服务启动失败

检查：
- 端口是否被占用
- 环境变量是否配置正确
- Node.js 版本是否符合要求

### 2. 视图渲染失败

检查：
- 模板文件是否存在
- 模板语法是否正确
- 数据格式是否匹配

## 错误码说明

- 0: 成功
- 400: 请求参数错误
- 401: 未授权
- 403: 无权限
- 500: 服务器错误

## 日志管理

项目使用 Winston 进行日志管理，日志文件位于 `logs` 目录：
- `access.log`: 访问日志
- `error.log`: 错误日志
- `app.log`: 应用日志

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交变更
4. 发起 Pull Request

## 许可证

MIT License


