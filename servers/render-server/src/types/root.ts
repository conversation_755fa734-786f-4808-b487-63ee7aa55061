export interface HtmlRootDataAppProjVerMapP1 {
  o: string;
  b: string;
}

export interface HtmlRootDataAppProjVerMap {
  p1: HtmlRootDataAppProjVerMapP1;
}

export interface HtmlRootDataAppProjVer {
  map: HtmlRootDataAppProjVerMap;
  utime: number;
}

export interface HtmlRootDataApp {
  id: number;
  name: string;
  app_group_name: string;
  name_in_sec: string;
  logo: string;
  splash_screen: string;
  is_test: number;
  enable_build_to_online: number;
  enable_pipeline: number;
  token: string;
  cnname: string;
  desc: string;
  class_name: string;
  class_key: string;
  create_by: string;
  online_version: string;
  pre_version: string;
  test_version: string;
  build_version: string;
  npm_version: string;
  enable_display: number;
  api_host: string;
  render_mode: string;
  extract_mode: string;
  proj_ver: HtmlRootDataAppProjVer;
  ui_framework: string;
  host_map: string;
  git_repo_url: string;
  is_rich: number;
  is_top: number;
  is_back_render: number;
  additional_scripts: unknown[];
  additional_body_scripts: unknown[];
  is_local_render: number;
  render_app_host: string;
  enable_gray: number;
  is_in_gray: number;
  owners: string[];
  gray_users: string[];
  is_xc: number;
  create_at: string;
  update_at: string;
}

export interface HtmlRootDataVersionSrcMapHeadAssetListAttrs {
  src: string;
}

export interface HtmlRootDataVersionSrcMapHeadAssetList {
  tag: string;
  append: boolean;
  attrs: HtmlRootDataVersionSrcMapHeadAssetListAttrs;
}

export interface HtmlRootDataVersionSrcMapBodyAssetListAttrs {
  src: string;
}

export interface HtmlRootDataVersionSrcMapBodyAssetList {
  tag: string;
  append: boolean;
  attrs: HtmlRootDataVersionSrcMapBodyAssetListAttrs;
}

export interface HtmlRootDataVersionSrcMap {
  webDirPath: string;
  htmlIndexSrc: string;
  extractMode: string;
  iframeSrc: string;
  chunkCssSrcList: string[];
  chunkJsSrcList: string[];
  staticCssSrcList: unknown[];
  staticJsSrcList: unknown[];
  relativeCssSrcList: unknown[];
  relativeJsSrcList: unknown[];
  privCssSrcList: unknown[];
  headAssetList: HtmlRootDataVersionSrcMapHeadAssetList[];
  bodyAssetList: HtmlRootDataVersionSrcMapBodyAssetList[];
}

export interface HtmlRootDataVersion {
  id: number;
  sub_app_id: string;
  sub_app_name: string;
  sub_app_version: string;
  src_map: HtmlRootDataVersionSrcMap;
  html_content: string;
  create_by: string;
  desc: string;
  api_host: string;
  project_name: string;
  pipeline_id: string;
  build_id: string;
  git_branch: string;
  git_hashes: string;
  git_messages: unknown[];
  git_repo_url: string;
  plugin_ver: string;
  npm_version: string;
  create_at: string;
  update_at: string;
}

export interface HtmlRootData {
  app: HtmlRootDataApp;
  version: HtmlRootDataVersion;
}

export interface HtmlRoot {
  data: HtmlRootData;
  msg: string;
  code: string;
}
