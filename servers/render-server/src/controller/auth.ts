import { Context } from 'koa';
import { hostInstance } from '../services/hosts';

export const getUserName = async (ctx: Context) => {
  const userName = ctx.currentUser.name;
  ctx.success(userName);
};

/** 获取用户所有角色 */
export const getRoleList = async (ctx: Context) => {
  const userName = ctx.currentUser.name;
  if (!userName) {
    ctx.error(-1, 'please login');
    return;
  }
  const panguAction = hostInstance.getPanguAction(ctx.hostname, {
    ...ctx.hostConfig.pangu,
    staffname: userName,
  });
  try {
    const res = await panguAction.getUserRoleList(userName || '', 1);
    const { retObj } = res || {};
    ctx.success(retObj);
  } catch (error) {
    ctx.logger.error(error);
    ctx.error(-1, 'getRoleList error');
  }
  const roleRes = await panguAction.getUserRoleList(userName || '', 1);
  const { retObj } = roleRes || {};
  ctx.success(retObj);
};

/** 获取用户所有权限 */
export const getUserAuthTree = async (ctx: Context) => {
  const userName = ctx.currentUser.name || '';
  if (!userName) {
    ctx.body = {
      code: -1,
      msg: 'please login',
    };
    return;
  }
  const panguAction = hostInstance.getPanguAction(ctx.hostname, {
    ...ctx.hostConfig.pangu,
    staffname: userName,
  });
  try {
    const res = await panguAction.getUserAuthTree(userName, 1);
    const { retObj } = res || {};
    ctx.success(retObj);
  } catch (error) {
    ctx.logger.error(error);
    ctx.error(-1, 'getUserAuthTree error');
  }
};

/** 获取系统所有权限 */
export const getAllAuths = async (ctx: Context) => {
  const { name, apiPath } = ctx.query;
  try {
    const userName = ctx.currentUser.name;
    const panguAction = hostInstance.getPanguAction(ctx.hostname, {
      ...ctx.hostConfig.pangu,
      staffname: userName,
    });
    const res = await panguAction.getProjectAuthList(name as string, apiPath as string);
    const { retObj } = res || {};
    ctx.success(retObj);
  } catch (error) {
    ctx.logger.error(error);
    ctx.error(-1, 'getAllAuths error');
  }
};

/** 获取系统所有角色 */
export const getAllRoles = async (ctx: Context) => {
  try {
    const userName = ctx.currentUser.name;
    const panguAction = hostInstance.getPanguAction(ctx.hostname, {
      ...ctx.hostConfig.pangu,
      staffname: userName,
    });
    const res = await panguAction.do({
      url: `roleGroup/getList?projectId=${ctx.hostConfig.pangu.projectId}`,
      isOpenApi: true,
    });
    const { retObj } = res || {};
    ctx.success(retObj);
  } catch (error) {
    ctx.logger.error(error);
    ctx.error(-1, 'getAllAuths error');
  }
};
