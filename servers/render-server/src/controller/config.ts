import { Context } from 'koa';
import { getConfig, getGroupConfig } from '../extend/rainbow';

export const getConfigs = async (ctx: Context) => {
  const { group, key } = ctx.request.query as { group: string; key: string };
  const config = await getConfig(group, key);
  config ? ctx.success(config) : ctx.error(-1, '获取配置失败');
};

export const getGroupConfigs = async (ctx: Context) => {
  const { group } = ctx.request.query as { group: string };
  const config = await getGroupConfig(group);
  config ? ctx.success(config) : ctx.error(-1, '获取配置失败');
};
