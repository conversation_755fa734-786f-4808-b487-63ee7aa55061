// 地址正则
export const htmlUrlPattern = /^(https?:\/\/)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/[a-zA-Z0-9-._~:\\/?#[\]@!$&'()*+,;=]*)?$/;

// 用户名正则
export const userNamePattern = /^(?!_)(?!.*_$)[a-z_]+$/;

// 多用户正则
export const multiUserReg = /^(?:[a-zA-Z_]+)(?:,[a-zA-Z_]+)*$/;

// 仅英文下划线正则
export const onlyEnglishUnderlinePattern = /^[a-zA-Z_]+$/;

// 仅英文下划线正则
export const onlyEnglishPattern = /^[a-zA-Z]+$/;

// 中英文数字下划线
export const onlyEnCnLine = /^[\u4e00-\u9fa5a-zA-Z0-9_]+$/;
