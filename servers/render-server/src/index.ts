import * as path from 'path';
import QnNode, { registerPlugins } from '@tencent/qn-node';
import QnView from '@tencent/qn-node-view';
import QnHttp from '@tencent/qn-node-http';
import HostConfigMiddleWare from './middleware/host-config';
import TaihuMiddleware from './middleware/taihu';
import PanguMiddleware from './middleware/pangu';
import ErrorCatchMiddleware from './middleware/error-catch';
import FilterMiddleware from './middleware/filter';
import ReturnBodyMiddleware from './middleware/return-body';
import { ROUTE_CONFIG } from './routes';
import { getGroupConfig } from './extend/rainbow';

// 预请求配置，缓存
getGroupConfig('hosts');

// 启动服务器
const PORT = Number(process.env.PORT) || 3000;
const qnNode = new QnNode(PORT);
const app = qnNode.getApp();

const qnView = new QnView({
  basePath: path.join(__dirname, './views'),
});

const qnHttp = new QnHttp({
  requestConfig: { cors: false },
  routeConfig: ROUTE_CONFIG,
});

// 插件
registerPlugins(app, [
  new HostConfigMiddleWare(),
  new TaihuMiddleware(),
  new PanguMiddleware(app),
  new FilterMiddleware(),
  new ReturnBodyMiddleware(),
  new ErrorCatchMiddleware(),
  qnView,
  qnHttp,
]);

qnNode.start();
