import { isProd } from './utils/env';

export const galileoConfig = {
  ip: process.env.HOST_IP || '',
  port: process.env.main_port || '',
  env: process.env.SUMERU_ENV || '',
  namespace: isProd() ? 'Production' : 'Development',
  app: 'render_server',
  server: 'common_render',
  name: 'trpc.render_server.common_render.Greeter',
  containerName: process.env.SUMERU_CONTAINER_NAME,
  city: process.env.SUMERU_CITY,
};

export enum ErrorCode {
  SUCCESS = 200,
  EXCEPTION = 500,
  NOT_FOUND = 404,
  REDIRECT = 302,
}

export const ADMIN_ROLE_ID = 279780;
