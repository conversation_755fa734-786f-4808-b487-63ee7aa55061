import { RouteConfig } from '@tencent/qn-node-http';
import { getRootHtml } from '../controller/root';
import { getConfigs, getGroupConfigs } from '../controller/config';
import {
  getAllAuths,
  getAllRoles,
  getRoleList,
  getUserAuthTree,
  getUserName,
} from '../controller/auth';

export const ROUTE_CONFIG: RouteConfig = {
  routes: [
    {
      path: '/',
      method: 'GET',
      controller: getRootHtml,
    },
    {
      path: '/renderapi',
      children: [
        {
          path: '/user',
          children: [
            {
              path: '/user_name',
              controller: getUserName,
            },
            {
              path: '/role_list',
              controller: getRoleList,
            },
            {
              path: '/auths',
              controller: getUserAuthTree,
            },
            {
              path: '/all_auths',
              controller: getAllAuths,
            },
            {
              path: '/all_roles',
              controller: getAllRoles,
            },
          ],
        },
        {
          path: '/config',
          children: [
            {
              path: '/get_configs',
              controller: getConfigs,
            },
            {
              path: '/get_group_configs',
              controller: getGroupConfigs,
            },
          ],
        },
      ],
    },
    {
      path: '/(.*)',
      controller: getRootHtml,
    },
  ],
};

