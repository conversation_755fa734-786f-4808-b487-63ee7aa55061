/**
 * 伽利略日志上报
 */
import { GalileoLog, GalileoMetric, ReqStatus } from '@tencent/qn-node-galileo';
import { Context } from 'koa';
import { isDev } from '../utils/env';
import { galileoConfig, ErrorCode } from '../consts';

const { ip, port, env, app, server, namespace, name, containerName, city } = galileoConfig;

class Galileo {
  public log: GalileoLog;
  public metric: GalileoMetric;

  private target = `PCG-123.${app}.${server}`;
  private instance = `${ip}:${port}`;
  private serverName = `${app}.${server}`;

  constructor() {
    this.log = new GalileoLog({
      env,
      server: this.serverName,
      namespace,
      target: this.target,
      instance: this.instance,
      containerName,
      'service.name': name,
    });

    this.metric = new GalileoMetric({
      env_name: env,
      namespace,
      target: this.target,
      instance: this.instance,
      server: this.serverName,
      container_name: containerName,
      'service.name': name,
    });
  }

  // 上报错误日志
  public reportError(message: string, desc: string, operator?: string) {
    if (isDev()) {
      return;
    }
    const logContent = this.getLogParams({ message, desc, operator });
    return this.log.reportLog(logContent);
  }

  // 上报普通日志
  public reportInfo(message: string, desc: string, operator?: string) {
    if (isDev()) {
      return;
    }
    const logContent = this.getLogParams({ message, desc, operator, level: 'info' });
    return this.log.reportLog(logContent);
  }

  // 获取日志上报参数
  public getLogParams({
    level = 'error',
    desc = '服务错误',
    operator = '--',
    message,
  }: {
    level?: 'info' | 'error';
    desc?: string;
    operator?: string;
    message: string;
  }) {
    return {
      level,
      desc,
      operator,
      message,
    };
  }

  // 获取被调上报参数
  public getMetricServer(ctx: Context) {
    const isException = ctx.status !== ErrorCode.SUCCESS && ctx.status !== ErrorCode.REDIRECT;
    const { path } = ctx.request;
    return {
      caller_server: '',
      caller_method: `${ctx.request.method}-${path}`,
      caller_ip: ctx.request.ip?.replace('::ffff:', ''),

      callee_service: this.serverName,
      callee_server: this.serverName,
      callee_method: `${ctx.request.method}-${path}`,
      callee_container: containerName,
      callee_ip: ip,

      code: `${ctx.status}`,
      code_type: isException ? ReqStatus.EXCEPTION : ReqStatus.SUCCESS,
      city,
    };
  }

  // 获取主调上报参数
  public getMetricClient({ path, method = '', host = '', code, codeType }: {
    path: string;
    method?: string;
    host?: string;
    code: string;
    codeType: ReqStatus;
  }) {
    return {
      caller_service: this.serverName,
      caller_server: this.serverName,
      caller_ip: ip,

      callee_service: host,
      callee_server: host,
      callee_method: `${method}-${path}`,
      code,
      code_type: codeType,
      city,
    };
  }
}

export const galileo = new Galileo();
