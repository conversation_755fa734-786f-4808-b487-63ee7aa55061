import { initRainbowClient } from '@tencent/qn-rainbow';
import logger from './logger';
import { ENVS } from '../utils/env';

export interface RainbowConfigPangu {
  projectId: number;
  loginType: string;
  token: string;
  domain: string;
  staffname: string;
  whiteList?: string[];
  isProd?: boolean;
}

export interface RainbowConfigTaihu {
  token: string;
}

export interface HostConfig {
  appName: string;
  pangu: RainbowConfigPangu;
  taihu: RainbowConfigTaihu;
}

export type HostsConfigs = Record<string, HostConfig>;

const env2rainbow: Record<string, string> = {
  [ENVS.dev]: 'dev',
  [ENVS.test]: 'test',
  [ENVS.formal]: 'prod',
};

export type RecordAny = Record<string, any>;

const rainbowEnv = env2rainbow[process.env.SUMERU_ENV as ENVS] || 'dev';

export const rainbow = initRainbowClient({
  appID: '143fe199-d56b-4fff-a2c8-7bbfe5664b6d',
  // 【可选】使用内存缓存，无特殊情况都要开启。业务需严格按照规范打开本地内存缓存，保证当七彩石后端出现异常后，线上业务服务还可以正常运行。
  isUsingLocalCache: true,
  // 【可选】使用文件缓存，无特殊情况都要开启。原因同上
  isUsingFileCache: true,
  connectStr: 'http://api.rainbow.woa.com:8080',
  envName: rainbowEnv,
  groupConfList: [
    {
      group: 'hosts',
      envName: rainbowEnv,
      allowLocal: true,
      // @ts-ignore
      localLevel: 'watch',
    },
  ],
});

export async function getGroupConfig<T extends RecordAny = RecordAny>(group: string) {
  try {
    const res = await rainbow.getKVConfig<T>(group);
    return res;
  } catch (error) {
    logger.error(`getGroupConfig ${group} error`);
    logger.error(error);
    return null;
  }
}

export async function getConfig<T = unknown>(group: string, key: string) {
  try {
    const res = await rainbow.getKVConfigItem<T>(group, key);
    return res;
  } catch (error) {
    logger.error(`getConfig ${group} ${key} error`);
    logger.error(error);
    return null;
  }
}

