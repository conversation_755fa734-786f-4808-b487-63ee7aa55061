import * as path from 'path';
import winston, { format, transports } from 'winston';
import 'winston-daily-rotate-file';

const { combine, timestamp, printf } = format;

const logPath = path.resolve(__dirname, '../../logs');

const timeFormat = printf(({ level, message, timestamp }) => `${timestamp} [${level}]: ${message}`);

const logger = winston.createLogger({
  format: combine(
    timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), // 添加时间戳
    timeFormat,
  ),
  transports: [
    new transports.DailyRotateFile({
      filename: `${logPath}/info-%DATE%.log`, // 信息日志文件名格式
      datePattern: 'YYYY-MM-DD', // 日期格式
      zippedArchive: true, // 是否压缩归档的日志文件
      maxSize: '20m', // 每个日志文件的最大大小
      maxFiles: '14d', // 保留日志文件的天数
      level: 'info', // 只记录 info 及以下级别的日志
    }),
    new transports.DailyRotateFile({
      filename: `${logPath}/error-%DATE%.log`, // 错误日志文件名格式
      datePattern: 'YYYY-MM-DD', // 日期格式
      zippedArchive: true, // 是否压缩归档的日志文件
      maxSize: '20m', // 每个日志文件的最大大小
      maxFiles: '14d', // 保留日志文件的天数
      level: 'error', // 只记录 error 及以上级别的日志
    }),
  ],
});

const logInfo = (data: unknown) => {
  if (typeof data === 'string') {
    logger.info(data);
    console.log(data);
  } else {
    logger.info(JSON.stringify(data));
    console.log(JSON.stringify(data));
  }
};

const logError = (data: unknown) => {
  if (typeof data === 'string') {
    logger.error(data);
    console.error(data);
  } else {
    logger.error(JSON.stringify(data));
    console.error(JSON.stringify(data));
  }
};

export default {
  info: logInfo,
  error: logError,
};
