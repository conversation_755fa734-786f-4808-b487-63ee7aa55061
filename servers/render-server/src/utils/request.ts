import { Request } from '@tencent/qn-request';
import { galileo } from '../extend/galileo';
import logger from '../extend/logger';

export const request = new Request({
  retryCount: 3,
  timeout: 10000,
});

request.addResponseInterceptor((response) => {
  if (response.status !== 200) {
    const message = JSON.stringify({
      headers: response.headers,
      data: response.data,
      status: response.status,
    });
    galileo.reportError(message, 'getwayRequest error');
    logger.error(`getwayRequest error: ${message}`);
  }
  return response;
}, (e) => {
  const message = JSON.stringify(e);
  galileo.reportError(message, 'getwayRequest error');
  logger.error(`getwayRequest error: ${message}`);
});
