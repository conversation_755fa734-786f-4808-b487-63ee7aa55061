export const parseFilePath = (url: string) => {
  // 使用正则表达式匹配路径和文件名
  const pathReg = /.*(\/maker\/.*)/;
  const paths = url.match(pathReg);
  // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
  const [_, filePath] = paths || [];
  return filePath?.slice(1) || '';
};

export function safeParse<T = unknown>(jsonData: string) {
  try {
    return JSON.parse(jsonData) as T;
  } catch (error) {
    return jsonData as T;
  }
}

export const isVal = (val: unknown) => val !== undefined && val !== null;

