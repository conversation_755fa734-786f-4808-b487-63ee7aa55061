import Koa, { Context, Next } from 'koa';
import panguKoaModule from '@tencent/pangu_koa_module';
import { isDev } from '../utils/env';

export default class PanguMiddleware {
  public name = 'qn-node-pangu';
  private app: Koa;
  constructor(app: Koa) {
    this.app = app;
  }

  public run = async (ctx: Context, next: Next) => {
    if (isDev()) {
      await next();
    } else {
      const { pangu: panguConfigs } = ctx.hostConfig || {};
      return panguKoaModule({
        app: this.app,
        ...panguConfigs,
        staffname: ctx.currentUser.name,
      })(ctx, next);
    }
  };
}
