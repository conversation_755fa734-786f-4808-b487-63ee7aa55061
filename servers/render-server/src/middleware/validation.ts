import { Context, Next } from 'koa';
import { Schema } from 'joi';

interface ValidationOptions {
  bodySchema?: Schema;
  querySchema?: Schema;
}

export const validation = ({
  bodySchema,
  querySchema,
}: ValidationOptions) => async (ctx: Context, next: Next) => {
  if (bodySchema) {
    const { error: bodyError } = bodySchema.validate(ctx.request.body, { abortEarly: false });
    if (bodyError) {
      ctx.status = 400;
      ctx.body = {
        code: 1002,
        msg: bodyError.message,
        data: bodyError.details.map(detail => detail.message),
      };
      return;
    }
  }
  if (querySchema) {
    const { error: queryError } = querySchema.validate(ctx.request.query, { abortEarly: false });
    if (queryError) {
      ctx.status = 400;
      ctx.body = {
        code: 1002,
        msg: queryError.message,
        data: queryError.details.map(detail => detail.message),
      };
      return;
    }
  }
  await next();
};
