import { Context, Next } from 'koa';
import { getGroupConfig, HostConfig } from '../extend/rainbow';
import logger from '../extend/logger';
import { isDev } from '../utils/env';

declare module 'koa' {
  interface Context {
    hostConfig: HostConfig;
  }
}

export default class HostConfigMiddleware {
  public name = 'qn-node-hostconfig';

  public run = async (ctx: Context, next: Next) => {
    if (isDev()) {
      ctx.hostConfig = {
        appName: 'newscontent-test',
        taihu: { token: 'test' },
        pangu: {
          token: 'test',
          projectId: 0,
          loginType: 'test',
          domain: 'test',
          staffname: 'test',
        },
      };
      await next();
      return;
    }
    // 处理逻辑
    const hostConfigs = await getGroupConfig<Record<string, HostConfig>>('hosts');
    if (!hostConfigs) {
      logger.error('获取七彩石配置失败');
      ctx.body = {
        code: -1,
        msg: '获取七彩石配置失败',
      };
      return;
    }
    ctx.hostConfig = hostConfigs[ctx.hostname] as HostConfig;
    if (!ctx.hostConfig) {
      logger.error('域名未支持');
      ctx.body = {
        code: -1,
        msg: '域名未支持',
      };
      return;
    }
    console.log('ctx.hostConfig', ctx.hostConfig);
    await next();
  };
}
