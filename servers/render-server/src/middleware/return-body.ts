import { Context, Next } from 'koa';
import { galileo } from '../extend/galileo';
import logger from '../extend/logger';

declare module 'koa' {
  interface Context {
    success: (data: unknown) => void;
    error: (code?: number, msg?: string) => void;
  }
}

export default class ReturnBodyMiddleware {
  public name = 'qn-node-return-body';

  public run = async (ctx: Context, next: Next) => {
    ctx.success = (data) => {
      ctx.status = 200;
      ctx.body = {
        code: 0,
        data,
        msg: 'success',
      };
    };
    ctx.error = (code?: number, msg?: string) => {
      ctx.status = 200;
      ctx.body = {
        code: code || -1,
        data: null,
        msg,
      };
      const message = JSON.stringify({
        headers: ctx.headers,
        path: ctx.path,
        method: ctx.method,
      });
      galileo.reportError(message, msg || '', ctx.currentUser.name);
      logger.error(msg);
    };
    await next();
  };
}
