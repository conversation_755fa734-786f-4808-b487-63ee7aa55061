import { Context, Next } from 'koa';
import { galileo } from '../extend/galileo';

export default class ErrorCatchMiddleware {
  public name = 'qn-node-errorcatch';

  public run = async (ctx: Context, next: Next) => {
    let message = '';
    let desc = '';
    try {
      await next();
      if (ctx.status === 404) {
        desc = `${ctx.path}-404`;
        message = JSON.stringify({
          headers: ctx.headers,
          path: ctx.path,
          method: ctx.method,
        });
        ctx.error(404);
      }
    } catch (error) {
      console.log('error', error);
      // 报错时，记录错误日志，上传伽利略
      desc = `${ctx.path}-服务内部错误`;
      message = JSON.stringify({
        // @ts-expect-error error
        stack: error.stack,
        headers: ctx.headers,
        path: ctx.path,
        method: ctx.method,
        data: ctx.data,
      });
      ctx.error(500);
    }
    if (message) {
      galileo.reportError(message, desc, ctx.currentUser.name);
    }
  };
}
