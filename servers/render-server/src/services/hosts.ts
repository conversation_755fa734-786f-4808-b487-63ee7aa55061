import { PanguAction } from '@tencent/pangu_koa_module';
import type { RainbowConfigPangu } from '../extend/rainbow';
import { getHtmlStr } from './root';

interface CacheItem {
  data: string;
  lastUpdate: number;
}

class Hosts {
  private panguActionMap: Record<string, PanguAction> = {};
  private htmlStrMap: Record<string, CacheItem> = {};
  private cacheMaxAge = 2 * 60 * 1000; // 缓存有效期：2分钟

  getPanguAction(hostName: string, panguConfig: RainbowConfigPangu) {
    if (!this.panguActionMap[hostName]) {
      this.panguActionMap[hostName] = new PanguAction(panguConfig);
    }
    return this.panguActionMap[hostName];
  }

  async getHtmlStr(appName: string): Promise<string> {
    const now = Date.now();
    const cached = this.htmlStrMap[appName];

    // 如果没有缓存，立即获取
    if (!cached) {
      const htmlStr = await getHtmlStr(appName);
      this.htmlStrMap[appName] = {
        data: htmlStr,
        lastUpdate: now,
      };
      return htmlStr;
    }

    // 如果缓存过期，后台更新缓存
    if (now - cached.lastUpdate > this.cacheMaxAge) {
      // 异步更新缓存，不阻塞当前请求
      void this.updateCache(appName);
    }

    // 返回缓存的数据
    return cached.data;
  }

  private async updateCache(appName: string): Promise<void> {
    try {
      const htmlStr = await getHtmlStr(appName);
      this.htmlStrMap[appName] = {
        data: htmlStr,
        lastUpdate: Date.now(),
      };
      console.log(`[Cache] 更新 ${appName} 的 HTML 缓存成功`);
    } catch (error) {
      console.error(`[Cache] 更新 ${appName} 的 HTML 缓存失败:`, error);
    }
  }
}

export const hostInstance = new Hosts();
