import { request } from '../utils/request';
import type { HtmlRoot } from '../types/root';
import logger from '../extend/logger';

const defaultHtmlStr = '<html><body>出错了</body></html>';
export const getHtmlStr = async (appName: string) => {
  try {
    const res = await request.get<HtmlRoot>({
      url: 'http://tnews.woa.com/openapi/v1/app/info/getSubAppAndItsFullVersion',
      data: { name: appName },
    });
    const { data } = res.data;
    const { version } = data;
    return version?.html_content || defaultHtmlStr;
  } catch (error) {
    logger.error(error);
    return defaultHtmlStr;
  }
};
