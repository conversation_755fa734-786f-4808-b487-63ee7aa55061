#!/bin/bash

# node-agent 启动器已经做了 worker 进程监控及主动拉起，这里只需要检查 master 进程

bashPath=/usr/local/app
trpcPath=/usr/local/trpc/bin
srcPath=$trpcPath/src/servers/render-server
logFile=$bashPath/monitor.log

pidHasChanged=10003

masterNum=$(ps -ef | grep $srcPath | grep "master process" | grep -v grep | wc -l)

if [ $masterNum -gt 1 ]; then
  echo "The master process is more then one, this should not happened, at $(date)" >>$logFile
  ps -ef | grep $srcPath | grep "master process" | grep -v grep | sort -k5 | tail -1 | awk '{print $2}' | xargs kill -9
elif [ $masterNum -eq 0 ]; then
  echo "The master process has lost, at $(date)" >>$logFile
  startScriptNum=$(ps -ef | grep $bashPath/start.sh | grep -v grep | wc -l)
  if [ $startScriptNum -lt 1 ]; then
    echo "start.sh is not executing now so begin to execute start.sh" >>$logFile
    nohup /bin/sh $bashPath/start.sh 2>&1 &
  else
    echo "start.sh is executing now so do nothing " >>$logFile
  fi
fi

#if pid.conf is exist,the pid check is needed
if [ -f $bashPath/pid.conf ]; then
  pidNow=$(ps -ef | grep $srcPath | grep "master process" | grep -v grep | tail -1 | awk '{print $2}')
  pidOld=$(cat $bashPath/pid.conf)
  if [ $pidNow -ne $pidOld ]; then
    echo "$(date) the pid now $pidNow is not equel with pidInfo in pid.conf $pidOld" >>$logFile
    exit $pidHasChanged
  fi
fi

exit 0
