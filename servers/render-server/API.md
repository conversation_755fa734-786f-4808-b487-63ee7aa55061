# Render Server API 文档

## 基础信息

### 基础URL
- 开发环境：`http://newscontent.testsite.woa.com`
- 生产环境：`https://newscontent.woa.com`

### 通用响应格式
```typescript
interface Response<T> {
  code: number;      // 状态码，0 表示成功
  msg: string;       // 状态信息
  data: T;          // 响应数据
}
```

## 接口列表

### 1. 页面渲染接口

#### 获取根页面
- **路径**: `/`
- **方法**: `GET`
- **控制器**: `getRootHtml`
- **描述**: 返回应用的根页面HTML
- **响应**: HTML页面内容

### 2. 用户认证接口

#### 获取用户名
- **路径**: `/renderapi/user/user_name`
- **方法**: `GET`
- **控制器**: `getUserName`
- **描述**: 获取当前登录用户的用户名
- **响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": "username"
}
```

#### 获取用户角色列表
- **路径**: `/renderapi/user/role_list`
- **方法**: `GET`
- **控制器**: `getRoleList`
- **描述**: 获取当前用户的角色列表
- **响应示例**:

```typescript
// packages/types/src/auth.ts
export type RoleRes = Res<RoleResData[]>;
```

#### 获取用户权限树
- **路径**: `/renderapi/user/auths`
- **方法**: `GET`
- **控制器**: `getUserAuthTree`
- **描述**: 获取当前用户的权限树结构
- **响应示例**:

```typescript
// packages/types/src/auth.ts
type AuthRes = Res<AuthResData[]>;
```

#### 获取所有权限
- **路径**: `/renderapi/user/all_auths`
- **方法**: `GET`
- **控制器**: `getAllAuths`
- **描述**: 获取系统中所有可用的权限列表
- **响应示例**:

```typescript
// packages/types/src/auth.ts
export type GetAllAuthsRes = Res<AuthResData[]>;
```

#### 获取所有角色
- **路径**: `/renderapi/user/all_roles`
- **方法**: `GET`
- **控制器**: `getAllRoles`
- **描述**: 获取系统中所有可用的角色列表
- **响应示例**:

```typescript
// packages/types/src/auth.ts
export type GetAllRolesRes = Res<Role[]>;
```

### 3. 配置管理接口

#### 获取配置信息
- **路径**: `/renderapi/config/get_configs`
- **方法**: `GET`
- **控制器**: `getConfigs`
- **描述**: 获取系统配置信息
- **响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "configKey": "configValue"
  }
}
```

#### 获取分组配置
- **路径**: `/renderapi/config/get_group_configs`
- **方法**: `GET`
- **控制器**: `getGroupConfigs`
- **描述**: 获取分组的配置信息
- **响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "groupName": {
      "configKey": "configValue"
    }
  }
}
```

## 错误处理

### 错误码说明
- 0: 成功
- 400: 请求参数错误
- 401: 未授权
- 403: 无权限访问
- 404: 资源不存在
- 500: 服务器内部错误

### 错误响应示例
```json
{
  "code": 400,
  "msg": "参数错误",
  "data": null
}
```

## 注意事项

1. 所有接口都需要在请求头中携带有效的认证信息
2. 接口返回的时间格式统一使用 ISO 8601 格式
3. 所有请求参数和响应数据都使用 UTF-8 编码
4. API 路径区分大小写
5. 在开发环境中，部分接口可能会返回模拟数据

## 开发环境配置

### hosts 配置
```
127.0.0.1 newscontent.testsite.woa.com
```

### 开发端口
默认端口：3000

## 调试建议

1. 使用 Postman 或类似工具进行接口测试
2. 开发环境下启用详细日志输出
3. 注意检查请求头中的认证信息
4. 使用 Chrome DevTools 的 Network 面板监控请求 