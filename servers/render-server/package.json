{"name": "render-server", "private": true, "version": "1.0.2", "main": "./dist/index.js", "scripts": {"dev": "cross-env SUMERU_ENV=dev nodemon -e ts src/index.ts --files", "build": "tsc --build && cp -r src/views dist/", "start": "HOST=0.0.0.0 PORT=$main_port node dist/index.js", "lint": "eslint --fix . --ext .ts"}, "dependencies": {"@packages/types": "workspace:~", "@tencent/pangu_koa_module": "6.0.5", "@tencent/qn-node": "^1.0.0", "@tencent/qn-node-galileo": "^1.0.0", "@tencent/qn-node-http": "^1.0.0", "@tencent/qn-node-view": "^1.0.0", "@tencent/qn-rainbow": "^0.5.4", "@tencent/qn-request": "^3.0.3", "ejs": "^3.1.10", "fs-extra": "^11.2.0", "joi": "^17.13.3", "jose": "^5.6.3", "koa": "^2.15.3", "koa-body": "^6.0.1", "koa-static": "^5.0.0", "node-cron": "^3.0.3", "winston": "^3.13.1", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@types/ejs": "^3.1.5", "@types/fs-extra": "^11.0.4", "@types/koa": "^2.15.0", "@types/koa-bodyparser": "^4.3.12", "@types/koa-static": "^4.0.4", "@types/koa__router": "^12.0.4", "@types/node-cron": "^3.0.11", "@types/shelljs": "^0.8.15", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "eslint-config-custom-qqnews": "workspace:^", "kill-port": "^2.0.1", "nodemon": "^3.1.7", "prisma": "4.13.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2", "tsconfig": "workspace:^"}, "volta": {"node": "20.16.0", "pnpm": "10.5.2"}}