#!/bin/bash
# encoding: utf-8
# Author: lrelia<PERSON>
# Date: 2024-07-16
# Script: trpc_stop.sh
# Description: stop process in elegant way

bashPath=/usr/local/app
trpcPath=/usr/local/trpc/bin
srcPath=$trpcPath/src/servers/pmonitor-server
logFile=$bashPath/stop.log
processNotExist=10005

#stop process
pidInfo=$(ps -ef | grep $srcPath | grep "master process" | grep -v grep | awk '{print $2}')
echo "$(date) the master pid info is $pidInfo" >>$logFile

for pid in $pidInfo; do
  kill -9 $pid
done

#check process num
num=$(ps -ef | grep $srcPath | grep "master process" | grep -v grep | wc -l)
if [ $num -gt 0 ]; then
  echo "$(date) after stop process in force way the master processNum is $num still bigger than 0" >>$logFile
  exit $processNotExist
fi
exit 0