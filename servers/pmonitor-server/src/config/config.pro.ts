import { IConfig } from './index';

const config: IConfig = {
  db: {
    mysql: {
      host: '',
      port: 3306,
      username: '',
      password: '',
      database: 'db_leah_server',
      dialect: 'mysql',
    },
    redis: {
      ip: '127.0.0.1',
      port: 6379,
      password: '',
    },
    mongo_db: {
      host: '',
      port: '',
      database: '',
    },
  },

  atta: {
    attaId: '',
    attaToken: '',
  },

  wuji: {
    connect: {
      appId: '',
      appKey: '', // todo 添加appKey
      baseUrl: 'http://nodeapi.webdev.com/x/api/wuji_cache/object',
    },
    schemas: {
      table1: '',
    },
  },
  git: {
    groupName: 'Comply-XAC',
    groupCode: '335282',
    groupBasePath: 'Comply-XAC',
    user: '8EVBpiitloV-OALVclNv',
    baseUrl: 'https://api-idc.sgw.woa.com/ebus/gitcode/git_all_api',
  },
};

export default config;
