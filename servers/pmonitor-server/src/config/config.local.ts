import { IConfig } from './index';

const config: IConfig = {
  db: {
    mysql: {
      host: '127.0.0.1',
      port: 3306,
      username: 'root',
      password: '',
      database: 'db_leah_server',
      dialect: 'mysql',
    },
    redis: {
      ip: '127.0.0.1',
      port: 6379,
      password: '',
    },
    mongo_db: {
      host: '',
      port: '',
      database: '',
    },
  },

  wuji: {
    connect: {
      appId: 'leah',
      appKey: '', // todo 添加appKey
      baseUrl: 'http://nodeapi.webdev.com/x/api/wuji_cache/object',
    },
    schemas: {
      table1: '',
    },
  },
  git: {
    groupName: 'pmonitor-web-dev',
    groupCode: '335252',
    groupBasePath: 'pmonitor-web-dev',
    user: 'G066HvOniU9brr6loIKB',
    baseUrl: 'https://git.woa.com',
  },
};

export default config;
