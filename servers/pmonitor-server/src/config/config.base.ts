import * as path from 'path';
import { IConfig } from './index';

const publicPath = path.resolve(__dirname, '../../public');
const uploadPath = path.join(__dirname, '../../upload');
const templatePath = path.join(__dirname, '../../views');

const config: IConfig = {
  port: process.env.PORT || 5001,
  publicPath,
  uploadPath,
  templatePath,
  logger: {
    localLog: false,
    info: path.join(__dirname, '../../logs/info.log'),
    error: path.join(__dirname, '../../logs/error.log'),
  },
  rainbow: {
    connect: {
      connectStr: 'http://api.rainbow.oa.com:8080',
      isUsingLocalCache: true,
      isUsingFileCache: true,
      timeoutPolling: 10000,
    },
    appID: '143fe199-d56b-4fff-a2c8-7bbfe5664b6d',
  },
  git: {
    groupName: 'pmonitor-web-dev',
    groupCode: '335252',
    groupBasePath: 'pmonitor-web-dev',
    user: 'G066HvOniU9brr6loIKB',
    baseUrl: 'https://git.woa.com',
  },
};

export default config;
