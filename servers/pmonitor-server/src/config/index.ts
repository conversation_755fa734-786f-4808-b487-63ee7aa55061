import _ from 'lodash';
import { Options } from 'sequelize';
import { Options as RainbowOptions } from '@tencent/rainbow-node-sdk';
import { isLocal, isProd, isTest } from '../utils/env';
import Rainbow from '../services/tencent/rainbow';

import baseConf from './config.base';
import localConf from './config.local';
import testConf from './config.test';
import proConf from './config.pro';

export interface IConfig {
  port?: string | number;
  publicPath?: string;
  templatePath?: string;
  uploadPath?: string;
  logger?: {
    localLog: boolean; // 是否开启本地日志
    info: string;
    error: string;
  };

  rainbow?: {
    connect: RainbowOptions;
    appID: string;
    group?: string;
  };

  db?: {
    mysql?: Options;
    redis?: {
      ip: string;
      port: number;
      password: string;
    };
    mongo_db?: {
      host: string;
      port: string;
      database: string;
    };
  };

  atta?: {
    attaId: string;
    attaToken: string;
  };

  wuji?: {
    connect: {
      appId: string;
      appKey: string;
      baseUrl: string;
    };
    schemas: any;
  };

  git: {
    groupName: string;
    groupCode: string;
    groupBasePath: string;
    user: string;
    baseUrl: string;
    tof?: string;
    tofPass?: string;
  };
}

export let config: IConfig = baseConf;

const getRainbowEnv = () => {
  if (isLocal()) {
    return 'dev';
  }
  if (isTest()) {
    return 'test';
  }
  if (isProd()) {
    return 'prod';
  }
  return 'dev';
};

export const initConfig = async () => {
  let envConf = {};

  if (baseConf.rainbow) {
    const rainbowEnv = getRainbowEnv();

    if (isLocal()) {
      envConf = localConf;
    } else if (isTest()) {
      envConf = testConf;
    } else if (isProd()) {
      envConf = proConf;
    }
    const rainbowClient = Rainbow.getInstance();
    const mysqlConf = await rainbowClient.getConfig('mysql', rainbowEnv, true);
    const dbConfig = {
      db: {
        mysql: mysqlConf,
      },
    };
    const gitConf = await rainbowClient.getConfig('git', rainbowEnv, true);
    const gitConfig = {
      git: gitConf,
    };
    config = _.merge(baseConf, envConf, dbConfig, gitConfig);
  }
  return config;
};
