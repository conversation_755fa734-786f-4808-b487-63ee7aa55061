import createServer from './core/create-server';
import routes from './routes';
import logger from './services/logger';
import { IConfig } from './config';
import runSchedule from './schedule';

export const start = (config: IConfig) => {
  createServer({
    port: config.port,
    templatePath: config.templatePath,
    publicPath: config.publicPath,
    uploadPath: config.uploadPath,
    logger: logger.getInstance(),
    routes,
  });
  runSchedule();
};
