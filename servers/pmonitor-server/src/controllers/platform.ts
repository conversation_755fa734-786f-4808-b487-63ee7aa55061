import { Context } from 'koa';
import _ from 'lodash';
import xss from 'xss';
import { createGitRepository } from './git';
import models from '../models';
import {
  queryPlatformsSchema,
  addPlatformSchema,
  updPlatformSchema,
  getPlatformInfoSchema,
  delPlatformSchema,
} from '../schemas/platform';
import { isProd, getEnv } from '../utils/env';
import { config } from '../config';

const { groupBasePath } = config.git;

export const queryPlatforms = async (ctx: Context) => {
  const { platformId, platformName, pageSize = 20, current = 1 } = ctx.query;
  const { error } = queryPlatformsSchema.validate({
    platformId,
    platformName,
    pageSize,
    current,
  });
  if (error) {
    ctx.error(error.message);
    return;
  }
  const where = _.omitBy({ platformId, platformName }, _.isNil);

  const result = await Promise.all([
    models.platformNodel.count({
      where,
    }),
    models.platformNodel.findAll({
      limit: +pageSize,
      offset: (+current - 1) * +pageSize,
      where,
    }),
  ]);

  ctx.success({
    total: result[0],
    list: result[1],
    current,
  });
};

// 接入
export const addPlatform = async (ctx: Context) => {
  const {
    platformName = '',
    platformId = '',
    owners = '',
  } = ctx.request.body as { platformName: string; platformId: string; owners: string };
  if (ctx.request.header['tencent-leakscan'] === 'TST(Tencent Security Team)') {
    // 避免扫写入脏数据
    ctx.error('门神安全系统扫描');
    return;
  }
  const filterPlatformId = xss(platformId);
  const filterPlatformName = xss(platformName);
  const filterOwners = xss(owners);
  const { error } = addPlatformSchema.validate({
    platformId: filterPlatformId,
    platformName: filterPlatformName,
    owners: filterOwners,
  });
  if (error) {
    ctx.error(error.message);
    return;
  }
  try {
    const isExitPlatform = await models.platformNodel.findOne({
      where: { platformId: filterPlatformId },
    });
    if (isExitPlatform) {
      ctx.error('名称已存在', null, 2);
      return;
    }
    const env = getEnv();
    const gitName = isProd() ? filterPlatformId : `${filterPlatformId}_${env}`;
    const url = `https://git.woa.com/${groupBasePath}/${gitName}`;
    const gitProject = await createGitRepository({
      name: gitName,
      description: filterPlatformName,
    });
    if (!gitProject) {
      ctx.error('创建git仓库失败，请重试', null, 3);
      return;
    }
    const platformInfo = await models.platformNodel.create({
      platformName: filterPlatformName,
      platformId: filterPlatformId,
      url,
      owners: filterOwners,
    });
    ctx.success(platformInfo);
  } catch (error) {
    ctx.error('入库失败', null, 4);
    return;
  }
};

// 更新
export const updPlatform = async (ctx: Context) => {
  const { id, platformName, owners } = ctx.request.body as { id: string; platformName: string; owners: string };
  if (ctx.request.header['tencent-leakscan'] === 'TST(Tencent Security Team)') {
    // 避免扫写入脏数据
    ctx.error('门神安全系统扫描');
    return;
  }
  const filterPlatformName = xss(platformName);
  const filterId = xss(id);
  const filterOwners = xss(owners);
  const { error } = updPlatformSchema.validate({
    id: filterId,
    platformName: filterPlatformName,
    owners: filterOwners,
  });
  if (error) {
    ctx.error(error.message);
    return;
  }
  const values = _.omitBy(
    {
      platformName: filterPlatformName,
      owners: filterOwners,
    },
    _.isNil,
  );
  try {
    const platformInfo = await models.platformNodel.update(values, {
      where: {
        id: filterId,
      },
    });
    ctx.success(platformInfo);
  } catch (error) {
    ctx.error('更新失败', null, 2);
  }
};

// 获取详情
export const getPlatformInfo = async (ctx: Context) => {
  const { id } = ctx.request.query;
  const { error } = getPlatformInfoSchema.validate({ id });
  if (error) {
    ctx.error(error.message);
    return;
  }
  try {
    const platformInfo = await models.platformNodel.findOne({
      where: {
        id,
      },
    });
    ctx.success(platformInfo);
  } catch (error) {
    ctx.error('删除失败');
  }
};

// 删除，暂不暴露
export const delPlatform = async (ctx: Context) => {
  const { id } = ctx.request.body as { id: string };
  const { error } = delPlatformSchema.validate({ id });
  if (error) {
    ctx.error(error.message);
    return;
  }
  try {
    const platformInfo = await models.platformNodel.destroy({
      where: {
        id,
      },
    });
    ctx.success(platformInfo);
  } catch (error) {
    ctx.error('删除失败');
  }
};
