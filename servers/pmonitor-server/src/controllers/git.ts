import { Context } from 'koa';
import yaml from 'js-yaml';
import dayjs from 'dayjs';
import xss from 'xss';
import { nanoid } from 'nanoid';
import sha256 from 'crypto-js/sha256';
import { httpProxy } from '../services/proxy';
import { createFileSchema, updateFileSchema, getFileInfoSchema } from '../schemas/git';
import models from '../models';
import { createProject, getProjectInfo, pubProject } from '../services/project';
import { isProd, isTest, isLocal, getEnv } from '../utils/env';
import { config } from '../config';

const { groupCode, groupBasePath, user, baseUrl, tof, tofPass } = config.git;

const env = getEnv();

const isTestDev = isTest() || isLocal();

const isLdc = isProd() || isTest();

export const getAuthParams = () => {
  const nonce = nanoid();
  const timestamp = (Date.now() / 1000).toFixed();
  const signature = sha256(`${timestamp}${tof}${nonce}${timestamp}`).toString();
  const upSignature = signature.toUpperCase();
  return {
    'x-tif-nonce': nonce,
    'x-tif-signature': upSignature,
    'x-tif-timestamp': timestamp,
    'x-tif-paasid': tofPass,
  };
};

export const createGitRepository = async (options: any) => {
  let authParams = {};
  if (isLdc) {
    authParams = getAuthParams();
  }
  const { name, description } = options;
  const params = {
    name,
    description,
    visibility_level: 10,
  };
  try {
    let gitResData = await httpProxy({
      method: 'post',
      url: `${baseUrl}/api/v3/projects?private_token=${user}`,
      body: params,
      headers: authParams,
    });
    const { id } = gitResData || {};
    // 目前git创建的项目不能指定到特定组，需要创建完成后迁移
    try {
      await httpProxy({
        url: `${baseUrl}/api/v3/groups/${groupCode}/projects/${id}`,
        method: 'post',
        query: {
          private_token: user,
        },
        headers: authParams,
      });
    } catch (error) {
      gitResData = null;
    }
    return gitResData;
  } catch (error) {
    return null;
  }
};

// 创建文件
export const createFile = async (ctx: Context) => {
  const { fileName, platform, contents, type } = ctx.request.body as {
    fileName: string;
    platform: string;
    contents: string;
    type: string;
  };
  if (ctx.request.header['tencent-leakscan'] === 'TST(Tencent Security Team)') {
    // 避免扫写入脏数据
    ctx.error('门神安全系统扫描');
    return;
  }
  const filterFileName = xss(fileName);
  const filterPlatform = xss(platform);
  const filterContents = xss(contents);
  const { error } = createFileSchema.validate({
    fileName: filterFileName,
    platform: filterPlatform,
    contents: filterContents,
  });
  if (error) {
    ctx.error(error.message);
    return;
  }
  const createDay = dayjs().format('YYYY-MM-DD');
  const projectFullPath = `${groupBasePath}/${platform}`;
  const formatFullPath = encodeURIComponent(projectFullPath);
  let authParams = {};
  if (isLdc) {
    authParams = getAuthParams();
  }
  const filePath = `${createDay}/${fileName}.yaml`;
  const contentObj = JSON.parse(contents);
  const contentText = yaml.dump(contentObj);
  try {
    // 判断git项目是否存在(是否接入)
    const platformId = isTestDev && platform.endsWith(`_${env}`) ? platform.split(`_${env}`)[0] : platform;
    const platformInfo = await models.platformNodel.findOne({
      where: {
        platformId,
      },
    });
    if (!platformInfo) {
      ctx.error('平台未接入，请先接入');
      return;
    }
    const proInfo = await getProjectInfo({
      projectId: fileName,
      platform,
    });
    if (proInfo) {
      ctx.error('项目已经存在，请勿重复创建');
      return;
    }
    const gitResData = await httpProxy({
      url: `${baseUrl}/api/v3/projects/${formatFullPath}/repository/files?private_token=${user}`,
      method: 'post',
      body: {
        id: formatFullPath,
        file_path: filePath,
        branch_name: 'master',
        commit_message: '新增文件',
        content: contentText,
      },
      headers: authParams,
    });
    await createProject({
      platform,
      projectId: fileName,
      type,
    });
    ctx.success(gitResData);
  } catch (error) {
    ctx.error('创建文件失败');
  }
};

// 获取文件信息
export const getFileInfo = async (ctx: Context) => {
  if (ctx.request.header['tencent-leakscan'] === 'TST(Tencent Security Team)') {
    // 避免扫写入脏数据
    ctx.error('门神安全系统扫描');
    return;
  }
  const { fileName = '', platform = '' } = ctx.query as { [key: string]: string };
  const filterFileName = xss(fileName as string);
  const filterPlatform = xss(platform as string);
  const { error } = getFileInfoSchema.validate({
    fileName: filterFileName,
    platform: filterPlatform,
  });
  if (error) {
    ctx.error(error.message);
    return;
  }
  const projectFullPath = `${groupBasePath}/${platform}`;
  const formatFullPath = encodeURIComponent(projectFullPath);
  const url = `${baseUrl}/api/v3/projects/${formatFullPath}/repository/files`;
  let authParams = {};
  if (isLdc) {
    authParams = getAuthParams();
  }
  try {
    const platformId = isTestDev && platform.endsWith(`_${env}`) ? platform.split(`_${env}`)[0] : platform;
    const platformInfo = await models.platformNodel.findOne({
      where: {
        platformId,
      },
    });
    if (!platformInfo) {
      ctx.error('Git仓库不存在，请确认是否已经接入');
      return;
    }
    const proInfo = await getProjectInfo({
      projectId: fileName,
      platform,
    });
    if (!proInfo) {
      ctx.error('文件不存在');
      return;
    }
    const { createTime } = proInfo || {};
    const createDay = dayjs(createTime).format('YYYY-MM-DD');
    const filePath = `${createDay}/${fileName}.yaml`;
    const gitResData = await httpProxy({
      url,
      query: {
        ref: 'master',
        file_path: filePath,
        id: formatFullPath,
        private_token: user,
      },
      headers: authParams,
    });
    const { content = '', encoding = 'base64' } = gitResData || {};
    const contentBuff = Buffer.from(content, encoding);
    const contentText = contentBuff.toString('utf-8');
    const contentObj = yaml.load(contentText);
    ctx.success({
      ...gitResData,
      content: contentObj,
    });
  } catch (error) {
    ctx.error('获取文件信息失败');
  }
};

// 更新文件
export const updateFile = async (ctx: Context) => {
  const { type, fileName, platform, contents, commitMessage } = ctx.request.body as {
    type: string;
    fileName: string;
    platform: string;
    contents: string;
    commitMessage: string;
  };
  if (ctx.request.header['tencent-leakscan'] === 'TST(Tencent Security Team)') {
    // 避免扫写入脏数据
    ctx.error('门神安全系统扫描');
    return;
  }
  const filterFileName = xss(fileName);
  const filterPlatform = xss(platform);
  const filterContents = xss(contents);
  const { error } = updateFileSchema.validate({
    fileName: filterFileName,
    platform: filterPlatform,
    contents: filterContents,
  });
  if (error) {
    ctx.error(error.message);
    return;
  }
  const projectFullPath = `${groupBasePath}/${platform}`;
  const formatFullPath = encodeURIComponent(projectFullPath);
  try {
    const contentObj = JSON.parse(contents);
    const contentText = yaml.dump(contentObj);
    let authParams = {};
    if (isLdc) {
      authParams = getAuthParams();
    }
    const platformId = isTestDev && platform.endsWith(`_${env}`) ? platform.split(`_${env}`)[0] : platform;
    const platformInfo = await models.platformNodel.findOne({
      where: {
        platformId,
      },
    });
    if (!platformInfo) {
      ctx.error('Git仓库不存在，请确认是否已经接入');
      return;
    }
    const proInfo = await getProjectInfo({
      projectId: fileName,
      platform,
    });
    if (!proInfo) {
      ctx.error('文件不存在');
      return;
    }
    const { createTime } = proInfo || {};
    const createDay = dayjs(createTime).format('YYYY-MM-DD');
    const filePath = `${createDay}/${fileName}.yaml`;
    const gitResData = await httpProxy({
      url: `${baseUrl}/api/v3/projects/${formatFullPath}/repository/files?private_token=${user}`,
      method: 'put',
      body: {
        id: projectFullPath,
        file_path: filePath,
        branch_name: 'master',
        commit_message: commitMessage || '修改文件',
        content: contentText,
      },
      headers: authParams,
    });
    if (String(type) === '1') {
      // 发布项目
      await pubProject({ projectId: fileName, platform });
    }
    ctx.success(gitResData);
  } catch (error) {
    ctx.error('更新文件失败');
  }
};
