import { Context } from 'koa';
import { createProject, getProjectInfo, deleteProject, pubProject } from '../services/project';
import { queryProjectByIdSchema, addProjectSchema, delProjectSchema, publishProjectSchema } from '../schemas/project';

export const queryProjectById = async (ctx: Context) => {
  if (ctx.request.header['tencent-leakscan'] === 'TST(Tencent Security Team)') {
    // 避免扫写入脏数据
    ctx.error('门神安全系统扫描');
    return;
  }
  const { projectId, platform } = ctx.request.body as { projectId: string; platform: string };
  const { error } = queryProjectByIdSchema.validate({
    projectId,
    platform,
  });
  if (error) {
    ctx.error(error.message);
    return;
  }
  try {
    const result = await getProjectInfo({ projectId, platform });
    if (result) {
      ctx.success(result);
    } else {
      ctx.error('Can not find project');
    }
  } catch (error) {
    ctx.error('data base error');
  }
};

// 接入
export const addProject = async (ctx: Context) => {
  if (ctx.request.header['tencent-leakscan'] === 'TST(Tencent Security Team)') {
    // 避免扫写入脏数据
    ctx.error('门神安全系统扫描');
    return;
  }
  const { platform, projectId } = ctx.request.body as { platform: string; projectId: string };
  const { error } = addProjectSchema.validate({
    platform,
    projectId,
  });
  if (error) {
    ctx.error(error.message);
    return;
  }
  const projectInfo = await createProject({
    platform,
    projectId,
  });
  ctx.success(projectInfo);
};

export const publishProject = async (ctx: Context) => {
  if (ctx.request.header['tencent-leakscan'] === 'TST(Tencent Security Team)') {
    // 避免扫写入脏数据
    ctx.error('门神安全系统扫描');
    return;
  }
  const { projectId, platform } = ctx.request.body as { projectId: string; platform: string };
  const { error } = publishProjectSchema.validate({ projectId });
  if (error) {
    ctx.error(error.message);
    return;
  }
  const projectInfo = await pubProject({ projectId, platform });
  ctx.success(projectInfo);
};

export const delProject = async (ctx: Context) => {
  if (ctx.request.header['tencent-leakscan'] === 'TST(Tencent Security Team)') {
    // 避免扫写入脏数据
    ctx.error('门神安全系统扫描');
    return;
  }
  const { projectId } = ctx.request.body as { projectId: string };
  const { error } = delProjectSchema.validate({ projectId });
  if (error) {
    ctx.error(error.message);
    return;
  }
  const projectInfo = await deleteProject({ projectId });
  ctx.success(projectInfo);
};
