import Router from 'koa-router';
import { Context, DefaultState } from 'koa';
import * as project from '../controllers/project';

export default (router: Router<DefaultState, Context>) => {
  router.post('/api/project/info', project.queryProjectById);
  router.post('/api/project/create', project.addProject);
  router.post('/api/project/publish', project.publishProject);
  router.post('/api/project/delete', project.delProject);
};
