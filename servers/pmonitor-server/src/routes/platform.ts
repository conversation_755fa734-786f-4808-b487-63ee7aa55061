import Router from 'koa-router';
import { Context, DefaultState } from 'koa';
import * as platform from '../controllers/platform';

export default (router: Router<DefaultState, Context>) => {
  router.get('/api/platform/list', platform.queryPlatforms);
  router.post('/api/platform/create', platform.addPlatform);
  router.post('/api/platform/update', platform.updPlatform);
  router.post('/api/platform/info', platform.getPlatformInfo);
};
