import Joi from 'joi';
import koa from 'koa';
import compose from 'koa-compose';

type TValidator = (valid: { query?: Joi.ObjectSchema; body?: Joi.ObjectSchema }) => compose.Middleware<any>;

const validator: TValidator = (valid) => async (ctx: koa.Context, next: Function) => {
  if (!valid) return next();
  const { query } = ctx.request;
  const { body } = ctx.request;

  let params;
  let paramsValid;

  if (valid.query && valid.body) {
    paramsValid = Joi.object(valid);
    params = {
      query,
      body,
    };
  } else if (valid.query) {
    paramsValid = valid.query;
    params = query;
  } else if (valid.body) {
    paramsValid = valid.body;
    params = body;
  }

  if (paramsValid) {
    const validResult = paramsValid.validate(params);
    if (validResult.error) {
      const err: KoaError = new Error(validResult.error?.message);
      err.code = 1001;
      err.expose = true;
      throw err;
    }
  }

  await next();
};

export default validator;
