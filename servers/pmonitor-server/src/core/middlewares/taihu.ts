import * as jose from 'jose';
import Application from 'koa';

const EXPIRE_LIMIT = 180000;

async function decodeAuthorizationHeader(identity: string, keyBytes: Buffer) {
  const dec = await jose.compactDecrypt(identity, keyBytes);
  const payload = JSON.parse(dec.plaintext.toString());

  const exp = new Date(payload.Expiration);
  // 检验 token 是否已经过期，增加3分钟缓冲，避免服务器时间差异
  if (new Date().getTime() - EXPIRE_LIMIT > exp.getTime()) {
    // demo 为了正常运行，此处异常注释了，实际环境需要开启验证
    throw new Error('expired');
  }

  return payload;
}

async function getUserInfo(headers: Record<string, string>, key: string) {
  const { staffname = '', staffid = '', 'x-tai-identity': taiIdentity } = headers;
  const keyBytes = Buffer.from(key);
  if (taiIdentity) {
    const payload = await decodeAuthorizationHeader(taiIdentity, keyBytes);
    return { staffid: payload.StaffId, staffname: payload.LoginName };
  }
  return { staffid: parseInt(staffid, 10) || 0, staffname };
}

declare module 'koa' {
  interface Context {
    currentUser: {
      id: string;
      name: string;
    };
  }
}

export const smartProxy = async (ctx: Application.Context, next: Application.Next) => {
  if (ctx.get('user-agent') === 'clb-healthcheck') {
    ctx.body = { code: 1, msg: '健康检查' };
    return;
  }
  if (process.env.SUMERU_ENV === 'dev') {
    ctx.currentUser = {
      id: '1',
      name: 'lreliawu',
    };
    ctx.cookies.set('staffid', ctx.currentUser.id);
    ctx.cookies.set('staffname', ctx.currentUser.name);
    await next();
    return;
  }
  // 从header中获取用户信息
  let staff = {
    staffid: '',
    staffname: '',
  };
  try {
    staff = await getUserInfo(
      {
        timestamp: ctx.get('timestamp'),
        signature: ctx.get('signature'),
        'x-tai-identity': ctx.get('x-tai-identity'),
      },
      'CYBQ9SRCQQVFKQHHYIUS5HG3SUVWG4WR',
    );
  } catch (e) {
    ctx.status = 401;
    ctx.body = {
      code: 401,
      msg: '太湖校验失败',
    };
  }
  if (staff?.staffid) {
    // 格式化user 使得后面的直接用这俩参数
    ctx.cookies.set('staffid', staff.staffid);
    ctx.cookies.set('staffname', staff.staffname);
    ctx.currentUser = {
      id: staff.staffid,
      name: staff.staffname,
    };
    await next();
  } else {
    ctx.status = 401;
    ctx.body = {
      code: 401,
      msg: '太湖校验失败',
    };
  }
};
