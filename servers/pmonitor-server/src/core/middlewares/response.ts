import { Middleware } from 'koa';

const response = (): Middleware => async (ctx, next) => {
  ctx.error = (msg = '', data = {}, code = 1) => {
    ctx.body = {
      code,
      data,
      msg,
    };
  };

  ctx.success = (data = null, msg = 'success', code = 0) => {
    ctx.body = {
      code,
      data,
      msg,
    };
  };

  ctx.output = (res: any) => {
    ctx.body = res;
  };

  await next();
};

export default response;
