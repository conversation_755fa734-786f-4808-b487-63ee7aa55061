import koaBody from 'koa-bodyparser';
// import cors from '@koa/cors';
import KoaStatic from 'koa-static';
import KoaViews from 'koa-views';
// import koaLogger from 'koa-logger';
import { ILogger } from '../create-server';

import response from './response';
import errorHandler from './error-handler';
import notfound from './notfound';
// import { smartProxy } from './taihu';

interface IConfig {
  logger?: ILogger;
  publicPath?: string;
  templatePath?: string;
  extension?: string;
  uploadPath?: string;
}

export const initMiddlewares = (config: IConfig = {}) => {
  const middlewares = [];
  // const { logger } = config;
  /**
   * 日志
   */
  // middlewares.push(
  //   koaLogger((str) => {
  //     logger?.info(str);
  //   }),
  // );

  /**
   * 静态资源目录
   */
  config.publicPath && middlewares.push(KoaStatic(config.publicPath));

  /**
   * koa模板
   */
  config.templatePath && middlewares.push(KoaViews(config.templatePath, { extension: config.extension || 'ejs' }));

  /**
   * 跨域
   */
  // middlewares.push(
  //   cors({
  //     origin: '*',
  //     credentials: true,
  //     allowMethods: ['GET', 'HEAD', 'PUT', 'POST', 'DELETE', 'PATCH'],
  //   }),
  // );

  /**
   * 请求参数处理
   */
  middlewares.push(
    koaBody({
      enableTypes: ['json', 'form', 'text', 'xml'],
      formLimit: '10mb',
      jsonLimit: '10mb',
      textLimit: '10mb',
      strict: true,
    }),
  );

  // middlewares.push(smartProxy);

  /**
   * 统一错误处理
   */
  middlewares.push(errorHandler());

  /**
   * success fail
   */
  middlewares.push(response());

  /**
   * 地址未找到
   */
  middlewares.push(notfound());

  return middlewares;
};
