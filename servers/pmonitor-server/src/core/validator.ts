import Joi from 'joi';
import _ from 'lodash';

export const schema = (params: any, valid: Joi.ObjectSchema) => valid.validate(params);

type TValidFunc = (query: { [key: string]: any }, keys: string[] | number[]) => boolean;

const isNull: TValidFunc = (query, keys) => {
  query = query || {};
  keys = _.isArray(keys) ? keys : [keys];

  for (const key of keys) {
    if (query[key] === undefined || query[key] === null) return true;
  }

  return false;
};

// keys = [a,b,c] 是否全部为null
const isAllNull: TValidFunc = (query, keys) => {
  query = query || {};
  keys = _.isArray(keys) ? keys : [keys];

  for (const key of keys) {
    if (query[key] !== undefined) return false;
  }

  return true;
};

export const validator = {
  isNull,
  isAllNull,
};
