import Koa from 'koa';
import compose from 'koa-compose';
import router, { IRoute } from './init-router';
import koaExtension from './koa-extension';
import { initMiddlewares } from './middlewares';

// const auth = require('@tencent/smart-proxy-koa2');
// const cors = require('@koa/cors');

const app = new Koa();

export interface ILogger {
  info: Function;
  debug?: Function;
  warn?: Function;
  error: Function;
  log?: Function;
}

interface IServerOptions {
  port?: string | number;
  routes: IRoute;
  publicPath?: string;
  templatePath?: string;
  extension?: string;
  uploadPath?: string;
  logger: ILogger;
}

const createServer = (options: IServerOptions) => {
  const { logger, port, routes } = options;
  const routers = router(routes);
  koaExtension(app.context);

  // app.use(cors());
  // app.use(auth({
  //   token: 'CYBQ9SRCQQVFKQHHYIUS5HG3SUVWG4WR',
  //   enable: process.env.IS_LOCAL_MODE !== 'true',
  //   defaultUserInfo: {
  //     id: 6666,
  //     name: 'zhangsan'
  //   }
  // }));
  app.use(
    compose(
      initMiddlewares({
        logger,
        publicPath: options.publicPath,
        templatePath: options.templatePath,
        extension: options.extension,
        uploadPath: options.uploadPath,
      }),
    ),
  );

  app.use(routers.routes());
  app.use(routers.allowedMethods());

  app.on('error', onError);

  app.listen(port, () => {
    logger.info('Server start success：');
    logger.info(` - Local:   \x1B[36m http://localhost:\x1B[0m\x1B[96m${port} \x1B[0m`);
  });

  function onError(error: any, ctx: Koa.Context) {
    if (ctx) {
      ctx.body = {
        code: 1000,
        msg: '程序运行时报错',
      };
      return;
    }

    if (error.syscall !== 'listen') {
      throw error;
    }
    const bind = typeof port === 'string' ? `Pipe ${port}` : `Port ${port}`;

    switch (error.code) {
      case 'EACCES':
        logger.error(`${bind} requires elevated privileges`);
        process.exit(1);
        break;
      case 'EADDRINUSE':
        logger.info(`${bind} is already in use`);
        process.exit(1);
        break;
      default:
        throw error;
    }
  }

  process.on('uncaughtException', (err) => {
    logger.error(err);
    process.exit(1);
  });

  process.on('unhandledRejection', (reason, promise) => {
    logger.error(reason, promise);
  });
};

export default createServer;
