import _ from 'lodash';
import { DefaultState, Context } from 'koa';
import Router from 'koa-router';

const router = new Router<DefaultState, Context>();

export interface IRoute {
  [key: string]: (router: Router<DefaultState, Context>) => void;
}

export default (routes: IRoute = {}) => {
  for (const key of Object.keys(routes)) {
    const route = routes[key];
    if (_.isFunction(route)) {
      route(router);
    }
  }
  return router;
};
