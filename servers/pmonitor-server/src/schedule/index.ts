import schedule from 'node-schedule';
import dayjs from 'dayjs';
import { Op } from 'sequelize';
import models from '../models';

const runSchedule = () => {
  /**
   * 每日凌晨0点0分0秒 删除一年前发布的项目
   */
  schedule.scheduleJob('0 0 0 * * *', async () => {
    const oneYearAgo = dayjs().subtract(1, 'year').format('YYYY-MM-DD HH:mm:ss');
    try {
      const result = await models.projectModel.destroy({
        where: {
          [Op.or]: [
            {
              publishTime: {
                [Op.lt]: oneYearAgo,
              },
            },
            {
              publishTime: {
                [Op.or]: [null, ''],
              },
              createTime: {
                [Op.lt]: oneYearAgo,
              },
            },
          ],
        },
      });
      console.log(`Deleted ${result} rows older than one year.`);
    } catch (e) {
      console.log('Error deleting old data:', e);
    }
  });
};

export default runSchedule;
