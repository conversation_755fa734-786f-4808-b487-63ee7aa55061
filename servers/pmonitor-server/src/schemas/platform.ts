import Joi from 'joi';

export const queryPlatformsSchema = Joi.object({
  platformId: Joi.string(),
  platformName: Joi.string(),
  pageSize: Joi.string(),
  current: Joi.string(),
});

export const addPlatformSchema = Joi.object({
  platformName: Joi.string().required(),
  platformId: Joi.string()
    .regex(/^[A-Za-z_]+$/)
    .max(20)
    .required(),
  owners: Joi.string()
    .regex(/^[A-Za-z_,]+$/)
    .required(),
});

export const updPlatformSchema = Joi.object({
  platformName: Joi.string(),
  id: Joi.string().required(),
  owners: Joi.string().regex(/^[A-Za-z_,]+$/),
});

export const getPlatformInfoSchema = Joi.object({
  id: Joi.string().required(),
});

export const delPlatformSchema = Joi.object({
  id: Joi.number().required(),
});
