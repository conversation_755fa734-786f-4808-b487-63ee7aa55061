import Joi from 'joi';

export const createFileSchema = Joi.object({
  fileName: Joi.string().required(),
  platform: Joi.string().required(),
  contents: Joi.string().required(),
});

export const getFileInfoSchema = Joi.object({
  fileName: Joi.string().required(),
  platform: Joi.string().required(),
});

export const updateFileSchema = Joi.object({
  fileName: Joi.string().required(),
  platform: Joi.string().required(),
  contents: Joi.string().required(),
  commitMessage: Joi.string(),
});
