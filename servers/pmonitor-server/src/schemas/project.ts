import Joi from 'joi';

export const queryProjectByIdSchema = Joi.object({
  projectId: Joi.string().required(),
  platform: Joi.string().required(),
});

export const addProjectSchema = Joi.object({
  platform: Joi.string().required(),
  projectId: Joi.string().required(),
});

export const publishProjectSchema = Joi.object({
  projectId: Joi.string().required(),
});

export const delProjectSchema = Joi.object({
  projectId: Joi.string().required(),
});
