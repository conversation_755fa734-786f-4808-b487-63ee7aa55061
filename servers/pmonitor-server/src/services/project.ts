import dayjs from 'dayjs';
import models from '../models';

export const getPrrojects = async (params: any) => {
  const { platform, pageSize = 20, current = 1 } = params;
  const where = { platform };
  const result = await Promise.all([
    models.projectModel.count({
      where,
    }),
    models.platformNodel.findAll({
      limit: +pageSize,
      offset: (+current - 1) * +pageSize,
      where,
    }),
  ]);

  return {
    total: result[0],
    list: result[1],
  };
};

export const getProjectInfo = async (params: any) => {
  const { projectId = '', platform = '' } = params;
  const result = await models.projectModel.findOne({
    where: {
      projectId,
      platform,
    },
  });
  return result;
};

// 接入
export const createProject = async (params: any) => {
  const { platform, projectId, type } = params || {};
  const publishTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
  const isPublish = String(type) === '1';
  const projectInfo = await models.projectModel.create({
    platform,
    projectId,
    createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    publishTime: isPublish ? publishTime : '',
    status: isPublish ? '1' : '0',
  });
  return projectInfo;
};

export const pubProject = async (params: any) => {
  const { projectId, platform } = params;
  if (!projectId || !platform) {
    return;
  }
  const publishTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
  const values = { status: '1', publishTime };
  const projectInfo = await models.projectModel.update(values, {
    where: {
      projectId,
      platform,
    },
  });
  return projectInfo;
};

export const deleteProject = async (params: any) => {
  const { projectId = '' } = params;
  if (!projectId) {
    return;
  }

  const projectInfo = await models.projectModel.destroy({
    where: {
      projectId,
    },
  });
  return projectInfo;
};
