import axios, { Method } from 'axios';

interface IHttpProxyOptions {
  url: string;
  method?: string;
  headers?: any;
  body?: any;
  query?: any;
}

export const httpProxy = async (options: IHttpProxyOptions) => {
  const { url, method = 'get', headers = {}, body = {}, query = {} } = options;
  if (!url) {
    throw Error('url 不能为空');
  }

  const { host, ...proxyHeaders } = headers;
  const result = await axios({
    method: method as Method,
    headers: proxyHeaders || {},
    url,
    params: query,
    data: body,
    timeout: 10000,
  });

  return result.data;
};
