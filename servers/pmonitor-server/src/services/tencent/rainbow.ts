import { Rainbow as TRainbow, Options } from '@tencent/rainbow-node-sdk';
import { config } from '../../config';

interface IRainbowRet {
  group: string;
  version: string;
  event_type: number;
  key_values: { key: string; value: string }[];
  version_id: string;
  version_name: string;
  struct_type: number;
  column_types: any[];
  rows: any[];
  rows_end: boolean;
}

export default class Rainbow {
  static rainbowInstance: Rainbow;
  static getInstance() {
    if (Rainbow.rainbowInstance) {
      return Rainbow.rainbowInstance;
    }

    if (!config?.rainbow?.appID || !config?.rainbow?.connect) {
      throw Error('缺少七彩石配置');
    }

    Rainbow.rainbowInstance = new Rainbow(config.rainbow.appID, config.rainbow.connect);
    return Rainbow.rainbowInstance;
  }

  private client;
  private readonly appId;

  constructor(appId: string, connect: Options) {
    this.appId = appId;
    this.client = new TRainbow(connect);
  }

  public async getConfig(key: string, group: string, json?: boolean) {
    let result = await this.client.get(key, { group, appID: this.appId });
    if (json) {
      try {
        result = JSON.parse(result);
      } catch (e) {
        result = {};
      }
    }
    return result;
  }

  public async getGroupValues(group: string, keys?: string[]) {
    const result: IRainbowRet = await this.client.getGroup({ group, appID: this.appId });
    if (keys && keys.length > 0) {
      result?.key_values?.forEach((item, index) => {
        if (keys.includes(item.key)) {
          result.key_values[index] = JSON.parse(item.value);
        }
      });
    }
    return result;
  }
}
