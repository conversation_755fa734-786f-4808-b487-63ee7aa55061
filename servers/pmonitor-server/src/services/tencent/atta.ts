import util from 'util';
import TAtta from '@tencent/atta';
import _ from 'lodash';
import { config } from '../../config';

export default class Atta {
  static AttaInstance: Atta;

  static getInstance() {
    if (Atta.AttaInstance) {
      return Atta.AttaInstance;
    }

    if (!config?.atta?.attaId || !config?.atta?.attaToken) {
      throw Error('缺少atta配置');
    }

    const { attaId, attaToken } = config.atta;

    Atta.AttaInstance = new Atta(attaId, attaToken);
    return Atta.AttaInstance;
  }

  private atta: TAtta | null;
  private readonly attaId: string;
  private readonly attaToken: string;

  private readonly LOG_DEBUG_CODE = 0;
  private readonly LOG_INFO_CODE = 1;
  private readonly LOG_WARN_CODE = 2;
  private readonly LOG_ERROR_CODE = 3;

  constructor(attaId: string, attaToken: string) {
    this.atta = null;
    this.attaId = attaId;
    this.attaToken = attaToken;
  }

  public debug(info: any) {
    this.log(this.LOG_DEBUG_CODE, info);
  }

  public info(info: any) {
    this.log(this.LOG_INFO_CODE, info);
  }

  public warn(info: any) {
    this.log(this.LOG_WARN_CODE, info);
  }

  public error(info: any) {
    this.log(this.LOG_ERROR_CODE, info);
  }

  private formatInfo(info: any) {
    try {
      if (_.isObject(info)) {
        info = util.inspect(info);
      }
    } catch (e) {
      console.error(info);
      console.error(e);
      info = 'format info error';
    } finally {
      return info;
    }
  }

  private log(code: any, info: any) {
    this.sendLog([code, this.formatInfo(info)]).catch((e) => {
      console.error(e);
    });
  }

  private async sendLog(info: any) {
    let { atta } = this;

    if (!atta) {
      atta = new TAtta();
      await atta.initProtocol('tcp');
      this.atta = atta;
    }

    await atta.send_fields(this.attaId, this.attaToken, info);
  }
}
