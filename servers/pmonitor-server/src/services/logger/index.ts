import _ from 'lodash';
import siz from 'siz';
import log4js from 'log4js';
import Atta from '../tencent/atta';
import { config } from '../../config';

interface TLogFunc {
  debug: Function;
  info: Function;
  warn: Function;
  error: Function;
}

export default class Log {
  static loggerInstance: Log;

  static getInstance() {
    if (Log.loggerInstance) {
      return Log.loggerInstance;
    }

    if (!config.logger) {
      throw Error('缺少日志配置');
    }

    Log.loggerInstance = new Log(
      {
        appenders: {
          info: { type: 'file', filename: config.logger.info, maxLogSize: siz('10mb') },
          error: { type: 'file', filename: config.logger.error, maxLogSize: siz('10mb') },
          console: { type: 'stdout' },
        },
        categories: {
          default: { appenders: ['info', 'console'], level: 'all' },
          error: { appenders: ['error', 'console'], level: 'error' },
        },
      },
      config.logger.localLog,
      !!(config?.atta?.attaId && config?.atta?.attaToken),
    );
    return Log.loggerInstance;
  }

  readonly NUM = 25;
  readonly Decorator = '+';
  private loggerInfo: TLogFunc = console;
  private loggerError: TLogFunc = console;
  private atta;

  private readonly INFODECORATORS;
  private readonly DEBUGDECORATORS;
  private readonly WARNDECORATORS;
  private readonly ERRORDECORATORS;
  private readonly LOGDECORATORS;

  constructor(options: log4js.Configuration, isLocal: boolean, isAtta?: boolean) {
    // 开启本地日志
    if (isLocal) {
      log4js.configure(options);

      this.loggerInfo = log4js.getLogger('info');
      this.loggerError = log4js.getLogger('error');
    }

    if (isAtta) {
      this.atta = Atta.getInstance();
    }

    this.INFODECORATORS = this.decorators('INFO');
    this.DEBUGDECORATORS = this.decorators('DEBUG');
    this.WARNDECORATORS = this.decorators('WARN');
    this.ERRORDECORATORS = this.decorators('ERROR');
    this.LOGDECORATORS = this.decorators('LOG');
  }

  public info(message: any, ...args: any[]) {
    this.loggerInfo.info(this.INFODECORATORS);
    this.loggerInfo.info(message, ...args);
    this.loggerInfo.info(this.INFODECORATORS);
    this?.atta?.info({
      message,
      ...args,
    });
  }

  public debug(message: any, ...args: any[]) {
    this.loggerInfo.info(this.DEBUGDECORATORS);
    this.loggerInfo.info(message, ...args);
    this.loggerInfo.info(this.DEBUGDECORATORS);
    this?.atta?.debug({
      message,
      ...args,
    });
  }

  public warn(message: any, ...args: any[]) {
    this.loggerInfo.warn(this.WARNDECORATORS);
    this.loggerInfo.warn(message, ...args);
    this.loggerInfo.warn(this.WARNDECORATORS);
    this?.atta?.warn({
      message,
      ...args,
    });
  }

  public error(message: any, ...args: any[]) {
    this.loggerInfo.error(this.ERRORDECORATORS);
    this.loggerInfo.error(message, ...args);
    this.loggerInfo.error(this.ERRORDECORATORS);
    this?.atta?.warn({
      message,
      ...args,
    });
  }

  public log(message: any, ...args: any[]) {
    console.log(this.LOGDECORATORS);
    console.log(message, ...args);
    console.log(this.LOGDECORATORS);
  }

  private decorators = (type: string) =>
    `${_.repeat(this.Decorator, this.NUM)} ${type}_LOG ${_.repeat(this.Decorator, this.NUM)}`;
}
