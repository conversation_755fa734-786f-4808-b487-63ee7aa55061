import mongoose from 'mongoose';
import { config } from '../../config';

export default class MongooseClient {
  static mongooseInstance: MongooseClient;
  static async getInstance() {
    if (MongooseClient.mongooseInstance) {
      return MongooseClient.mongooseInstance;
    }

    if (!config?.db?.mongo_db) {
      throw Error('缺少mongo配置');
    }

    const mongoConf = config.db.mongo_db;

    const client = await mongoose.connect(`${mongoConf.host}${mongoConf.port}${mongoConf.database}`);
    MongooseClient.mongooseInstance = new MongooseClient(client);
    return MongooseClient.mongooseInstance;
  }

  private client;
  constructor(client: any) {
    this.client = client;
  }
}
