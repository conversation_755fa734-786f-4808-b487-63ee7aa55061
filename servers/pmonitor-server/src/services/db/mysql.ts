import { Sequelize, Options } from 'sequelize';
import { config } from '../../config';

export default class MysqlClient {
  static instance: MysqlClient;
  static getInstance() {
    if (MysqlClient.instance) {
      return MysqlClient.instance;
    }

    if (!config?.db?.mysql) {
      throw Error('缺少mysql配置');
    }

    MysqlClient.instance = new MysqlClient(config.db.mysql);
    return MysqlClient.instance;
  }

  public client;
  constructor(options: Options) {
    this.client = new Sequelize(options.database || '', options.username || '', options.password, options);
  }
}
