import IORedis from 'ioredis';
import { config } from '../../config';

export default class RedisClient {
  static instance: RedisClient;
  static async getInstance() {
    if (RedisClient.instance) {
      return RedisClient.instance;
    }

    if (!config?.db?.redis) {
      throw Error('缺少redis配置');
    }

    const client = await new Promise<IORedis.Redis>((resolve, reject) => {
      const redis = new IORedis(config?.db?.redis);
      redis.on('connect', () => {
        // console.log('redis connect success!', redisConf);
        resolve(redis);
      });

      redis.on('error', (err) => {
        console.log('redis connect error!', err);
        reject(err);
      });

      redis.on('reconnecting', () => {
        console.log(redis.options);
        // 断线重连时，获取新的ip，port
        // console.log('redis reconnecting.', redisConf);
      });
    });

    RedisClient.instance = new RedisClient(client);

    return RedisClient.instance;
  }

  private client;
  constructor(client: IORedis.Redis) {
    this.client = client;
  }
}
