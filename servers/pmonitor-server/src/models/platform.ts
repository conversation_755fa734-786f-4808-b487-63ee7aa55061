import { Sequelize, DataTypes, Model } from 'sequelize';

interface IPlatform extends Model {
  id: number;
  platformId: string;
  platformName: string;
  url: string;
}

const platFormModel = (client: Sequelize) =>
  client.define<IPlatform>(
    'platform',
    {
      id: {
        primaryKey: true,
        type: DataTypes.NUMBER,
        autoIncrement: true,
        allowNull: false,
      },
      platformId: {
        type: DataTypes.STRING,
        allowNull: false,
        field: 'platform_id',
      },
      platformName: {
        type: DataTypes.STRING,
        allowNull: false,
        field: 'platform_name',
      },
      url: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      owners: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
      },
      updatedAt: {
        type: DataTypes.DATE,
      },
    },
    {
      tableName: 't_pmonitor_platform', // 指定表名
      underscored: true,
    },
  );

export default platFormModel;
