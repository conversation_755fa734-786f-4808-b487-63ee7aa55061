import { Sequelize, DataTypes, Model } from 'sequelize';

interface IPrroject extends Model {
  id: number;
  platform: string;
  createTime: string;
  publishTime: string;
  status: string;
}

const projectModel = (client: Sequelize) =>
  client.define<IPrroject>(
    'project',
    {
      id: {
        primaryKey: true,
        type: DataTypes.NUMBER,
        autoIncrement: true,
        allowNull: false,
      },
      projectId: {
        type: DataTypes.STRING,
        field: 'project_id',
        allowNull: false,
      },
      platform: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      createTime: {
        type: DataTypes.STRING,
        field: 'create_time',
        allowNull: false,
      },
      publishTime: {
        type: DataTypes.STRING,
        field: 'publish_time',
      },
      // 0: 未发布, 1，已发布
      status: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    },
    {
      tableName: 't_pmonitor_project', // 指定表名
      timestamps: false,
    },
  );

export default projectModel;
