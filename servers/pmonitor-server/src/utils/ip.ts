import * as os from 'os';
import { Context } from 'koa';

export const isIP = (ip: string) =>
  /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])(\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])){3}$/.test(ip);

export const isInnerIP = (ip: string) => {
  if (!isIP(ip)) return false;
  if (ip === '127.0.0.1') return true;

  const ips: string[] = ip.split('.');

  if (
    ips[0] === '10' || // 10.x.x.x
    (ips[0] === '172' && ips[1] >= '16' && ips[1] <= '31') || // 172.16.x.x
    (ips[0] === '192' && ips[1] === '168') || // 192.168.x.x
    (ips[0] === '169' && ips[1] === '254') // 169.254.x.x
  ) {
    return true;
  }

  return false;
};

export const getRealIP = (first = true) => {
  const interfaces = os.networkInterfaces() || {};
  const addresses: string[] = [];

  for (const i of Object.keys(interfaces)) {
    for (const j of Object.keys(interfaces[i] || {})) {
      const address = interfaces[i]?.[+j] as os.NetworkInterfaceInfo;
      if (address && address.family === 'IPv4' && !address.internal) {
        addresses.push(address.address);
      }
    }
  }

  return first ? addresses[0] : addresses;
};

// 获取客户端ip
export const getClientIP = (ctx: Context) => {
  const { req } = ctx;
  let ip =
    req.headers['x-real-ip'] ||
    req.headers['x-forwarded-for'] ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress;

  if (Array.isArray(ip)) {
    ip = ip[0] || '';
  }

  if (ip) {
    if (ip.split(',').length > 0) [ip] = ip.split(',');
    if (ip.indexOf('::ffff:') !== -1) ip = ip.substring(7);

    return ip;
  }
};
