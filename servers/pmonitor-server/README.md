## 一、目录

- [项目介绍](#项目介绍)
- [快速上手](#快速上手)
- [工程简介](#工程简介)
  - [项目结构](#项目结构)
  - [git 代码分支](#git代码分支)
- [二次开发](#二次开发)
  - [代码规范](#规范)
  - [模版使用](#模版使用)
  - [添加路由](#添加路由)
  - [添加页面](#添加页面)
  - [添加模块](#添加模块)
  - [添加菜单](#添加菜单)

## 二、项目介绍

<a href="https://demo.leah.woa.com/" target="_blank"><img width="" src="https://tnfe.cdn.qq.com/transformer/audio/assets/124ca6231de875a794f13a874a6160b4.png" alt="image.png" /></a>

> 零配置的中后台前端开发框架

Leah(利亚)是专门为开发人员打造的一个快速开发项目的中台解决方案，它集成了项目开发中的多个痛点的解决方案，将项目开发中开发人员高重复、易出错的步骤封装了起来，原先复杂繁琐的操作，仅仅只需要一条简单的命令行就能让机器快速完成，帮助开发人员便捷、高效地开发前后端项目。

**相关项目**

- [leah-server](https://git.woa.com/leah/leah-server)
- [leah-cli](https://git.woa.com/leah/leah-cli)
- [leah-react](https://git.woa.com/leah/leah-react-app)

## 三、快速上手

- 安装相关依赖

```bash
npm i
```

- 启动与调试项目

```bash
npm run dev # 本地启动应用
```

- 测试与格式校验

```bash
npm run test (jest测试)
npm run lint (eslint校验)
npm run lintfix (eslint修复)
```

- 代码提交与推送

```
git commit -am 'xxxx_msg' (触发 husky钩子 pre-commit: npm run lintfix)
git push (触发 husky钩子 pre-commit: npm run test)
```

- 编译构建

```bash
npm run build
```

## 四、工程简介

### 1. 项目结构

```
.
├── README.md
├── public                 # 静态目录
├── package.json
├── views                  # 服务端模版
└── src
    ├── config             # 静态资源
    ├── controllers        # controller文件
    ├── core               # 框架核心代码
    ├── models             # 数据model
    ├── routes             # 路由文件
    ├── schedule           # 定时器文件
    ├── services           # 服务文件
    ├── types              # 类型定义文件
    ├── utils              # 工具函数
    ├── bootstrap.ts       # 启动文件
    └── index.ts           # 入口文件
```

## 2.git 代码分支

```text
├─master 主分支，最新稳定版
├─develop 开发分支，最新开发版
└─feature 各开发者创建的feature分支
```

## 五、二次开发

### 1.规范

- 必须符合腾讯代码规范，详情见[腾讯代码规范](https://git.woa.com/standards/javascript) ,本地 eslint 校验必须通过

- 单文件，尽量保持在 500 行以内，组件做好抽象和模块化

- 函数复杂度尽量保证在 5 以内，写好注释，函数行数尽量保持在 30 行以内，需考虑可读性、可维护性、可兼容性、异常处理等

### 2.创建service

在 `src/services` 接着在这个目录下新建文件 `demoService.ts`

```typescript
# src/services/demoService.ts

export const say = (name: string) => {
  return `hello ${name}`;
};
```

### 3.添加controller

在 `src/controllers` 接着在这个目录下新建文件 `demo.ts`

```ts
# src/controllers/demo.ts

import { Context } from 'koa';
import { say } from '../services/demoService';

export const helloWorld = (ctx: Context) => {
  const result = say('leah');
  ctx.success({
    word: result,
  });
};

```

### 4.添加路由

在 `src/routes` 下，创建 `demo.ts` 路由文件

```typescript
# src/routes/demo.ts

import Router from 'koa-router';
import { Context, DefaultState } from 'koa';
import * as demo from '../controllers/demo';

export default (router: Router<DefaultState, Context>) => {
  router.get('/api/demo', demo.helloWorld);
};

```

在 `src/routes/index.ts` 中引入 `demo.ts` 

```typescript
# src/routes/index.ts

import { Context, DefaultState } from 'koa';
import Router from 'koa-router';
import demo from './demo';

interface IRoute {
  [key: string]: (router: Router<DefaultState, Context>) => void;
}

const routes: IRoute = {
  demo,
};

export default routes;

```

## 六、常见问题

## 七、行为准则

我们十分期待您通过 MR（Merge Requests）让 leah-server 变的更加完善。

## 八、Commit Message

我们希望您能遵守[约定式提交（Conventional Commits）](https://www.conventionalcommits.org/zh-hans/) ，保持项目的一致性，也可以方便生成每个版本的 Changelog，很容易地被追溯。

## 九、MR 流程

leah 团队会查看所有的 MR，我们会运行一些代码检查和测试，一经测试通过，我们会接受这次 MR，但不会立即发布外网，会有一些延迟。

当您准备 MR 时，请确保已经完成以下几个步骤:

1. 将主仓库代码 Fork 到自己名下。
1. 基于 `master` 分支创建您的开发分支。
1. 如果您更改了 API(s) 请更新代码及文档。
1. 检查您的代码语法及格式。
1. 提一个 MR 到主仓库的 `master` 分支上。

## 十、如何加入

我们十分期待您的任何贡献，无论是修复错别字、提 Bug 还是提交一个新的特性。

如果您使用过程中发现 Bug，请通过 [issues](/issues) 来提交并描述相关的问题，您也可以在这里查看其它的 issue，通过解决这些 issue 来贡献代码。

如果您是第一次贡献代码，请阅读 [CONTRIBUTING](https://git.woa.com/leah/leah-server) 了解我们的贡献流程，并提交 Merge Request 给我们。

## 十一、团队介绍

- [团队 iWiki 文档空间](https://iwiki.woa.com/space/TNTWeb)
- [团队 KM 空间](https://km.woa.com/group/tnfe)
- [本项目的贡献者](https://git.woa.com/leah/leah-server/-/metrics/code_trend/master)

|  [<img src="https://dayu.oa.com/avatars/yajieliu/profile.jpg" width="90px;" /><br />yajeiliu](https://git.code.oa.com/u/yajieliu)   | [<img src="https://dayu.oa.com/avatars/chaozhengxu/profile.jpg" width="90px;" /><br />chaozhengxu](https://git.code.oa.com/u/chaozhengxu) | [<img src="https://dayu.oa.com/avatars/dravenwu/profile.jpg" width="90px;" /><br />dravenwu](https://git.code.oa.com/u/dravenwu) |  [<img src="https://dayu.oa.com/avatars/bohrzhang/profile.jpg" width="90px;" /><br />bohrzhang](https://git.code.oa.com/u/bohrzhang)   |
| :---------------------------------------------------------------------------------------------------------------------------------: | :---------------------------------------------------------------------------------------------------------------------------------------: | :------------------------------------------------------------------------------------------------------------------------------: | :------------------------------------------------------------------------------------------------------------------------------------: |
| [<img src="https://dayu.oa.com/avatars/thinkchen/profile.jpg" width="90px;" /><br />thinkchen](https://git.code.oa.com/u/thinkchen) |     [<img src="https://dayu.oa.com/avatars/layxiang/profile.jpg" width="90px;" /><br />layxiang](https://git.code.oa.com/u/layxiang)      | [<img src="https://dayu.oa.com/avatars/ruikunai/profile.jpg" width="90px;" /><br />ruikunai](https://git.code.oa.com/u/ruikunai) | [<img src="https://dayu.oa.com/avatars/fancyzhong/profile.jpg" width="90px;" /><br />fancyzhong](https://git.code.oa.com/u/fancyzhong) |


## 常见问题

**Web环境可以使用吗**

A1: 不可以，可以安装web平台的sdk pmonitor-web-sdk

**Q2: 平台没有审核功能如何接入**

A: 审核功能是公司对运营平台的基本要求，没有审核功能的平台需要自己实现一套审核功能然后再接入。


## 团队介绍

- 前端：lreliawu

- 安全：jomliu


## 行为准则

- JS代码安全规范：https://git.woa.com/standards/security/blob/master/JavaScript%E5%AE%89%E5%85%A8%E8%A7%84%E8%8C%83.md
- CSS编码规范：https://git.woa.com/standards/css
- JavaScript/TypeScript 编码规范：https://git.woa.com/standards/javascript
- 谷歌工程实践Code Review：https://jimmysong.io/eng-practices/docs/review/reviewer/standard/

## 如何加入

企业微信联系@lreliawu

## 问题咨询

企业微信联系@lreliawu @jomliu

# 相关链接

- https://km.woa.com/articles/show/556911