{"name": "pmonitor-server", "version": "1.0.0", "main": "./build/index.js", "scripts": {"dev": "cross-env IS_LOCAL_MODE=true nodemon -e ts src/index.ts --files", "start": "cross-env IS_LOCAL_MODE=true nodemon -e ts ./build/index.js", "build": "rm -rf build && tsc --outDir ./build --noEmitOnError false --skipLib<PERSON>heck", "build:check": "rm -rf build && tsc --outDir ./build", "lint": "eslint ./src --ext ts", "lintfix": "eslint ./src --ext ts --fix", "test": "jest", "kill": "kill-port 5001"}, "dependencies": {"@koa/cors": "^3.3.0", "@tencent/anode-autoproxy": "^2.2.2", "@tencent/anode-http": "^2.3.11", "@tencent/anode-mw-polaris": "^2.2.4", "@tencent/anode-rpc": "^2.1.3", "@tencent/atta": "^0.1.3", "@tencent/rainbow-node-sdk": "^0.2.47", "@tencent/smart-proxy-koa2": "^1.0.1", "ansi-regex": "^6.0.1", "axios": "^0.21.4", "crypto-js": "^4.1.1", "dayjs": "1.11.13", "ejs": "^3.1.8", "formidable": "^2.0.1", "ioredis": "^4.28.0", "joi": "^17.4.2", "jose": "^5.8.0", "js-yaml": "^4.1.0", "koa": "^2.13.1", "koa-bodyparser": "^4.3.0", "koa-compose": "^4.1.0", "koa-logger": "^3.2.1", "koa-router": "^10.1.1", "koa-static": "^5.0.0", "koa-views": "^7.0.1", "lodash": "4.17.21", "log4js": "^6.3.0", "luxon": "^3.2.1", "mongoose": "^6.7.0", "mysql2": "^2.3.0", "nanoid": "^3.3.4", "node-schedule": "^2.1.0", "protobufjs": "^7.1.2", "qs": "6.12.1", "sequelize": "^6.29.0", "siz": "^1.2.2", "xss": "^1.0.13"}, "devDependencies": {"@tencent/dwt": "^3.9.3", "@tencent/dwt-reporter": "^3.8.0", "@types/crypto-js": "^4.1.1", "@types/formidable": "^2.0.0", "@types/ioredis": "^4.28.1", "@types/jest": "^26.0.24", "@types/js-yaml": "^4.0.5", "@types/koa": "^2.13.4", "@types/koa-bodyparser": "^4.3.3", "@types/koa-logger": "^3.1.2", "@types/koa-router": "^7.4.4", "@types/koa-static": "^4.0.2", "@types/koa__cors": "^3.0.3", "@types/node-schedule": "^1.3.2", "eslint-config-custom-qqnews": "workspace:^", "kill-port": "^2.0.1", "minimatch": "^5.1.0", "nodemon": "^2.0.12", "prettier": "^2.3.2", "ts-jest": "^27.0.4", "ts-node": "^10.2.0"}}