#!/usr/bin/env node

import { FastMCP } from 'fastmcp';
import { z } from 'zod';
import { getTemplates } from './services/templates.js';

const server = new FastMCP({
  name: 'maker-mcp',
  version: '1.0.0',
});

const envApiKey = process.env.API_KEY || '';

server.addTool({
  name: 'get_template',
  description: '获取搭建平台的模板，返回相关度最高的第一个模板JSON信息',
  parameters: z.object({
    keyword: z.string().describe('关键词，用于搜索模板'),
    apiKey: z.string().optional()
      .describe('API密钥，用于获取模板, 非必传，可从环境变量中获取'),
  }),
  execute: async ({ keyword, apiKey }) => {
    const templateInfo = await getTemplates(keyword, apiKey || envApiKey);
    return templateInfo;
  },
});

server.start({
  transportType: 'stdio',
});
