import crypto from 'crypto';
import axios from 'axios';
import { MaterialRes } from './types.js';

const SERVER_ENV = process.env.SERVER_ENV || 'prod';

const BASE_URL = SERVER_ENV === 'prod' ? 'http://maker.woa.com' : 'http://maker.testsite.woa.com';

export const getTemplates = async (keyword: string, apiKey: string) => {
  const timestamp = Date.now();
  const signature = crypto.createHmac('sha256', apiKey).update(timestamp.toString())
    .digest('hex');
  const response = await axios.get<MaterialRes>(`${BASE_URL}/makerapi/openapi/templates?keyword=${keyword}`, {
    headers: {
      'X-MCP-API-Key': 'maker-mcp',
      'X-MCP-Timestamp': timestamp.toString(),
      'X-MCP-Signature': signature,
      'Content-Type': 'application/json;charset=utf-8',
    },
  });
  const { code, data } = response.data;
  if (code !== 0) {
    return '{ "message": "未获取到模板信息" }';
  }
  const { list } = data || {};
  if (!list?.length) {
    return '{ "message": "未获取到模板信息" }';
  }
  const [templateInfo] = list;
  return templateInfo.materialConfig;
};
