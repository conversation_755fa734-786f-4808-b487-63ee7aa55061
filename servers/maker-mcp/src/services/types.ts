export interface Material {
  /** ID */
  id: number;
  /** 名称 */
  materialName: string;
  /** 类型 */
  materialType: number;
  /** 负责人 */
  owner: string;
  /** 业务ID */
  businessId: number;
  /** 业务 */
  business: number;
  /** 预览地址 */
  previewUrl: string;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
  /** 配置 */
  materialConfig: string;
  /** 配置类型 */
  configType: number;
  /** 链接 */
  materialUrl: string;
  /** 成员 */
  members: string[];
}

export interface MaterialRes {
  code: number;
  data: {
    list: Material[];
    total: number;
  };
  message: string;
}
