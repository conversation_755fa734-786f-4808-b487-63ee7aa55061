# Maker-MCP 服务

Maker-MCP 是一个基于 Model Context Protocol (MCP) 的服务，服务于新闻搭建平台，可获取新闻搭建平台的模板，为搭建助手提供获取模板的能力。

## 功能描述

Maker-MCP 服务为新闻搭建平台提供模板获取功能，通过关键词搜索返回相关度最高的模板信息。该服务提供以下主要功能：

- **get_template**: 根据关键词获取搭建平台的模板，返回相关度最高的第一个模板JSON信息

## 工具说明

### get_template

获取搭建平台的模板，返回相关度最高的第一个模板JSON信息。

**参数：**
- `keyword` (string, 必需): 关键词，用于搜索模板
- `apiKey` (string, 可选): API密钥，用于获取模板。如果不提供，将从环境变量 `API_KEY` 中获取

**返回：**
返回匹配的模板配置信息（materialConfig）

## 环境配置

### 环境变量

在使用服务前，需要配置以下环境变量：

```bash
# API密钥（必需）
API_KEY=your_api_key_here
```

## 安装与启动

### 安装依赖

```bash
npm install
```

### 开发环境启动

```bash
npm run dev
```

### 生产环境部署

```bash
npm run build
npm start
```

## API 接口

服务通过调用 `http://maker.woa.com/makerapi/openapi/templates` 接口获取模板数据，使用 HMAC-SHA256 签名进行身份验证。

## 依赖

- **fastmcp**: MCP 服务框架
- **axios**: HTTP 客户端
- **zod**: 参数验证
- **crypto**: 签名生成

## 系统要求

- Node.js >= 20.0.0
