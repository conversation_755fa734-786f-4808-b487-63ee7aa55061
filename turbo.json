{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env*"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**"]}, "build:hel": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**"]}, "lint": {"outputs": []}, "lint:fix": {"outputs": []}, "dev": {"cache": false}, "dev:hel": {"cache": false}, "start": {"cache": false}, "test": {}, "deploy": {"dependsOn": ["build", "test"]}, "tscheck": {}, "pub": {}, "pub:test": {}}}