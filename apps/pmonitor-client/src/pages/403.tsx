import { Button, Result, Space } from 'antd';
import { useNavigate } from 'react-router-dom';

export default function NoAuthPage() {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate('/field');
  };

  const handleGetAuth = () => {
    // projectId 需要根据申请的盘古项目而定
    window.open('https://pangu.woa.com/#/app/authApplication?projectId=1725&validPeriod=360', '_blank');
  };

  return (
    <Result
      status="403"
      title="403"
      subTitle="抱歉，您没有权限访问此页面"
      extra={(
        <Space>
          <Button type="primary" onClick={handleGetAuth}>
            申请权限
          </Button>
          <Button type="primary" onClick={handleClick}>
            返回首页
          </Button>
        </Space>
      )}
    />
  );
}
