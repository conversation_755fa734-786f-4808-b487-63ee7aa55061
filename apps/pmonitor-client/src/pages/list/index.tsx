import { useState, useMemo, useRef, useEffect, useCallback } from 'react';
import { PageContainer, ProColumns, ProTable, ModalForm, ProFormText, ProFormInstance, ActionType } from '@ant-design/pro-components';
import { Button, App } from 'antd';
import { useAtom } from 'helux';
import { getProjectList, addProject, updateProject } from '@/services';
import { userAtom } from '@/model/auth';
import { ListItem } from '@/types';

export default function ListPage() {
  const [userInfo] = useAtom(userAtom);
  const { name: userName } = userInfo;
  const { message } = App.useApp();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editRecord, setEditRecord] = useState<ListItem | null>(null);
  const formRef = useRef<ProFormInstance<ListItem>>(null);
  const actionRef = useRef<ActionType>(null);

  const isEdit = useMemo(() => !!editRecord, [editRecord]);

  const handleAdd = () => {
    setEditRecord(null);
    setIsModalOpen(true);
  };

  const handleEdit = (record: ListItem) => {
    setEditRecord(record);
    setIsModalOpen(true);
  };

  const initialValues = useMemo(() => editRecord || {
    platformId: '',
    platformName: '',
    owners: '',
  }, [editRecord]);

  useEffect(() => {
    if (isModalOpen && formRef.current) {
      formRef.current?.setFieldsValue(initialValues);
    }
  }, [isModalOpen, initialValues]);

  const handleInit = useCallback((_: ListItem, form: ProFormInstance<ListItem>) => {
    form.setFieldsValue(initialValues);
  }, [initialValues]);

  const handleSubmit = useCallback(async (values: ListItem) => {
    if (isEdit) {
      const res = await updateProject({
        id: editRecord?.id || 0,
        platformName: values.platformName,
        owners: values.owners,
      });
      if (res.success) {
        message.success('更新成功');
        setIsModalOpen(false);
      } else {
        message.error(res.message);
      }
    } else {
      const res = await addProject({
        platformName: values.platformName,
        platformId: values.platformId,
        owners: values.owners,
      });
      if (res.success) {
        message.success('添加成功');
        setIsModalOpen(false);
      } else {
        message.error(res.message);
      }
    }
    actionRef.current?.reload();
  }, [isEdit, editRecord]);

  const columns: ProColumns<ListItem>[] = [
    {
      title: '平台英文名称',
      dataIndex: 'platformId',
      align: 'center',
      width: 120,
    },
    {
      title: '平台描述',
      dataIndex: 'platformName',
      align: 'center',
      width: 120,
    },
    {
      title: 'git仓库地址',
      dataIndex: 'url',
      search: false,
      align: 'center',
      width: 120,
      render: (text, record) => <a href={record.url} target="_blank">{text}</a>,
    },
    {
      title: '负责人',
      dataIndex: 'owners',
      search: false,
      width: 200,
      ellipsis: true,
      align: 'center',
    },
    {
      title: '接入时间',
      dataIndex: 'createdAt',
      search: false,
      align: 'center',
      width: 200,
      valueType: 'dateTime',
    },
    {
      title: '操作',
      key: 'option',
      width: 120,
      valueType: 'option',
      align: 'center',
      render: (_, record) => {
        const { owners } = record;
        return (
          <Button type="link" disabled={!owners.includes(userName)} onClick={() => handleEdit(record)}>
            编辑
          </Button>
        );
      },
    },
  ];
  return (
    <PageContainer
      title={false}
    >
      <ProTable
        columns={columns}
        actionRef={actionRef}
        request={getProjectList}
        rowKey="id"
        dateFormatter="string"
        pagination={{
          pageSize: 10,
        }}
        search={{
          labelWidth: 'auto',
        }}
        toolBarRender={() => [
          <Button key="add" type="primary" onClick={handleAdd}>
            添加项目
          </Button>,
        ]}
      />
      <ModalForm
        title={isEdit ? '编辑项目' : '添加项目'}
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
        formRef={formRef}
        onInit={handleInit}
        onFinish={handleSubmit}
      >
        <ProFormText
          disabled={isEdit}
          rules={[
            { required: true, message: '请输入平台英文名称' },
            { pattern: /^[A-Za-z_]+$/, message: '请输入正确的平台英文名称' },
          ]}
          name="platformId"
          label="平台英文名称"
          fieldProps={{
            maxLength: 20,
          }}
        />
        <ProFormText name="platformName" label="平台描述" />
        <ProFormText
          rules={[
            { required: true, message: '请输入负责人' },
            { pattern: /^[A-Za-z_,]+$/, message: '请输入正确的负责人' },
          ]}
          name="owners"
          label="负责人"
        />
        {isEdit && <ProFormText disabled={isEdit} name="url" label="git仓库地址" />}
      </ModalForm>
    </PageContainer>
  );
}
