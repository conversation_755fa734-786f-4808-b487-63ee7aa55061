// 导出所有类型
export interface ProjectListParams {
  pageSize?: number;
  current?: number;
  platformName?: string;
  platformId?: string;
}

//  request params
export type AddProjectParams = {
  platformName: string;
  platformId: string;
  owners: string;
};
export type UpdateProjectParams = {
  id: number;
  platformName: string;
  owners: string;
};

export type ListItem = {
  id?: number;
  platformId: string;
  platformName: string;
  url?: string;
  owners: string;
  updatedAt?: string;
  createdAt?: string;
};

export interface CommonRes<T = any> {
  code: number;
  data: T;
  message?: string;
}

export interface ProjectListData {
  list: ListItem[];
  total: number;
}

export type ProjectListRes = CommonRes<ProjectListData>;
