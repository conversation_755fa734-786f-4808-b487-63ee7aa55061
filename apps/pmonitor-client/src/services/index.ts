// 导出所有服务
import { requestGet, requestPost } from '@packages/utils';
import { ProjectListParams, ProjectListRes, AddProjectParams, UpdateProjectParams, CommonRes } from '@/types';

export const getProjectList = async (params: ProjectListParams) => {
  try {
    const res = await requestGet<ProjectListRes>('/api/platform/list', params);
    return {
      success: res?.code === 0,
      data: res?.data?.list || [],
      total: res?.data?.total || 0,
    };
  } catch (error) {
    console.error(error);
    return {
      success: false,
      message: '获取项目列表失败',
      data: [],
      total: 0,
    };
  }
};

export const addProject = async (params: AddProjectParams) => {
  try {
    const res = await requestPost<CommonRes>('/api/platform/add', params);
    return {
      success: res?.code === 0,
      data: res?.data,
      message: res?.message,
    };
  } catch (error) {
    console.error(error);
    return {
      success: false,
      message: '添加项目失败',
      data: null,
    };
  }
};

export const updateProject = async (params: UpdateProjectParams) => {
  try {
    const res = await requestPost<CommonRes>('/api/platform/update', params);
    return {
      success: res?.code === 0,
      data: res?.data,
      message: res?.message,
    };
  } catch (error) {
    console.error(error);
    return {
      success: false,
      message: '更新项目失败',
      data: null,
    };
  }
};
