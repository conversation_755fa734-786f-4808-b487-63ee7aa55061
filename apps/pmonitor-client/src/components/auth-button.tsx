import { useMemo } from 'react';
import { Button, ButtonProps, Tooltip } from 'antd';
import { useAccess } from '@/hooks/useAccess';

export interface AuthButtonProps extends ButtonProps {
  auth?: number;
  authUsers?: string | string[];
  tooltip?: string;
}

export const AuthButton: React.FC<AuthButtonProps> = ({ auth, tooltip, authUsers, ...props }) => {
  const { hasAuth, userInfo } = useAccess();

  const hasUserAuth = useMemo(() => {
    if (authUsers) {
      return authUsers.includes(userInfo.name);
    }
    if (auth) {
      return hasAuth(auth);
    }
    return true;
  }, [authUsers, userInfo.name, hasAuth, auth]);

  const defaultTips = useMemo(() => {
    if (authUsers) {
      return `无权限，只有${Array.isArray(authUsers) ? authUsers.join('、') : authUsers}可以操作`;
    }
    return '无权限，请前往盘古申请';
  }, [authUsers]);

  if (hasUserAuth) {
    return <Button {...props} />;
  }
  return (
    <Tooltip title={tooltip || defaultTips}>
      <Button {...props} disabled />
    </Tooltip>
  );
};
