import { UnorderedListOutlined } from '@ant-design/icons';
import { MenuDataItem } from '@ant-design/pro-components';
import { Navigate } from 'react-router-dom';
import ListPage from '@/pages/list';
import NoAuthPage from '@/pages/403';
import NotFoundPage from '@/pages/404';
import ErrorPage from '@/pages/500';

export const ROUTES: MenuDataItem[] = [
  {
    path: '/',
    element: <Navigate to="/list" replace />,
  },
  {
    path: '/list',
    name: '接入管理',
    icon: <UnorderedListOutlined />,
    element: <ListPage />,
  },
  {
    path: '/403',
    element: <NoAuthPage />,
    hideInMenu: true,
  },
  {
    path: '/500',
    element: <ErrorPage />,
    hideInMenu: true,
  },
  {
    path: '*',
    element: <NotFoundPage />,
    hideInMenu: true,
  },
];
