# pmonitor-client

基于 client-template 创建的项目。

## 技术栈

- 🚀 React 19
- 📦 Vite 构建
- 🔧 TypeScript
- 🎨 Ant Design 5.x
- 📊 ProComponents
- 🔄 Helux 状态管理
- 🛣️ React Router 7
- 🔐 权限管理系统

## 特性

- ✨ 现代化的开发体验
- 📱 响应式布局
- 🔒 完整的权限控制
- 🎯 TypeScript 类型支持
- 🎨 可配置的主题
- 📝 统一的代码规范

## 快速开始

### 环境要求

- Node.js 18+
- pnpm 8.6+

### 安装依赖

```bash
pnpm install
```

### 开发

```bash
pnpm dev
```

### 构建

```bash
pnpm build
```

## 项目结构

```
src/
├── components/        # 公共组件
├── constants/         # 常量定义
├── hooks/            # 自定义 Hooks
├── model/            # 状态管理
├── pages/            # 页面组件
├── routes/           # 路由配置
├── services/         # API 服务
├── types/            # TypeScript 类型定义
└── utils/            # 工具函数
```

## 权限控制

项目实现了两层权限控制：

1. 路由权限：通过 `AuthWrapper` 组件控制页面访问权限
2. 按钮权限：使用 `useAccess` Hook 控制功能权限

### 使用示例

```tsx
// 路由权限
<AuthWrapper auth={allAuths.someAuth}>
  <SomePage />
</AuthWrapper>

// 按钮权限
const access = useAccess();
{access.hasAuth(allAuths.someAuth) && <Button>操作</Button>}
```

## 开发规范

- 遵循 ESLint 配置的代码规范
- 使用 TypeScript 强类型开发
- 组件采用函数式编程
- 状态管理使用 Helux

## 构建配置

- 开发环境：支持热更新
- 生产环境：代码压缩、Tree Shaking
- 支持环境变量配置
- 支持代理配置

## 目录别名

- `@/*` -> `src/*`

## 注意事项

1. 开发时请确保本地 hosts 已配置
2. 权限相关接口需要在测试环境调试
3. 提交代码前请确保通过 ESLint 检查

## 常见问题

### 1. 开发环境启动失败

检查：
- 端口是否被占用
- hosts 配置是否正确
- 依赖是否完整安装

### 2. 权限验证失败

检查：
- 用户登录状态
- 权限接口返回值
- 权限常量定义

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交变更
4. 发起 Pull Request

## 许可证

MIT License
