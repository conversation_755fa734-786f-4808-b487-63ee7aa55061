{"name": "client-template", "private": true, "version": "0.0.4", "scripts": {"dev": "cross-env DEV_SERVER=test vite", "dev:mock": "cross-env DEV_SERVER=mock vite", "dev:formal": "cross-env DEV_SERVER=formal vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/pro-components": "^2.8.6", "@packages/services": "workspace:*", "@packages/utils": "workspace:*", "@packages/types": "workspace:~", "@tencent/qn-request": "^3.0.3", "@tencent/qn-storage": "^2.1.2", "antd": "^5.24.1", "helux": "^4.4.2", "lodash": "4.17.21", "js-cookie": "^3.0.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router": "^7.2.0", "react-router-dom": "^7.2.0"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint-config-custom-qqnews": "workspace:^", "globals": "^15.14.0", "typescript": "~5.7.2", "vite": "^6.1.0", "tsconfig": "workspace:^"}, "volta": {"node": "20.16.0", "pnpm": "10.5.2"}}