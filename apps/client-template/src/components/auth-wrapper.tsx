import { Navigate } from 'react-router-dom';
import { ProSkeleton } from '@ant-design/pro-components';
import { useAccess } from '@/hooks/useAccess';
import { useAuthLoading } from '@/model/auth';

interface AuthWrapperProps {
  auth: number;
  children: React.ReactNode;
}

export function AuthWrapper({ auth, children }: AuthWrapperProps) {
  const access = useAccess();
  const { getAuths } = useAuthLoading();
  // 如果正在加载权限，显示加载状态
  if (getAuths.loading) {
    return (
      <ProSkeleton type="list" active />
    );
  }
  if (auth && !access.hasAuth(auth)) {
    return <Navigate to="/403" replace />;
  }
  return <>{children}</>;
}

