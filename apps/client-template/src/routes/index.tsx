import { HomeOutlined, UnorderedListOutlined } from '@ant-design/icons';
import { MenuDataItem } from '@ant-design/pro-components';
import { AuthWrapper } from '@/components/auth-wrapper';
import HomePage from '@/pages/home';
import ListPage from '@/pages/list';
import NoAuthPage from '@/pages/403';
import NotFoundPage from '@/pages/404';
import ErrorPage from '@/pages/500';
import { allAuths } from '@/constants/auth';

export const ROUTES: MenuDataItem[] = [
  {
    path: '/',
    name: '首页',
    icon: <HomeOutlined />,
    element: (
      <AuthWrapper auth={allAuths.fieldManagement}>
        <HomePage />
      </AuthWrapper>
    ),
    auth: allAuths.fieldManagement,
  },
  {
    path: '/list',
    name: '列表页',
    icon: <UnorderedListOutlined />,
    element: (
      <AuthWrapper auth={allAuths.producerManagement}>
        <ListPage />
      </AuthWrapper>
    ),
    auth: allAuths.producerManagement,
  },
  {
    path: '/403',
    element: <NoAuthPage />,
    hideInMenu: true,
  },
  {
    path: '/500',
    element: <ErrorPage />,
    hideInMenu: true,
  },
  {
    path: '*',
    element: <NotFoundPage />,
    hideInMenu: true,
  },
];
