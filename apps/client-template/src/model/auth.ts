import { share } from 'helux';
import Cookies from 'js-cookie';
import { fetchUserName, fetchRoleList, fetchAuths } from '@packages/services';
import type { UserInfo } from '@packages/types';
import { allAuths, allRoles } from '@/constants/auth';

const staffname = Cookies.get('staffname');

const [userAtom, setUserAtom, userCtx] = share<UserInfo>({
  name: staffname || '',
  avatar: `//r.hrc.woa.com/photo/100/${staffname}.png`,
  auths: [],
  roles: [],
  authLoading: true,
}, {
  moduleName: 'auth',
});

type Payloads = {
  setUserName: void;
  setRoleList: void;
  setAuths: void;
};

const { actions: authActions, useLoading: useAuthLoading } = userCtx.defineActions<Payloads>()({
  async setUserName({ draft }) {
    const res = await fetchUserName();
    draft.name = res;
    draft.avatar = `//r.hrc.woa.com/photo/100/${res}.png`;
  },
  async setRoleList({ draft }) {
    if (process.env.NODE_ENV === 'development') {
      draft.roles = Object.values(allRoles);
      return;
    }
    const ruleList = await fetchRoleList();
    draft.roles = ruleList.map(item => item.roleId);
  },
  async setAuths({ draft }) {
    if (process.env.NODE_ENV === 'development') {
      draft.auths = Object.values(allAuths);
      draft.authLoading = false;
      return;
    }
    const authList = await fetchAuths();
    draft.auths = authList.map(item => item.id);
    draft.authLoading = false;
  },
});

export {
  userAtom,
  authActions,
  setUserAtom,
  useAuthLoading,
};
