import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { RouterProvider, createBrowserRouter } from 'react-router-dom';
import './index.css';
import App from './App';
import { authActions } from './model/auth';
import { ROUTES } from './routes';

const router = createBrowserRouter([
  {
    path: '/',
    element: <App />,
    children: ROUTES,
  },
], {
  basename: '/',
});

// 在组件渲染前提前获取权限
authActions.setAuths();
authActions.setUserName();
authActions.setRoleList();

createRoot(document.getElementById('root')!).render(<StrictMode>
  <RouterProvider router={router} />
</StrictMode>);
