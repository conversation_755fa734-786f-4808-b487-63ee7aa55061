{"name": "field-management", "private": true, "version": "0.0.5", "scripts": {"dev": "cross-env DEV_SERVER=test vite", "dev:mock": "cross-env DEV_SERVER=mock vite", "dev:formal": "cross-env DEV_SERVER=formal vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/pro-components": "^2.8.7", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@monaco-editor/react": "^4.7.0", "@packages/components": "workspace:*", "@packages/services": "workspace:*", "@packages/types": "workspace:~", "@packages/utils": "workspace:*", "@tencent/qn-request": "^3.0.3", "@tencent/qn-storage": "^2.1.2", "@tencent/qn-utils": "^2.0.1", "ahooks": "^3.8.4", "antd": "^5.24.1", "dayjs": "1.11.13", "helux": "^4.4.2", "js-cookie": "^3.0.5", "lodash": "4.17.21", "monaco-editor": "^0.52.2", "nanoid": "^5.1.5", "react": "^19.0.0", "react-diff-viewer-continued": "^3.4.0", "react-dom": "^19.0.0", "react-router": "^7.2.0", "react-router-dom": "^7.2.0"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint-config-custom-qqnews": "workspace:^", "globals": "^15.14.0", "tsconfig": "workspace:^", "typescript": "~5.7.2", "vite": "^6.1.0"}, "volta": {"node": "20.16.0", "pnpm": "10.5.2"}}