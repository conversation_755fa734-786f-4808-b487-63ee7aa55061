import { resolve } from 'path';
import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';

const proxyTargetMap = {
  mock: 'https://yapi.pl.woa.com/mock/3691',
  test: 'https://newscontent.testsite.woa.com',
  formal: 'https://newscontent.woa.com',
};

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  const proxyTarget = proxyTargetMap[process.env.DEV_SERVER as keyof typeof proxyTargetMap];
  return {
    base: process.env.NODE_ENV === 'production' ? `https://mat1.gtimg.com/qqcdn/${process.env.CMS_APP_NAME}` : env.VITE_BASE_URL,
    plugins: [react()],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
      },
    },
    server: {
      host: true,
      allowedHosts: ['newscontent.testsite.woa.com', 'newscontent.woa.com'],
      port: 3001,
      open: false,
      // 允许跨域
      cors: true,
      proxy: {
        '/api': {
          target: proxyTarget,
          changeOrigin: true,
          rewrite: (path: string) => {
            if (process.env.DEV_SERVER === 'mock') {
              return path.replace('/api', '');
            }
            return path;
          },
        },
        '/renderapi': {
          target: proxyTargetMap.test,
          changeOrigin: true,
        },
      },
    },
    build: {
      // 设置最终构建的浏览器兼容目标
      target: 'es2015',
      // 构建后是否生成 source map 文件
      sourcemap: false,
      //  chunk 大小警告的限制（以 kbs 为单位）
      chunkSizeWarningLimit: 2000,
      // 启用/禁用 gzip 压缩大小报告
      reportCompressedSize: true,
      outDir: resolve(__dirname, 'build'),
    },
  };
});
