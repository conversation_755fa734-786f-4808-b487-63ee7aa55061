import { Navigate } from 'react-router-dom';
import { useAtom } from 'helux';
import { ProSkeleton } from '@ant-design/pro-components';
import { useAccess } from '@/hooks/useAccess';
import { userAtom } from '@/model/auth';

interface AuthWrapperProps {
  auth?: number;
  children: React.ReactNode;
  adminAccess?: boolean;
  adminOnly?: boolean;
  authUsers?: string | string[];
  authUserLoading?: boolean;
}

export function AuthWrapper({ auth, children, adminAccess, adminOnly, authUsers, authUserLoading }: AuthWrapperProps) {
  const access = useAccess();
  const isAdminRole = access.isAdmin();
  const [authState] = useAtom(userAtom);
  // 如果正在加载权限，显示加载状态
  if (authState.authLoading || authUserLoading) {
    return (
      <ProSkeleton type="list" active />
    );
  }
  if (adminOnly) {
    return isAdminRole ? <>{children}</> : <Navigate to="/403" replace />;
  }
  if (adminAccess && isAdminRole) {
    return <>{children}</>;
  }
  if (auth && !access.hasAuth(auth)) {
    return <Navigate to="/403" replace />;
  }
  const formattedAuthUsers = Array.isArray(authUsers) ? authUsers : authUsers?.split(';');
  if (formattedAuthUsers?.length && !formattedAuthUsers.includes(access.userInfo.name)) {
    return <Navigate to="/403" replace />;
  }
  return <>{children}</>;
}

