import { Spin, Typography, Progress } from 'antd';
import { useAtom } from 'helux';
import { useEffect, useState } from 'react';
import { userAtom } from '@/model/auth';

const { Title, Text } = Typography;

export function GlobalLoading() {
  const [authState] = useAtom(userAtom);
  const [progress, setProgress] = useState(0);
  const [loadingText, setLoadingText] = useState('正在初始化应用...');

  useEffect(() => {
    if (!authState.authLoading) {
      return;
    }

    const steps = [
      { text: '正在获取用户信息...', duration: 1000 },
      { text: '正在获取权限配置...', duration: 2000 },
      { text: '正在初始化界面...', duration: 1000 },
    ];

    let currentStep = 0;
    let currentProgress = 0;

    const updateProgress = () => {
      if (currentStep < steps.length) {
        setLoadingText(steps[currentStep].text);
        const targetProgress = ((currentStep + 1) / steps.length) * 100;

        const progressInterval = setInterval(() => {
          currentProgress += 2;
          setProgress(Math.min(currentProgress, targetProgress));

          if (currentProgress >= targetProgress) {
            clearInterval(progressInterval);
            currentStep += 1;
            setTimeout(updateProgress, 200);
          }
        }, 50);
      }
    };

    updateProgress();
  }, [authState.authLoading]);

  if (!authState.authLoading) {
    return null;
  }

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#fff',
        zIndex: 9999,
      }}
    >
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          padding: '40px',
          borderRadius: '8px',
          backgroundColor: '#fafafa',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
          minWidth: '320px',
        }}
      >
        <Spin size="large" />
        <div style={{ marginTop: 24, textAlign: 'center', width: '100%' }}>
          <Title level={4} style={{ marginBottom: 8, color: '#1890ff' }}>
            总库管理系统
          </Title>
          <Text type="secondary" style={{ fontSize: '14px' }}>
            {loadingText}
          </Text>
          <div style={{ marginTop: 16, width: '100%' }}>
            <Progress
              percent={progress}
              showInfo={false}
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
