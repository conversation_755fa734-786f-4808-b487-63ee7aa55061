import { useState, useEffect, useMemo } from 'react';
import uniqBy from 'lodash/uniqBy';
import { App, Modal, Button, Space, Table, Typography, Tooltip } from 'antd';
import { CodeEditor } from '@packages/components';
import { useMemoizedFn } from 'ahooks';
import { EditOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { FieldClass, FieldSchemaUpdateData } from '@/types/api';
import { fetchAllFields } from '@/services/field';
import { VALUE_TYPE_MAP, ValueType } from '@/constants/field';
import AddFieldModal from './add-field-modal';

// 定义组件接口
interface BatchAddFieldModalProps {
  open: boolean;
  fieldClass?: FieldClass;
  entityType?: string;
  readyAddFields?: FieldSchemaUpdateData[];
  onConfirm: (fields: FieldSchemaUpdateData[]) => void;
  onCancel: () => void;
}

// 定义类型接口
interface AnalyzedField {
  field_path: string;
  src_field_path?: string;
  field_name?: string;
  value_type: string;
  exists: boolean; // 是否已存在
  isEditing?: boolean; // 是否正在编辑
  // 添加其他标准字段属性
  entity_type?: string;
  column_family?: string;
  description?: string;
  example?: string;
  produce_logic?: string;
  owner?: string;
  tapd?: string;
  validation?: string;
}

// 分析JSON输入示例
const sampleJson = `{
    "user": {
      "id": 123,
      "name": "张三",
      "tags": ["前端", "后端"],
      "profile": {
        "age": 28,
        "address": "深圳市南山区"
      },
      "orders": [
        {
          "id": 1001,
          "price": 99.8
        }
      ]
    }
  }`;

/**
 * 批量添加字段弹窗组件
 */
const BatchAddFieldModal = (props: BatchAddFieldModalProps) => {
  const { open, fieldClass, entityType, readyAddFields, onConfirm, onCancel } = props;
  const { message } = App.useApp();
  const [jsonInput, setJsonInput] = useState<string>(sampleJson);
  const [analyzedFields, setAnalyzedFields] = useState<AnalyzedField[]>([]);
  const [existingFields, setExistingFields] = useState<FieldSchemaUpdateData[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [editingField, setEditingField] = useState<FieldSchemaUpdateData | null>(null);
  const [addModalVisible, setAddModalVisible] = useState(false);

  // 当打开弹窗时重置状态
  useEffect(() => {
    if (open) {
      setJsonInput(sampleJson);
      setAnalyzedFields([]);
    }
  }, [open]);

  // 获取所有字段
  useEffect(() => {
    if (entityType && fieldClass) {
      fetchAllFields({
        entity_type: entityType,
        field_class: fieldClass,
        current: 1,
        pageSize: -1,
      }).then((res) => {
        if (res.success) {
          setExistingFields(uniqBy([
            ...res.data,
            ...(readyAddFields || []),
          ], 'field_path'));
        }
      });
    }
  }, [entityType, fieldClass, readyAddFields]);

  // 分析JSON输入，提取字段结构和类型
  const analyzeJson = useMemoizedFn(async () => {
    if (!jsonInput.trim()) {
      message.error('请输入JSON数据');
      return;
    }
    try {
      setIsAnalyzing(true);
      // 解析JSON
      const jsonObj = JSON.parse(jsonInput);
      // 分析字段结构
      const fields = analyzeJsonStructure(jsonObj);
      // 检查字段是否已存在
      if (existingFields.length > 0) {
        fields.forEach((field) => {
          // 检查完整路径是否存在
          field.exists = existingFields.some(existField => existField.field_path === field.field_path);
        });
      }
      // 过滤已存在的字段
      const filteredFields = fields.filter(field => !field.exists);
      setAnalyzedFields(filteredFields);
      setIsAnalyzing(false);
      message.success(`分析完成，发现 ${fields.length} 个字段，其中 ${fields.length - filteredFields.length} 个已存在`);
    } catch (error) {
      message.error('JSON解析失败，请检查格式');
      setIsAnalyzing(false);
    }
  });

  // 分析JSON结构，递归提取字段路径和类型
  const analyzeJsonStructure = (json: any, parentPath = ''): AnalyzedField[] => {
    const fields: AnalyzedField[] = [];
    // 处理根对象
    if (!parentPath) {
      // 检查是否为数组
      if (Array.isArray(json)) {
        // 处理数组，取第一个元素作为样本
        if (json.length > 0) {
          // 确定数组元素类型
          if (typeof json[0] === 'object' && json[0] !== null) {
            // 对象数组，继续分析结构
            return analyzeJsonStructure(json[0], '');
          }
          // 基本类型数组
          return [{
            field_name: '',
            column_family: '',
            field_path: 'root',
            value_type: getValueType(json),
            exists: false,
            entity_type: entityType,
          }];
        }
        return [];
      }
    }

    // 处理对象
    if (typeof json === 'object' && json !== null) {
      // 遍历对象属性
      for (const key in json) {
        if (Object.prototype.hasOwnProperty.call(json, key)) {
          const value = json[key];
          const currentPath = parentPath ? `${parentPath}.${key}` : key;
          // 添加当前字段
          fields.push({
            field_path: currentPath,
            src_field_path: currentPath,
            value_type: getValueType(value),
            exists: false,
            entity_type: entityType,
            column_family: '',
            field_name: '',
            // 设置基本的字段信息
            description: `${key}字段`,
            example: JSON.stringify({
              [key]: value,
            }),
          });

          // 递归处理嵌套对象或数组
          if (typeof value === 'object' && value !== null) {
            if (Array.isArray(value)) {
              // 如果是数组且有元素
              if (value.length > 0) {
                if (typeof value[0] === 'object' && value[0] !== null) {
                  // 处理对象数组的第一个元素
                  const nestedFields = analyzeJsonStructure(value[0], currentPath);
                  fields.push(...nestedFields);
                }
              }
            } else {
              // 处理嵌套对象
              const nestedFields = analyzeJsonStructure(value, currentPath);
              fields.push(...nestedFields);
            }
          }
        }
      }
    }
    return fields;
  };

  // 根据值确定字段类型
  const getValueType = (value: any): string => {
    if (value === null || value === undefined) {
      return ValueType.STRING; // 默认为字符串
    }

    if (Array.isArray(value)) {
      if (value.length === 0) {
        return ValueType.ARRAY;
      }
      const firstItem = value[0];
      if (typeof firstItem === 'number') {
        return value.every(item => Number.isInteger(item)) ? ValueType.INT_ARRAY : ValueType.FLOAT_ARRAY;
      } if (typeof firstItem === 'string') {
        return ValueType.STRING_ARRAY;
      } if (typeof firstItem === 'object' && firstItem !== null) {
        return ValueType.OBJECT_ARRAY;
      }
      return ValueType.ARRAY;
    }
    if (typeof value === 'number') {
      return Number.isInteger(value) ? ValueType.INT : ValueType.FLOAT;
    } if (typeof value === 'string') {
      return ValueType.STRING;
    } if (typeof value === 'object') {
      return ValueType.OBJECT;
    } if (typeof value === 'boolean') {
      return ValueType.BOOL;
    }
    return ValueType.STRING; // 默认为字符串
  };

  // 处理编辑字段
  const handleEditField = useMemoizedFn((record: AnalyzedField) => {
    setEditingField({
      ...record,
      entity_type: entityType,
    });
    setAddModalVisible(true);
  });

  // 处理编辑完成
  const handleEditFinish = useMemoizedFn(async (fieldData: FieldSchemaUpdateData) => {
    const newFields = analyzedFields.map((filed) => {
      if (filed.field_path === fieldData.field_path) {
        return {
          ...filed,
          ...fieldData,
        };
      }
      return filed;
    });
    setAnalyzedFields(newFields);
    setAddModalVisible(false);
    setEditingField(null);
    return true;
  });

  // 处理确认添加
  const handleConfirm = useMemoizedFn(() => {
    // 转换为标准字段格式
    const fields: FieldSchemaUpdateData[] = analyzedFields.map(field => ({
      entity_type: entityType,
      field_path: field.field_path,
      src_field_path: field.src_field_path || field.field_path,
      field_name: field.field_name,
      value_type: field.value_type,
      column_family: field.column_family,
      description: field.description || `${field.field_name}字段`,
      example: field.example || '',
      produce_logic: field.produce_logic || '',
      owner: field.owner || '',
      tapd: field.tapd || '',
      validation: field.validation || '',
    }));
    // 校验每个字段是否存在column_family、owner、field_name、description、tapd
    const isValid = fields.every(({
      column_family,
      owner,
      field_name,
      description,
      tapd,
    }) => column_family && owner && field_name && description && tapd);
    if (!isValid) {
      message.error('请检查每个字段是否存在column_family、owner、field_name、description、tapd');
      return;
    }
    // 调用确认回调
    onConfirm(fields);
  });

  // 处理取消
  const handleCancel = useMemoizedFn(() => {
    // 清空状态
    setJsonInput('');
    setAnalyzedFields([]);
    onCancel();
  });

  // 获取缺失的父级路径
  const getMissingParentPaths = useMemoizedFn((field_path: string) => {
    const segments = field_path.split('.');
    if (segments.length <= 1) return [];

    // 构建所有父级路径
    const parentPaths = [];
    for (let i = 1; i < segments.length; i++) {
      parentPaths.push(segments.slice(0, i).join('.'));
    }

    // 找出缺失的父级路径
    return parentPaths.filter((path) => {
      const existsInDB = existingFields.some(f => f.field_path === path);
      const existsInCurrent = analyzedFields.some(f => f.field_path === path);
      return !existsInDB && !existsInCurrent;
    });
  });

  // 表格列配置
  const columns = useMemo(() => [
    {
      title: '介质类型',
      dataIndex: 'entity_type',
      key: 'entity_type',
    },
    {
      title: '列簇名称',
      dataIndex: 'column_family',
      key: 'column_family',
      render: (text: string) => text || '--',
    },
    {
      title: '字段路径',
      dataIndex: 'field_path',
      key: 'field_path',
      render: (text: string) => {
        const missingPaths = getMissingParentPaths(text);
        const hasParent = missingPaths.length === 0;
        return hasParent ? (
          <span>{text}</span>
        ) : (
          <Tooltip title={`缺少父级字段: ${missingPaths.join(', ')}`}>
            <Typography.Text type="danger">
              {text} <QuestionCircleOutlined />
            </Typography.Text>
          </Tooltip>
        );
      },
    },
    {
      title: '字段名称',
      dataIndex: 'field_name',
      key: 'field_name',
      render: (text: string) => text || '--',
    },
    {
      title: '数据类型',
      dataIndex: 'value_type',
      key: 'value_type',
      render: (text: string) =>
        // 修复类型错误：将字符串类型转换为ValueType类型
        VALUE_TYPE_MAP[text as unknown as ValueType] || text,
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: AnalyzedField) => (
        <Button
          type="link"
          icon={<EditOutlined />}
          onClick={() => handleEditField(record)}
        >
          编辑
        </Button>
      ),
    },
  ], [analyzedFields, existingFields]);

  return (
    <>
      <Modal
        title="批量添加字段"
        open={open}
        width={800}
        onCancel={handleCancel}
        footer={[
          <Button key="cancel" onClick={handleCancel}>
            取消
          </Button>,
          <Button
            key="analyze"
            type="primary"
            loading={isAnalyzing}
            onClick={analyzeJson}
          >
            分析字段
          </Button>,
          <Button
            key="confirm"
            type="primary"
            disabled={analyzedFields.length === 0}
            onClick={handleConfirm}
          >
            确认添加 ({analyzedFields.length})
          </Button>,
        ]}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Typography.Title level={5}>
            输入JSON格式数据进行字段分析
            <Tooltip title="输入JSON数据后点击分析字段，系统将自动解析JSON结构并识别字段类型">
              <QuestionCircleOutlined style={{ marginLeft: 8 }} />
            </Tooltip>
          </Typography.Title>
          <div style={{ marginBottom: 16 }}>
            <CodeEditor
              language="json"
              value={jsonInput}
              onChange={(value?: string) => setJsonInput(value || '')}
              height={300}
            />
          </div>
          <Typography.Title level={5}>分析结果</Typography.Title>
          <Table
            rowKey="field_path"
            dataSource={analyzedFields}
            columns={columns}
            pagination={false}
            size="small"
            scroll={{ y: 300 }}
          />
          <Typography.Text type="secondary" style={{ marginTop: 8, display: 'block' }}>
            红色标记的字段缺少父级字段，可能导致添加失败，建议先添加父级字段
          </Typography.Text>
        </Space>
      </Modal>

      {/* 编辑字段弹窗 */}
      <AddFieldModal
        open={addModalVisible}
        fieldClass={fieldClass}
        entityType={entityType}
        editingField={editingField}
        readyAddFields={analyzedFields}
        onFinish={handleEditFinish}
        onOpenChange={setAddModalVisible}
      />
    </>
  );
};

export default BatchAddFieldModal;
