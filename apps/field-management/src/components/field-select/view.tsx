import { useState, memo, useMemo, useEffect } from 'react';
import { App, Button } from 'antd';
import { ProTable, ProColumns } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { fetchAllFields, fetchColumnFamilies, fetchEntityTypes } from '@/services/field';
import { SELECT_FIELD_COLUMNS } from '@/constants/field';
import { getFieldsByString } from '@/utils';
import { FieldDetail, FieldClass } from '@/types/api';
import type { OptionItem } from '@/types/common';

interface FieldViewProps {
  fieldClass?: FieldClass;
  fieldNames?: string[];
  onlyDependentFields?: string[];
  entityType?: string;
  isConsumer?: boolean;
}

export default memo((props: FieldViewProps) => {
  const { fieldClass, entityType, fieldNames, onlyDependentFields, isConsumer } = props;
  const { message } = App.useApp();
  const [loading, setLoading] = useState(false);
  const [fields, setFields] = useState<FieldDetail[]>([]);
  const [columnFamilies, setColumnFamilies] = useState<OptionItem[]>([]);
  const [entityTypes, setEntityTypes] = useState<OptionItem[]>([]);

  const handleSearch = useMemoizedFn(async () => {
    setLoading(true);
    try {
      const res = await fetchAllFields({
        field_class: fieldClass,
        entity_type: entityType,
        current: 1,
        pageSize: -1,
      });
      setFields(res.data);
    } catch (error) {
      message.error('查询失败');
    } finally {
      setLoading(false);
    }
  });

  useEffect(() => {
    fetchColumnFamilies({
      field_class: fieldClass,
      entity_type: entityType,
    }).then((res) => {
      setColumnFamilies(res);
    });
    fetchEntityTypes({
      field_class: fieldClass,
    }).then((res) => {
      setEntityTypes(res);
    });
    handleSearch();
  }, [fieldClass, entityType]);

  const dataSource = useMemo(() => {
    const allFieldNames = [...(onlyDependentFields || []), ...(fieldNames || [])];
    if (allFieldNames?.length) {
      const showFields = getFieldsByString(allFieldNames, fields, !isConsumer);
      return showFields.map(item => ({
        ...item,
        isDependentOnly: onlyDependentFields?.includes(item.field_path || ''),
      }));
    }
    return [];
  }, [fieldNames, onlyDependentFields, fields, isConsumer]);

  const handleDetail = useMemoizedFn((record: FieldDetail) => {
    const { field_class: fieldClass, entity_type: entityType } = record;
    window.open(`${location.origin}/field/detail/${record.field_path}?field_class=${fieldClass}&entity_type=${entityType}`, '_blank');
  });

  // 自定义列配置，添加删除操作
  const columns: ProColumns<FieldDetail>[] = useMemo(() => [
    ...(SELECT_FIELD_COLUMNS.map((column) => {
      if (column.dataIndex === 'entity_type') {
        return {
          ...column,
          request: undefined,
          fieldProps: {
            options: entityTypes,
          },
        };
      }
      if (column.dataIndex === 'column_family') {
        return {
          ...column,
          request: undefined,
          fieldProps: {
            options: columnFamilies,
          },
        };
      }
      return column;
    })),
    {
      title: '是否仅依赖字段(不下发)',
      dataIndex: 'isDependentOnly',
      valueType: 'switch',
      width: 100,
      hideInTable: !onlyDependentFields?.length,
      fieldProps: {
        checkedChildren: '是',
        unCheckedChildren: '否',
      },
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 100,
      valueType: 'option',
      render: (_, record) => (
        <Button type="link" onClick={() => handleDetail(record)}>详情</Button>
      ),
    },
  ], [entityTypes, columnFamilies, onlyDependentFields]);

  return (
    <ProTable<FieldDetail>
      search={false}
      options={false}
      dataSource={dataSource}
      loading={loading}
      rowKey='field_path'
      size="small"
      bordered
      columns={columns}
      style={{ marginTop: 16 }}
      scroll={{ x: 'max-content', y: 300 }}
      pagination={{
        pageSize: 20,
        showSizeChanger: false,
      }}
    />
  );
});
