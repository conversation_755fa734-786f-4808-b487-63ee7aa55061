import { useState, useEffect, memo, useMemo, useRef, useCallback } from 'react';
import { <PERSON>ton, Card, Space, App, Checkbox } from 'antd';
import { useMemoizedFn } from 'ahooks';
import { ProTable, ProColumns } from '@ant-design/pro-components';
import { getFieldsByString } from '@/utils';
import { fetchAllFields, fetchEntityTypes, fetchColumnFamilies } from '@/services/field';
import { SELECT_FIELD_COLUMNS } from '@/constants/field';
import { FieldDetail, FieldClass, FieldStatus } from '@/types/api';
import type { OptionItem } from '@/types/common';
import FieldSelectModal from './select-field-modal';

export type FieldDetailWithPublish = FieldDetail & {
  isDependentOnly: boolean;
};

export type ValyeType = {
  /** 字段路径列表 */
  fields: string[];
  /** 依赖但不下发的字段列表 */
  dependent_fields?: string[];
};
export interface FieldSelectProps {
  hasDependentOnly?: boolean;
  fieldClass?: FieldClass;
  entityType?: string;
  value?: string[] | ValyeType;
  isConsumer?: boolean;
  skipClearOnParamsChange?: boolean;
  onChange?: (value: string[] | ValyeType) => void;
}

export default memo((props: FieldSelectProps) => {
  const { fieldClass, entityType, value, onChange, isConsumer, hasDependentOnly, skipClearOnParamsChange } = props;
  const { message } = App.useApp();
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [columnFamilies, setColumnFamilies] = useState<OptionItem[]>([]);
  const [entityTypes, setEntityTypes] = useState<OptionItem[]>([]);
  const [allFields, setAllFields] = useState<FieldDetail[]>([]);

  // 使用ref记录上一次的查询参数
  const prevParamsRef = useRef({
    fieldClass,
    entityType,
  });

  const getAllFields = (value?: string[] | ValyeType) => {
    if (!value) return [];
    if (Array.isArray(value)) {
      return value;
    }
    const fields = [...(value?.dependent_fields || []), ...(value?.fields || [])];
    return Array.from(new Set(fields));
  };

  const formatFields = useMemo(() => getAllFields(value), [value]);

  // 获取已选字段详情
  const fetchSelectedFields = useMemoizedFn(async () => {
    if (!fieldClass || !entityType) {
      return;
    }
    setLoading(true);
    try {
      const res = await fetchAllFields({
        field_class: fieldClass,
        entity_type: entityType,
        current: 1,
        pageSize: -1,
        status: FieldStatus.FIELD_STATUS_ONLINE,
      });
      setAllFields(res.data);
    } catch (error) {
      message.error('获取已选字段失败');
    } finally {
      setLoading(false);
    }
  });

  const dataSource = useMemo(() => getFieldsByString(formatFields, allFields, !isConsumer), [allFields, formatFields]);

  // 检查查询参数是否发生变化
  const checkParamsChanged = useCallback(() => {
    const prevParams = prevParamsRef.current;
    const paramsChanged = prevParams.fieldClass !== fieldClass
      || prevParams.entityType !== entityType;
    // 更新上一次的参数
    prevParamsRef.current = {
      fieldClass,
      entityType,
    };
    return paramsChanged;
  }, [fieldClass, entityType]);

  useEffect(() => {
    if (!fieldClass || !entityType) {
      return;
    }
    fetchColumnFamilies({
      field_class: fieldClass,
      entity_type: entityType,
    }).then((res) => {
      setColumnFamilies(res);
    });
    fetchEntityTypes({
      field_class: fieldClass,
    }).then((res) => {
      setEntityTypes(res);
    });
    fetchSelectedFields();
  }, [fieldClass, entityType]);

  useEffect(() => {
    // 检查查询参数是否变化
    const paramsChanged = checkParamsChanged();
    // 如果参数变化且不跳过清空，则清空已选字段
    if (paramsChanged && !skipClearOnParamsChange && onChange) {
      if (hasDependentOnly) {
        onChange({
          fields: [],
          dependent_fields: [],
        });
      } else {
        onChange([]);
      }
      return;
    }
  }, [checkParamsChanged, skipClearOnParamsChange, hasDependentOnly]);

  // 处理新增字段
  const handleAddFields = useMemoizedFn((fields: FieldDetail[]) => {
    // 合并新旧字段，去重
    const newFieldNames = fields.map(field => field.field_path as string);
    // 调用onChange回调
    if (hasDependentOnly) {
      const formatValue = value as ValyeType;
      const mergedFieldNames = [...new Set([...(formatValue?.fields || []), ...newFieldNames])];
      onChange?.({
        fields: mergedFieldNames,
        dependent_fields: formatValue?.dependent_fields || [],
      });
    } else {
      const mergedFieldNames = [...new Set([...(formatFields || []), ...newFieldNames])];
      onChange?.(mergedFieldNames);
    }
    setModalVisible(false);
  });

  // 处理删除字段
  const handleRemoveField = useMemoizedFn((fieldPath: string) => {
    if (!value) return;
    if (hasDependentOnly) {
      const formatValue = value as ValyeType;
      const newFields = formatValue.fields.filter(name => name !== fieldPath);
      const newDependentFields = formatValue.dependent_fields?.filter(name => name !== fieldPath);
      onChange?.({
        fields: newFields,
        dependent_fields: newDependentFields,
      });
    } else {
      const newFields = (value as string[]).filter(name => name !== fieldPath);
      onChange?.(newFields);
    }
  });

  const handleDetail = useMemoizedFn((record: FieldDetail) => {
    const { field_class: fieldClass, entity_type: entityType } = record;
    window.open(`${location.origin}/field/detail/${record.field_path}?field_class=${fieldClass}&entity_type=${entityType}`, '_blank');
  });

  const handlePubChange = useMemoizedFn((record: FieldDetailWithPublish) => {
    if (Array.isArray(value)) {
      return;
    }
    const { field_path, isDependentOnly } = record;
    const newDependentFields = isDependentOnly
      ? [...(value?.dependent_fields || []), field_path]
      : (value?.dependent_fields || []).filter(name => name !== field_path);
    const newFields = isDependentOnly
      ? (value?.fields || []).filter(name => name !== field_path)
      : [...(value?.fields || []), field_path];
    onChange?.({
      fields: newFields as string[],
      dependent_fields: newDependentFields as string[],
    });
  });

  // 自定义列配置，添加删除操作
  const columns: ProColumns<FieldDetail>[] = useMemo(() => [
    ...(SELECT_FIELD_COLUMNS.map((column) => {
      if (column.dataIndex === 'entity_type') {
        return {
          ...column,
          request: undefined,
          fieldProps: () => ({
            options: entityTypes,
          }),
        };
      }
      if (column.dataIndex === 'column_family') {
        return {
          ...column,
          request: undefined,
          fieldProps: () => ({
            options: columnFamilies,
          }),
        };
      }
      return column;
    })),
    {
      title: '是否仅依赖字段(不下发)',
      key: 'isDependentOnly',
      valueType: 'switch',
      width: 100,
      hideInTable: !hasDependentOnly,
      render: (_: any, record: FieldDetail) => {
        const { field_path } = record;
        const isChecked = !Array.isArray(value) && Boolean(field_path && value?.dependent_fields?.includes(field_path));
        return (
          <Checkbox
            checked={isChecked}
            onChange={(e) => {
              handlePubChange({
                ...record,
                isDependentOnly: e.target.checked,
              });
            }}
          />
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      valueType: 'option',
      width: 100,
      render: (_: any, record: FieldDetail) => (
        <Space>
          <Button
            type="link"
            danger
            style={{ padding: 0 }}
            onClick={() => handleRemoveField(record.field_path as string)}
          >
            删除
          </Button>
          <Button
            type="link"
            style={{ padding: 0 }}
            onClick={() => handleDetail(record)}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ], [entityTypes, columnFamilies, value]);

  const handleAddField = useMemoizedFn(() => {
    if (fieldClass == null || !entityType) {
      message.error('请先选择订阅字段类型和介质类型');
      return;
    }
    setModalVisible(true);
  });

  return (
    <>
      <Card
        title="已选字段"
        extra={(
          <Button type="primary" onClick={handleAddField}>
            勾选字段
          </Button>
        )}
      >
        {dataSource.length > 0 ? (
          <ProTable<FieldDetail>
            dataSource={dataSource}
            loading={loading}
            rowKey='field_path'
            columns={columns}
            scroll={{ x: 'max-content', y: 300 }}
            pagination={{
              showSizeChanger: true,
            }}
            search={false}
            options={false}
          />
        ) : (
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            暂无已选字段，请点击（勾选字段）按钮添加
          </div>
        )}
      </Card>
      <FieldSelectModal
        open={modalVisible}
        fieldClass={fieldClass}
        entityType={entityType}
        isConsumer={isConsumer}
        initFields={dataSource}
        onConfirm={handleAddFields}
        onCancel={() => setModalVisible(false)}
      />
    </>
  );
});
