import { useState, useEffect, useMemo, useRef, memo } from 'react';
import { Modal, Button } from 'antd';
import { ProTable, ProFormInstance, ProColumns, ActionType } from '@ant-design/pro-components';
import { useMemoizedFn } from 'ahooks';
import { fetchAllFields, fetchEntityTypes } from '@/services/field';
import { SELECT_FIELD_COLUMNS } from '@/constants/field';
import { FieldDetail, FieldClass, GetFieldProducibleRequest, FieldStatus } from '@/types/api';
import { TablePage } from '@/types/common';

interface FieldSelectProps {
  fieldClass?: FieldClass;
  entityType?: string;
  isConsumer?: boolean;
  // 是否排除当前介质类型
  excludeCurrentEntityType?: boolean;
  open?: boolean;
  // 已选字段
  initFields?: FieldDetail[];
  onConfirm?: (value: FieldDetail[]) => void;
  onCancel?: () => void;
}

export default memo((props: FieldSelectProps) => {
  const { fieldClass, entityType, open, initFields, excludeCurrentEntityType, onConfirm, onCancel } = props;
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [selectedFields, setSelectedFields] = useState<FieldDetail[]>([]);
  const formRef = useRef<ProFormInstance<FieldDetail>>(undefined);
  const actionRef = useRef<ActionType>(undefined);

  const requestFields = useMemoizedFn(async (params: GetFieldProducibleRequest & TablePage) => {
    if (excludeCurrentEntityType && !params.entity_type) {
      const entityTypes = await fetchEntityTypes({
        field_class: fieldClass,
        currentEntityType: entityType,
      });
      const [firstEntityType] = entityTypes;
      formRef.current?.setFieldsValue({
        entity_type: firstEntityType?.value,
      });
      params.entity_type = firstEntityType?.value;
    } else if (!excludeCurrentEntityType) {
      formRef.current?.setFieldsValue({
        entity_type: entityType,
      });
    }
    const res = await fetchAllFields({
      ...params,
      field_class: fieldClass,
      entity_type: excludeCurrentEntityType ? params.entity_type : entityType,
      status: FieldStatus.FIELD_STATUS_ONLINE,
    });
    return res;
  });

  // 弹窗打开时加载数据
  useEffect(() => {
    if (open) {
      actionRef.current?.reload?.();
      if (initFields) {
        setSelectedKeys(initFields?.map(field => field?.field_path || ''));
        setSelectedFields(initFields);
      }
    }
  }, [open, initFields, fieldClass, excludeCurrentEntityType, entityType]);

  const handleRowSelectionChange = useMemoizedFn((selectedRowKeys: React.Key[], selectedRows: FieldDetail[]) => {
    const filteredSelectedRows = selectedRows.filter(row => row);
    setSelectedKeys(selectedRowKeys);
    setSelectedFields(filteredSelectedRows);
  });

  const handleConfirm = useMemoizedFn(() => {
    onConfirm?.(selectedFields);
  });

  const handleDetail = useMemoizedFn((record: FieldDetail) => {
    const { field_class: fieldClass, entity_type: entityType } = record;
    window.open(`${location.origin}/field/detail/${record.field_path}?field_class=${fieldClass}&entity_type=${entityType}`, '_blank');
  });

  const columns: ProColumns<FieldDetail>[] = useMemo(() => SELECT_FIELD_COLUMNS.map((column) => {
    if (column.dataIndex === 'entity_type') {
      return {
        ...column,
        params: {
          field_class: fieldClass,
          currentEntityType: excludeCurrentEntityType ? entityType : undefined,
        },
        fieldProps: () => {
          // 新增时排除当前介质，不展示
          if (!excludeCurrentEntityType) {
            return {
              disabled: true,
            };
          }
          return {
            disabled: !fieldClass,
            showSearch: true,
          };
        },
      };
    }
    if (column.dataIndex === 'column_family') {
      return {
        ...column,
        params: {
          field_class: fieldClass,
          entityType: excludeCurrentEntityType ? undefined : entityType,
        },
      };
    }
    if (column.dataIndex === 'status') {
      return {
        ...column,
        search: false,
      };
    }
    return column;
  }).concat({
    title: '操作',
    dataIndex: 'action',
    width: 100,
    search: false,
    render: (_: any, record: FieldDetail) => <Button type='link' onClick={() => handleDetail(record)}>详情</Button>,
  }), [excludeCurrentEntityType, entityType, fieldClass]);

  return (
    <Modal
      title="勾选字段(弹窗中无法删除已选字段，需要在列表中手动删除)"
      open={open}
      onCancel={onCancel}
      onOk={handleConfirm}
      width='90%'
      okButtonProps={{ disabled: selectedFields.length === 0 }}
      okText="确认添加"
      cancelText="取消"
    >
      <ProTable<FieldDetail>
        formRef={formRef}
        actionRef={actionRef}
        request={requestFields}
        rowKey='field_path'
        columns={columns}
        style={{ marginTop: 16 }}
        scroll={{ y: 500, x: 'max-content' }}
        pagination={{
          showSizeChanger: true,
        }}
        rowSelection={{
          preserveSelectedRowKeys: true,
          alwaysShowAlert: true,
          selectedRowKeys: selectedKeys,
          onChange: handleRowSelectionChange,
        }}
      />
    </Modal>
  );
});
