import { useState, memo, useEffect, useRef, useMemo } from 'react';
import { <PERSON>ton, Card, Space, App } from 'antd';
import { useMemoizedFn } from 'ahooks';
import { ProTable, ProColumns } from '@ant-design/pro-components';
import { PlusOutlined, SyncOutlined, UploadOutlined } from '@ant-design/icons';
import { SELECT_FIELD_COLUMNS } from '@/constants/field';
import { fetchEntityTypes, fetchColumnFamilies } from '@/services/field';
import { FieldDetail, FieldClass, FieldSchemaUpdateData } from '@/types/api';
import type { OptionItem } from '@/types/common';
import AddFieldModal from './add-field-modal';
import FieldSelectModal from './select-field-modal';
import BatchAddFieldModal from './batch-add-field-modal';

interface AddFieldProps {
  fieldClass?: FieldClass;
  entityType?: string;
  value?: FieldSchemaUpdateData[];
  onChange?: (value: FieldSchemaUpdateData[]) => void;
  skipClearOnParamsChange?: boolean;
}

export default memo((props: AddFieldProps) => {
  const { fieldClass, entityType, value, onChange, skipClearOnParamsChange } = props;
  const { message } = App.useApp();
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [syncModalVisible, setSyncModalVisible] = useState(false);
  const [batchAddModalVisible, setBatchAddModalVisible] = useState(false);
  const [columnFamilies, setColumnFamilies] = useState<OptionItem[]>([]);
  const [entityTypes, setEntityTypes] = useState<OptionItem[]>([]);
  // 添加状态跟踪当前正在编辑的字段
  const [editingField, setEditingField] = useState<FieldSchemaUpdateData | null>(null);

  const dataSource = useMemo(() => {
    if (!value) {
      return [];
    }
    return value.map(item => ({
      ...item,
      entity_type: entityType,
    }));
  }, [value, entityType]);

  // 使用ref记录上一次的查询参数
  const prevParamsRef = useRef({
    fieldClass,
    entityType,
  });

  // 检查查询参数是否发生变化
  const checkParamsChanged = useMemoizedFn(() => {
    const prevParams = prevParamsRef.current;
    const paramsChanged = prevParams.fieldClass !== fieldClass
      || prevParams.entityType !== entityType;
    // 更新上一次的参数
    prevParamsRef.current = {
      fieldClass,
      entityType,
    };
    return paramsChanged;
  });

  // 当参数变化时，根据skipClearOnParamsChange判断是否清空字段
  useEffect(() => {
    // 检查查询参数是否变化
    const paramsChanged = checkParamsChanged();
    // 如果参数变化且不跳过清空，则清空已选字段
    if (paramsChanged && !skipClearOnParamsChange && onChange) {
      onChange([]);
    }
    fetchColumnFamilies({
      field_class: fieldClass,
      entity_type: entityType,
    }).then((res) => {
      setColumnFamilies(res);
    });
    fetchEntityTypes({
      field_class: fieldClass,
    }).then((res) => {
      setEntityTypes(res);
    });
  }, [fieldClass, entityType, skipClearOnParamsChange]);

  // 处理删除字段
  const handleRemoveField = useMemoizedFn((fieldPath: string) => {
    const newValue = value?.filter(item => item.field_path !== fieldPath);
    onChange?.(newValue || []);
  });

  // 处理编辑字段按钮点击
  const handleEditField = useMemoizedFn((record: FieldSchemaUpdateData) => {
    setEditingField(record);
    setAddModalVisible(true);
  });

  // 处理同步字段
  const handleSyncFields = useMemoizedFn((fields: FieldDetail[]) => {
    // 转换FieldDetail为FieldSchemaUpdateData
    const newFields = fields.map(field => ({
      entity_type: entityType,
      column_family: field.column_family,
      field_path: field.field_path,
      src_field_path: field.src_field_path,
      field_name: field.field_name,
      value_type: field.value_type,
      validation: field.validation,
      description: field.description,
      example: field.example,
      produce_logic: field.produce_logic,
      owner: field.owner,
      tapd: field.tapd,
    }));
    // 合并新旧字段，以field_path和entity_type为唯一键进行去重
    const existingPaths = dataSource?.map(field => `${field.field_path}-${field.entity_type}`);
    const fieldsToAdd = newFields.filter(field => !existingPaths?.includes(`${field.field_path}-${field.entity_type}`));
    if (fieldsToAdd.length === 0) {
      message.info('没有新增字段');
      setSyncModalVisible(false);
      return;
    }
    const mergedFields = [...(dataSource || []), ...fieldsToAdd];
    onChange?.(mergedFields || []);
    message.success(`成功同步${fieldsToAdd.length}个字段`);
    setSyncModalVisible(false);
  });

  // 处理批量添加字段
  const handleBatchAddFields = useMemoizedFn((fields: FieldSchemaUpdateData[]) => {
    if (fields.length === 0) {
      message.info('没有新增字段');
      setBatchAddModalVisible(false);
      return;
    }
    // 合并新旧字段，以field_path和entity_type为唯一键进行去重
    const existingPaths = dataSource?.map(field => `${field.field_path}-${field.entity_type}`);
    const fieldsToAdd = fields.filter(field => !existingPaths?.includes(`${field.field_path}-${field.entity_type}`));
    if (fieldsToAdd.length === 0) {
      message.info('所有字段已存在，没有新增字段');
      setBatchAddModalVisible(false);
      return;
    }
    const mergedFields = [...(dataSource || []), ...fieldsToAdd];
    onChange?.(mergedFields || []);
    message.success(`成功批量添加${fieldsToAdd.length}个字段`);
    setBatchAddModalVisible(false);
  });

  // 表格列配置
  const columns: ProColumns<FieldSchemaUpdateData>[] = useMemo(
    () => [
      ...SELECT_FIELD_COLUMNS.filter(column => column.dataIndex !== 'status').map((column) => {
        if (column.dataIndex === 'entity_type') {
          return {
            ...column,
            request: undefined,
            fieldProps: {
              options: entityTypes,
            },
          };
        }
        if (column.dataIndex === 'column_family') {
          return {
            ...column,
            request: undefined,
            fieldProps: {
              options: columnFamilies,
            },
          };
        }
        return column;
      }),
      {
        title: '操作',
        key: 'action',
        valueType: 'option',
        fixed: 'right',
        width: 100,
        render: (_: any, record: FieldSchemaUpdateData) => (
          <Space>
            <Button
              type='link'
              style={{ padding: 0 }}
              onClick={() => handleEditField(record)}
            >
              编辑
            </Button>
            <Button
              type='link'
              style={{ padding: 0 }}
              danger
              onClick={() => handleRemoveField(record.field_path as string)}
            >
              删除
            </Button>
          </Space>
        ),
      },
    ],
    [entityTypes, columnFamilies],
  );

  // 处理模态框关闭
  const handleModalOpenChange = useMemoizedFn((visible: boolean) => {
    setAddModalVisible(visible);
    if (!visible) {
      // 当模态框关闭时，清空编辑状态
      setEditingField(null);
    }
  });

  const handleAddFinish = useMemoizedFn(async (fieldData: FieldSchemaUpdateData) => {
    if (editingField) {
      // 编辑模式：替换现有字段
      const newValue = value?.map(item => (item.field_path === editingField.field_path ? fieldData : item));
      onChange?.(newValue || []);
      message.success('编辑字段成功');
    } else {
      // 新增模式：添加新字段
      const newValue = [...(value || []), fieldData];
      onChange?.(newValue || []);
      message.success('添加字段成功');
    }
    return true;
  });

  const showSyncFields = useMemoizedFn(() => {
    if (fieldClass == null) {
      message.info('请先选择字段分类');
      return;
    }
    setSyncModalVisible(true);
  });

  const showAddField = useMemoizedFn(() => {
    if (fieldClass == null || !entityType) {
      message.info('请先选择订阅字段类型和介质类型');
      return;
    }
    setAddModalVisible(true);
  });

  const showBatchAddField = useMemoizedFn(() => {
    if (fieldClass == null || !entityType) {
      message.info('请先选择订阅字段类型和介质类型');
      return;
    }
    setBatchAddModalVisible(true);
  });
  return (
    <>
      <Card
        title='新增字段'
        extra={(
          <Space>
            <Button
              type='primary'
              icon={<SyncOutlined />}
              onClick={showSyncFields}
            >
              同步字段
            </Button>
            <Button
              type='primary'
              icon={<PlusOutlined />}
              onClick={showAddField}
            >
              新增字段
            </Button>
            <Button
              type='primary'
              icon={<UploadOutlined />}
              onClick={showBatchAddField}
            >
              批量添加
            </Button>
          </Space>
        )}
      >
        {dataSource.length > 0 ? (
          <ProTable<FieldSchemaUpdateData>
            dataSource={dataSource}
            rowKey='field_path'
            columns={columns}
            search={false}
            options={false}
            pagination={false}
            scroll={{ x: 'max-content', y: 300 }}
          />
        ) : (
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            暂无新增字段，请点击右上角按钮添加
          </div>
        )}
      </Card>
      {/* 新增/编辑字段弹窗 */}
      <AddFieldModal
        open={addModalVisible}
        fieldClass={fieldClass}
        entityType={entityType}
        editingField={editingField}
        readyAddFields={dataSource}
        onFinish={handleAddFinish}
        onOpenChange={handleModalOpenChange}
      />
      {/* 同步字段弹窗 */}
      <FieldSelectModal
        open={syncModalVisible}
        fieldClass={fieldClass}
        entityType={entityType}
        initFields={dataSource}
        excludeCurrentEntityType
        onConfirm={handleSyncFields}
        onCancel={() => setSyncModalVisible(false)}
      />
      {/* 批量添加字段弹窗 */}
      <BatchAddFieldModal
        open={batchAddModalVisible}
        fieldClass={fieldClass}
        entityType={entityType}
        readyAddFields={dataSource}
        onConfirm={handleBatchAddFields}
        onCancel={() => setBatchAddModalVisible(false)}
      />
    </>
  );
});
