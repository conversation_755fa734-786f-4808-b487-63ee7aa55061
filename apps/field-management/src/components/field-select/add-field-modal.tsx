import { useRef, memo, useEffect, useMemo, useState } from 'react';
import { App } from 'antd';
import { useMemoizedFn } from 'ahooks';
import uniqBy from 'lodash/uniqBy';
import { CodeEditor } from '@packages/components';
import { safeJsonParse } from '@packages/utils';
import {
  ModalForm,
  ProFormText,
  ProFormSelect,
  ProFormTextArea,
  ProFormItem,
  ProFormDependency,
  ProFormInstance,
} from '@ant-design/pro-components';
import { useAccess } from '@/hooks/useAccess';
import { fetchColumnFamilies, fetchEntityTypes, fetchAllFields } from '@/services/field';
import { httpUrlValidationRule, fieldExampleValidationRule, noSpaceValidationRule } from '@/constants/rules';
import { getUsers } from '@/services/user';
import { FieldClass, FieldSchemaUpdateData } from '@/types/api';
import ValidationRules from '@/components/validation-rules';
import { VALUE_TYPE_OPTIONS, COLUMN_FAMILY_TOOLTIP, BLOCK_RULE_OPTIONS } from '@/constants/field';

interface AddFieldModalProps {
  fieldClass?: FieldClass;
  entityType?: string;
  editingField?: FieldSchemaUpdateData | null;
  open: boolean;
  readyAddFields?: FieldSchemaUpdateData[];
  onFinish: (field: FieldSchemaUpdateData) => Promise<boolean | void>;
  onOpenChange: (visible: boolean) => void;
}

export default memo((props: AddFieldModalProps) => {
  const { fieldClass, entityType, editingField, open, readyAddFields, onFinish, onOpenChange } = props;
  const formRef = useRef<ProFormInstance<FieldSchemaUpdateData>>(null);
  const { message } = App.useApp();
  const [allFields, setAllFields] = useState<FieldSchemaUpdateData[]>([]);
  const { isAdmin, userInfo } = useAccess();
  // 判断是否处于编辑模式
  const isEditMode = !!editingField;
  const isAdminRole = isAdmin();

  // 获取所有字段用于校验
  useEffect(() => {
    if (entityType && fieldClass) {
      fetchAllFields({
        entity_type: entityType,
        field_class: fieldClass,
        current: 1,
        pageSize: -1,
        useCache: true,
      }).then((res) => {
        if (res.success) {
          setAllFields(uniqBy([
            ...res.data,
            ...(readyAddFields || []),
          ], 'field_path'));
        }
      });
    }
  }, [entityType, fieldClass, readyAddFields]);

  // 当数据类型变化时，重置校验规则
  const handleValueTypeChange = useMemoizedFn(() => {
    formRef.current?.setFieldValue('validation', {});
  });

  const initialValues = useMemo(() => {
    if (editingField && isEditMode) {
      return {
        ...editingField,
        owner: userInfo.name,
        field_class: fieldClass,
        entity_type: entityType,
        src_field_path: editingField?.src_field_path || editingField.field_path,
        validation: safeJsonParse<Record<string, any>>(editingField?.validation, {}),
        block_rule: editingField?.block_rule || 1,
      };
    }
    return {
      owner: userInfo.name,
      entity_type: entityType,
      field_class: fieldClass,
      block_rule: 1,
    };
  }, [editingField, isEditMode, entityType, fieldClass, userInfo.name]);

  useEffect(() => {
    if (formRef.current) {
      // @ts-expect-error s
      formRef.current?.setFieldsValue(initialValues);
    }
  }, [initialValues, formRef.current]);

  const handleFinish = useMemoizedFn(async (values: FieldSchemaUpdateData) => {
    try {
      const isValidate = await formRef.current?.validateFields();
      if (!isValidate) {
        return false;
      }
      // 确保entity_type和column_family的值
      values.entity_type = entityType;
      if (values.validation) {
        values.validation = JSON.stringify(values.validation);
      }
      if (!values.src_field_path) {
        values.src_field_path = values.field_path;
      }
      // 调用确认回调
      const success = await onFinish(values);
      if (success !== false) {
        return true;
      }
      return false;
    } catch (error) {
      message.error(isEditMode ? '编辑字段失败' : '添加字段失败');
      return false;
    }
  });

  const handleFieldPathChange = useMemoizedFn((e: React.ChangeEvent<HTMLInputElement>) => {
    if (!isEditMode) {
      formRef.current?.setFieldValue('src_field_path', e.target.value);
    }
  });

  const handleInit = useMemoizedFn((_: FieldSchemaUpdateData, form: ProFormInstance<FieldSchemaUpdateData>) => {
    // @ts-expect-error s
    form.setFieldsValue(initialValues);
  });

  return (
    <ModalForm<FieldSchemaUpdateData>
      title={isEditMode ? '编辑字段' : '新增字段'}
      open={open}
      formRef={formRef}
      onFinish={handleFinish}
      onOpenChange={onOpenChange}
      width={660}
      layout="horizontal"
      onInit={handleInit}
      modalProps={{
        destroyOnClose: true,
        styles: {
          body: {
            maxHeight: '70vh',
            overflow: 'auto',
          },
        },
      }}
      // @ts-ignore
      initialValues={initialValues}
      submitter={{
        searchConfig: {
          submitText: isEditMode ? '保存' : '确认添加',
        },
      }}
    >
      <ProFormSelect
        name="entity_type"
        label="介质类型"
        disabled
        request={fetchEntityTypes}
        showSearch
        params={{
          field_class: fieldClass,
        }}
        rules={[{ required: true, message: '请选择介质类型' }]}
      />
      <ProFormSelect
        name="column_family"
        label="列簇名称"
        request={fetchColumnFamilies}
        showSearch
        tooltip={COLUMN_FAMILY_TOOLTIP}
        params={{
          entity_type: entityType,
          field_class: fieldClass,
        }}
        rules={[{ required: true, message: '请输入列簇名称' }]}
      />
      <ProFormText
        name="src_field_path"
        label="源字段路径"
        disabled={!isAdminRole}
        placeholder="例如：user.name"
        rules={[
          { required: true, message: '请输入源字段路径' },
          {
            validator: async (_, value) => {
              if (value?.includes(' ')) {
                return Promise.reject(new Error('字段路径不能包含空格'));
              }
              if (value && value.toLowerCase() !== value) {
                return Promise.reject(new Error('字段路径只能包含小写字母'));
              }
              // 字段路径存在性校验
              if (value && allFields.length > 0) {
                // 检查当前字段是否已存在
                const fieldExists = allFields.some(field => field.src_field_path === value);
                if (fieldExists && !isEditMode) {
                  return Promise.reject(new Error(`源字段路径 ${value} 已存在`));
                }
              }
              return Promise.resolve();
            },
          },
          noSpaceValidationRule,
        ]}
      />
      <ProFormText
        name="field_path"
        label="字段路径"
        disabled={isEditMode}
        placeholder="例如：user.name"
        rules={[
          { required: true, message: '请输入字段路径' },
          {
            validator: async (_, value) => {
              if (value?.includes(' ')) {
                return Promise.reject(new Error('字段路径不能包含空格'));
              }
              if (value && value.toLowerCase() !== value) {
                return Promise.reject(new Error('字段路径只能包含小写字母'));
              }
              // 字段路径存在性校验
              if (value && allFields.length > 0) {
                // 检查当前字段是否已存在
                const fieldExists = allFields.some(field => field.field_path === value);
                if (fieldExists && !isEditMode) {
                  return Promise.reject(new Error(`字段路径 ${value} 已存在`));
                }
                // 检查父级字段是否完整
                const pathSegments = value.split('.');
                if (pathSegments.length > 1) {
                  // 构建父级字段路径
                  const parentPaths = [];
                  for (let i = 1; i < pathSegments.length; i++) {
                    const parentPath = pathSegments.slice(0, i).join('.');
                    parentPaths.push(parentPath);
                  }
                  // 检查所有父级字段是否存在
                  const missingParents = parentPaths.filter(path => !allFields.some(field => field.field_path === path));
                  if (missingParents.length > 0) {
                    return Promise.reject(new Error(`父级字段 ${missingParents.join(', ')} 不存在，请先创建父级字段`));
                  }
                }
              }
              return Promise.resolve();
            },
          },
          noSpaceValidationRule,
        ]}
        fieldProps={{
          onChange: handleFieldPathChange,
        }}
      />
      <ProFormText
        name="field_name"
        label="字段名称"
        placeholder="例如：用户名"
        rules={[
          { required: true, message: '请输入字段名称' },
          noSpaceValidationRule,
        ]}
      />
      <ProFormSelect
        name="value_type"
        label="数据类型"
        options={VALUE_TYPE_OPTIONS}
        rules={[{ required: true, message: '请选择数据类型' }]}
        fieldProps={{
          onChange: handleValueTypeChange,
        }}
      />
      <ProFormDependency name={['value_type']}>
        {({ value_type }) => (
          <ProFormItem name="validation" label="校验规则">
            <ValidationRules valueType={value_type} />
          </ProFormItem>
        )}
      </ProFormDependency>
      <ProFormTextArea
        name="description"
        label="字段描述"
        rules={[
          { required: true, message: '请输入字段描述' },
        ]}
      />
      <ProFormItem
        name="example"
        label="字段示例"
        tooltip='支持JSON格式，如：{"name": "张三", "age": 18}'
        rules={[
          { required: true, message: '请输入字段示例' },
          fieldExampleValidationRule,
        ]}
      >
        <CodeEditor language="json" height={120} />
      </ProFormItem>
      <ProFormTextArea
        name="produce_logic"
        label="生产逻辑"
      />
      <ProFormSelect
        name="block_rule"
        label="拦截规则"
        options={BLOCK_RULE_OPTIONS}
        rules={[{ required: true, message: '请选择拦截规则' }]}
      />
      <ProFormSelect
        name='owner'
        label="负责人"
        showSearch
        request={getUsers}
        width={200}
        rules={[{ required: true, message: '请选择负责人' }]}
      />
      <ProFormText
        name="tapd"
        label="TAPD地址"
        placeholder="请输入TAPD地址"
        rules={[
          { required: true, message: '请输入TAPD地址' },
          httpUrlValidationRule,
        ]}
      />
    </ModalForm>
  );
});
