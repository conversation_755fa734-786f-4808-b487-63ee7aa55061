import { memo } from 'react';
import { useMemoizedFn } from 'ahooks';
import { App, <PERSON><PERSON>, Button } from 'antd';
import { useAtom } from 'helux';
import { ModalForm, ProFormText, ProFormTextArea, ProTable } from '@ant-design/pro-components';
import { OrderType, type OrderUpdateData } from '@/types/api';
import { orderAtom, orderActions, useOrderLoading } from '@/model/order';
import { userAtom } from '@/model/auth';
import { FIELD_COLUMNS } from '@/constants/field';
import type { OrderBusinessMode } from '@/types/common';
import { httpUrlValidationRule } from '@/constants/rules';

export interface OfflineModalProps {
  mode: OrderBusinessMode;
}

const showColumns = ['field_name', 'field_path', 'field_class', 'entity_type', 'consumer_count'];

const offlineColumns = FIELD_COLUMNS.map((column) => {
  if (!showColumns.includes(column.dataIndex as string)) {
    return {
      ...column,
      hideInTable: true,
    };
  }
  return column;
}).concat({
  title: '消费方数量',
  dataIndex: 'consumer_count',
}, {
  // 查看详情
  title: '查看详情',
  valueType: 'option',
  render: (_, record) => {
    const { field_class: fieldClass, entity_type: entityType } = record;
    return (
      <Button
        type="link"
        onClick={() => {
          window.open(`${location.origin}/field/detail/${record.field_path}?field_class=${fieldClass}&entity_type=${entityType}`, '_blank');
        }}
      >
        详情
      </Button>
    );
  },
});

const OfflineModal = ({ mode }: OfflineModalProps) => {
  const [orderState] = useAtom(orderAtom);
  const { message } = App.useApp();
  const { setOfflineProducer } = useOrderLoading();

  const handleFinish = useMemoizedFn(async (values: OrderUpdateData) => {
    const res = await orderActions.offline({
      order: {
        ...values,
        applicant: userAtom.name,
      },
      type: mode,
    });
    if (res.success) {
      message.success('已经提交，请等待审核');
    } else {
      message.error(res.message || '提交失败');
    }
    orderActions.setOfflineOpen(false);
    orderActions.resetOrderDetail();
  });

  const handleOpenChange = useMemoizedFn((open: boolean) => {
    orderActions.setOfflineOpen(open);
    if (!open) {
      orderActions.setAppName('');
    }
  });

  return (
    <ModalForm<OrderUpdateData>
      title={orderState.offlineTitle}
      width={800}
      open={orderState.offlineOpen}
      onFinish={handleFinish}
      onOpenChange={handleOpenChange}
    >
      <ProFormText
        name="tapd"
        label="TAPD地址"
        rules={[
          { required: true, message: '请输入TAPD地址' },
          httpUrlValidationRule,
        ]}
      />
      <ProFormTextArea
        name="description"
        label="工单描述"
        rules={[{ required: true, message: '请输入工单描述' }]}
      />
      {orderState.offlineType === OrderType.ORDER_TYPE_PRODUCER_OFFLINE ? (
        <>
          <Alert
            message={orderState.offlineFields?.length ? '请确保通知到所有消费方后再提交下线申请！' : '暂无字段消费方，可直接下线'}
            type={orderState.offlineFields?.length ? 'warning' : 'info'}
            style={{ marginTop: 16, marginBottom: 16 }}
            showIcon
          />
          {orderState.offlineFields?.length ? (
            <ProTable
              columns={offlineColumns}
              dataSource={orderState.offlineFields || []}
              loading={setOfflineProducer.loading}
              rowKey="field_path"
              search={false}
              options={false}
              pagination={false}
              scroll={{ x: 'max-content', y: 300 }}
            />
          ) : null}
        </>
      ) : null}
    </ModalForm>
  );
};

export default memo(OfflineModal);
