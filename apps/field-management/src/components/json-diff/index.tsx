import React, { useMemo } from 'react';
import { Card, Typography, Space, Tag, Empty } from 'antd';
import ReactDiffViewer, { DiffMethod } from 'react-diff-viewer-continued';
import './index.css';

const { Title, Text } = Typography;

interface JsonDiffProps {
  /** 旧数据对象 */
  oldData?: any;
  /** 新数据对象 */
  newData?: any;
  /** 左侧标题 */
  leftTitle?: string;
  /** 右侧标题 */
  rightTitle?: string;
  /** 是否显示行号 */
  showLineNumbers?: boolean;
  /** 是否分割视图 */
  splitView?: boolean;
  /** 是否隐藏差异数量 */
  hideLineNumbers?: boolean;
  /** 自定义样式 */
  styles?: any;
}

/**
 * 清理对象，移除 undefined 和 null 值，格式化为可比较的结构
 */
const cleanObject = (obj: any): any => {
  if (obj === null || obj === undefined) {
    return null;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(cleanObject).filter(item => item !== null && item !== undefined);
  }
  
  if (typeof obj === 'object') {
    const cleaned: any = {};
    Object.keys(obj).forEach(key => {
      const value = cleanObject(obj[key]);
      if (value !== null && value !== undefined) {
        cleaned[key] = value;
      }
    });
    return cleaned;
  }
  
  return obj;
};

/**
 * 计算差异统计
 */
const calculateDiffStats = (oldData: any, newData: any) => {
  const oldStr = JSON.stringify(cleanObject(oldData), null, 2);
  const newStr = JSON.stringify(cleanObject(newData), null, 2);
  
  if (oldStr === newStr) {
    return { hasChanges: false, additions: 0, deletions: 0, modifications: 0 };
  }
  
  const oldLines = oldStr.split('\n');
  const newLines = newStr.split('\n');
  
  // 简单的差异统计（实际实现可能需要更复杂的算法）
  const maxLines = Math.max(oldLines.length, newLines.length);
  let additions = 0;
  let deletions = 0;
  let modifications = 0;
  
  for (let i = 0; i < maxLines; i++) {
    const oldLine = oldLines[i];
    const newLine = newLines[i];
    
    if (oldLine === undefined && newLine !== undefined) {
      additions++;
    } else if (oldLine !== undefined && newLine === undefined) {
      deletions++;
    } else if (oldLine !== newLine) {
      modifications++;
    }
  }
  
  return { hasChanges: true, additions, deletions, modifications };
};

const JsonDiff: React.FC<JsonDiffProps> = ({
  oldData,
  newData,
  leftTitle = '变更前',
  rightTitle = '变更后',
  splitView = true,
  hideLineNumbers = false,
  styles = {},
}) => {
  const { cleanedOldData, cleanedNewData, diffStats } = useMemo(() => {
    const cleanedOld = cleanObject(oldData);
    const cleanedNew = cleanObject(newData);
    const stats = calculateDiffStats(cleanedOld, cleanedNew);
    
    return {
      cleanedOldData: cleanedOld,
      cleanedNewData: cleanedNew,
      diffStats: stats,
    };
  }, [oldData, newData]);

  const oldValue = useMemo(() => {
    return JSON.stringify(cleanedOldData, null, 2);
  }, [cleanedOldData]);

  const newValue = useMemo(() => {
    return JSON.stringify(cleanedNewData, null, 2);
  }, [cleanedNewData]);

  // 如果没有数据，显示空状态
  if (!oldData && !newData) {
    return (
      <Card>
        <Empty description="暂无数据对比" />
      </Card>
    );
  }

  // 如果没有变更
  if (!diffStats.hasChanges) {
    return (
      <Card>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Title level={5}>数据对比结果</Title>
          <Tag color="success">无变更</Tag>
          <Text type="secondary">新旧数据完全一致</Text>
        </Space>
      </Card>
    );
  }

  const customStyles = {
    variables: {
      light: {
        codeFoldGutterBackground: '#f7f7f7',
        codeFoldBackground: '#f1f8ff',
        addedBackground: '#e6ffed',
        addedColor: '#24292e',
        removedBackground: '#ffeef0',
        removedColor: '#24292e',
        wordAddedBackground: '#acf2bd',
        wordRemovedBackground: '#fdb8c0',
        addedGutterBackground: '#cdffd8',
        removedGutterBackground: '#fdbdbe',
        gutterBackground: '#f7f7f7',
        gutterBackgroundDark: '#f7f7f7',
        highlightBackground: '#fffbdd',
        highlightGutterBackground: '#fff5b4',
      },
    },
    ...styles,
  };

  return (
    <Card>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={5}>数据对比结果</Title>
          <Space>
            {diffStats.additions > 0 && (
              <Tag color="success">+{diffStats.additions} 新增</Tag>
            )}
            {diffStats.deletions > 0 && (
              <Tag color="error">-{diffStats.deletions} 删除</Tag>
            )}
            {diffStats.modifications > 0 && (
              <Tag color="warning">~{diffStats.modifications} 修改</Tag>
            )}
          </Space>
        </div>
        
        <div className="json-diff-container">
          <ReactDiffViewer
            oldValue={oldValue}
            newValue={newValue}
            splitView={splitView}
            leftTitle={leftTitle}
            rightTitle={rightTitle}
            hideLineNumbers={hideLineNumbers}
            compareMethod={DiffMethod.WORDS}
            styles={customStyles}
            useDarkTheme={false}
            extraLinesSurroundingDiff={3}
          />
        </div>
      </Space>
    </Card>
  );
};

export default JsonDiff;
