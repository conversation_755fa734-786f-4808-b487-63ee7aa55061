# JsonDiff 组件

一个用于展示JSON数据差异的React组件，基于 `react-diff-viewer-continued` 实现。

## 功能特性

- 🔍 **深度对比**: 支持复杂嵌套对象的深度比较
- 🎨 **语法高亮**: JSON代码语法高亮显示
- 📊 **差异统计**: 自动计算并显示新增、删除、修改的数量
- 🎯 **智能清理**: 自动过滤undefined和null值
- 📱 **响应式**: 支持移动端和桌面端适配
- ⚡ **性能优化**: 使用useMemo优化大数据对比性能

## 基本用法

```tsx
import JsonDiff from '@/components/json-diff';

const oldData = {
  name: 'test-app',
  version: '1.0.0',
  config: {
    enabled: true,
    timeout: 5000
  }
};

const newData = {
  name: 'test-app', 
  version: '1.1.0',
  config: {
    enabled: false,
    timeout: 10000,
    retries: 3
  }
};

function MyComponent() {
  return (
    <JsonDiff
      oldData={oldData}
      newData={newData}
      leftTitle="变更前"
      rightTitle="变更后"
    />
  );
}
```

## API 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| oldData | any | - | 旧数据对象 |
| newData | any | - | 新数据对象 |
| leftTitle | string | '变更前' | 左侧标题 |
| rightTitle | string | '变更后' | 右侧标题 |
| showLineNumbers | boolean | true | 是否显示行号 |
| splitView | boolean | true | 是否分割视图 |
| hideLineNumbers | boolean | false | 是否隐藏差异数量 |
| styles | object | {} | 自定义样式 |

## 使用场景

### 1. 配置对比
```tsx
<JsonDiff
  oldData={oldConfig}
  newData={newConfig}
  leftTitle="当前配置"
  rightTitle="新配置"
/>
```

### 2. 数据变更审核
```tsx
<JsonDiff
  oldData={originalData}
  newData={modifiedData}
  leftTitle="原始数据"
  rightTitle="修改后数据"
/>
```

### 3. API响应对比
```tsx
<JsonDiff
  oldData={previousResponse}
  newData={currentResponse}
  leftTitle="上次响应"
  rightTitle="当前响应"
/>
```

## 样式自定义

组件支持通过 `styles` 参数自定义样式：

```tsx
const customStyles = {
  variables: {
    light: {
      addedBackground: '#e6ffed',
      removedBackground: '#ffeef0',
      wordAddedBackground: '#acf2bd',
      wordRemovedBackground: '#fdb8c0',
    }
  }
};

<JsonDiff
  oldData={oldData}
  newData={newData}
  styles={customStyles}
/>
```

## 注意事项

1. **数据清理**: 组件会自动清理undefined和null值，确保对比结果的准确性
2. **性能考虑**: 对于大型数据对象，建议在父组件中使用useMemo缓存数据
3. **内存使用**: 组件会创建JSON字符串副本，注意大数据的内存占用
4. **浏览器兼容**: 依赖现代浏览器的JSON.stringify功能

## 依赖

- react-diff-viewer-continued: ^3.4.0
- antd: ^5.24.1
- lodash: ^4.17.21
