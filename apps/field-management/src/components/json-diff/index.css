.json-diff-container {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.json-diff-container .diff-viewer {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 12px;
  line-height: 1.4;
}

.json-diff-container .diff-viewer-title {
  background: #fafafa;
  border-bottom: 1px solid #d9d9d9;
  padding: 8px 12px;
  font-weight: 500;
  font-size: 13px;
  color: #262626;
}

.json-diff-container .diff-gutter {
  background: #fafafa;
  border-right: 1px solid #e8e8e8;
  min-width: 50px;
}

.json-diff-container .diff-gutter-col {
  padding: 0 8px;
}

.json-diff-container .diff-code {
  padding: 0 12px;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 自定义滚动条样式 */
.json-diff-container ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.json-diff-container ::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.json-diff-container ::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.json-diff-container ::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .json-diff-container .diff-viewer {
    font-size: 11px;
  }
  
  .json-diff-container .diff-code {
    padding: 0 8px;
  }
  
  .json-diff-container .diff-gutter-col {
    padding: 0 4px;
  }
}

/* 高亮样式优化 */
.json-diff-container .diff-viewer .diff-line-added {
  background-color: #e6ffed !important;
}

.json-diff-container .diff-viewer .diff-line-removed {
  background-color: #ffeef0 !important;
}

.json-diff-container .diff-viewer .diff-word-added {
  background-color: #acf2bd !important;
  color: #24292e !important;
}

.json-diff-container .diff-viewer .diff-word-removed {
  background-color: #fdb8c0 !important;
  color: #24292e !important;
}
