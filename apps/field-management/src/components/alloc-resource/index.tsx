import { memo, useEffect, useRef, useMemo } from 'react';
import { useMemoizedFn } from 'ahooks';
import { App } from 'antd';
import { useAtom, useDerived } from 'helux';
import { ModalForm, ProFormInstance } from '@ant-design/pro-components';
import { allocConsumerResource } from '@/services/consumer';
import { allocProducerResource } from '@/services/producer';
import type { ProducerAllocResourceRequest, ConsumerAllocResourceRequest } from '@/types/api';
import { orderAtom, isAPIRes, orderActions, useOrderLoading, isTestOrder } from '@/model/order';
import type { BusinessMode } from '@/types/common';
import { ResourceType, OrderType } from '@/types/api';
import { ProducerAllocResourceForm } from './producer';
import { ConsumerAllocResourceForm, kafkaInitialValues, apiInitialValues } from './consumer';

export interface AllocResourceModalProps {
  mode: BusinessMode;
}

type FormValues = ProducerAllocResourceRequest | ConsumerAllocResourceRequest;

const AllocResourceModal = ({ mode }: AllocResourceModalProps) => {
  const [orderState] = useAtom(orderAtom);
  const [isAPI] = useDerived(isAPIRes);
  const [isTest] = useDerived(isTestOrder);
  const { setOrderDetail } = useOrderLoading();
  const { message } = App.useApp();
  const formRef = useRef<ProFormInstance<FormValues>>(null);

  const handleFinish = useMemoizedFn(async (values: ProducerAllocResourceRequest | ConsumerAllocResourceRequest) => {
    const params = {
      ...values,
      order_id: orderState.currentOrder?.order_id,
      resource_type: isTest ? ResourceType.RESOURCE_TYPE_TEST : ResourceType.RESOURCE_TYPE_PRODUCTION,
    };
    const res = mode === 'consumer'
      ? await allocConsumerResource(params as ConsumerAllocResourceRequest)
      : await allocProducerResource(params as ProducerAllocResourceRequest);
    if (res.success) {
      message.success('提交成功，审核通过后生效，请勿重复提交');
    } else {
      message.error(res.message);
    }
    orderActions.setResourceOpen(false);
    orderActions.resetOrderDetail();
    return true;
  });

  const handleOpenChange = useMemoizedFn((open: boolean) => {
    orderActions.setResourceOpen(open);
    if (!open) {
      orderActions.resetOrderDetail();
    }
  });

  const initialValues = useMemo(() => {
    const { type } = orderState.currentOrder || {};
    if (type === OrderType.ORDER_TYPE_CONSUMER_UPDATE || type === OrderType.ORDER_TYPE_CONSUMER_CREATE) {
      const { subscribe_service: subscribeService } = orderState.curConsumer || {};
      const { test_api: testApi, prod_api: prodApi, test_kafka: testKafka, prod_kafka: prodKafka } = subscribeService || {};
      if (isAPI) {
        const apiService = isTest ? testApi : prodApi;
        return {
          api: {
            ...apiInitialValues,
            check_app_key: apiService?.check_app_key,
            app_key: apiService?.app_key,
            check_caller: apiService?.check_caller,
            service_name: apiService?.service_name,
            api_name: apiService?.api_name,
          },
        };
      }
      const kafkaService = isTest ? testKafka : prodKafka;
      return {
        kafka: {
          ...kafkaService,
          producer_id: kafkaService?.producer_id || kafkaInitialValues.producer_id,
          compression: kafkaService?.compression || kafkaInitialValues.compression,
          partitioner: kafkaService?.partitioner || kafkaInitialValues.partitioner,
        },
      };
    }
    if (type === OrderType.ORDER_TYPE_PRODUCER_UPDATE || type === OrderType.ORDER_TYPE_PRODUCER_CREATE) {
      const { kafka_addr, kafka_topic, test_kafka_addr, test_kafka_topic } = orderState?.curProducer || {};
      if (isAPI) {
        const { api_name: apiName } = orderState?.curProducer || {};
        let apiNames: string[] | undefined;
        if (typeof apiName === 'string' && apiName) {
          apiNames = apiName.split(',');
        }
        return {
          api: {
            api_name: apiNames,
          },
        };
      }
      return {
        kafka: {
          address: isTest ? test_kafka_addr : kafka_addr,
          topic: isTest ? test_kafka_topic : kafka_topic,
        },
      };
    }
    return {};
  }, [orderState.currentOrder, isAPI, isTest, orderState.curConsumer, orderState.curProducer]);

  useEffect(() => {
    if (!formRef.current || !orderState.resourceOpen) {
      return;
    }
    formRef.current.setFieldsValue(initialValues);
  }, [formRef.current, orderState.resourceOpen, initialValues]);

  const handleInit = useMemoizedFn((_: FormValues, form: ProFormInstance<FormValues>) => {
    form.setFieldsValue(initialValues);
  });

  return (
    <ModalForm<FormValues>
      formRef={formRef}
      title={`分配${isTest ? '测试' : '正式'}资源`}
      layout="horizontal"
      width={600}
      open={orderState.resourceOpen}
      initialValues={initialValues}
      onFinish={handleFinish}
      onOpenChange={handleOpenChange}
      onInit={handleInit}
      modalProps={{
        destroyOnClose: true,
        loading: setOrderDetail.loading,
      }}
    >
      {mode === 'consumer' ? (
        <ConsumerAllocResourceForm isAPI={isAPI} />
      ) : (
        <ProducerAllocResourceForm isAPI={isAPI} />
      )}
    </ModalForm>
  );
};

export default memo(AllocResourceModal);
