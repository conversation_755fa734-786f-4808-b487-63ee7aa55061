import { memo } from 'react';
import {
  ProFormText,
  ProFormSwitch,
  ProFormSelect,
  ProFormDependency,
} from '@ant-design/pro-components';
import { KAFKA_PARTITIONER_OPTIONS, KAFKA_COMPRESSION_OPTIONS } from '@/constants/consumer';
import { kafkaTopicValidationRule, noSpaceValidationRule } from '@/constants/rules';

interface ConsumerAllocResourceFormProps {
  isAPI: boolean;
  readonly?: boolean;
}

export const apiInitialValues = {
  check_app_key: false,
  check_caller: false,
};

export const kafkaInitialValues = {
  producer_id: 'databus_distribute_write',
  compression: KAFKA_COMPRESSION_OPTIONS[0].value,
  partitioner: KAFKA_PARTITIONER_OPTIONS[0].value,
};

export const ConsumerAllocResourceForm = memo(({ isAPI, readonly = false }: ConsumerAllocResourceFormProps) => {
  if (isAPI) {
    return (
      <>
        <ProFormSwitch
          name={['api', 'check_app_key']}
          label="是否开启AppKey鉴权"
          rules={[{ required: true, message: '请选择' }]}
          readonly={readonly}
          initialValue={false}
        />
        <ProFormDependency name={['api', 'check_app_key']}>
          {({ api }) => {
            if (api?.check_app_key) {
              return (
                <ProFormText
                  name={['api', 'app_key']}
                  label="AppKey"
                  rules={[
                    { required: true, message: '请输入AppKey' },
                    noSpaceValidationRule,
                  ]}
                  readonly={readonly}
                />
              );
            }
            return null;
          }}
        </ProFormDependency>
        <ProFormSwitch
          name={['api', 'check_caller']}
          label="是否启用主调方名鉴权方式"
          rules={[{ required: true, message: '请选择' }]}
          readonly={readonly}
          initialValue={false}
        />
        <ProFormDependency name={['api', 'check_caller']}>
          {({ api }) => {
            if (api?.check_caller) {
              return (
                <ProFormText
                  name={['api', 'caller']}
                  label="主调方"
                  rules={[
                    { required: true, message: '请输入主调方' },
                    noSpaceValidationRule,
                  ]}
                  readonly={readonly}
                />
              );
            }
            return null;
          }}
        </ProFormDependency>
        <ProFormText
          name={['api', 'service_name']}
          label="服务名称"
          rules={[
            { required: true, message: '请输入服务名称' },
            noSpaceValidationRule,
          ]}
          readonly={readonly}
        />
        <ProFormText
          name={['api', 'api_name']}
          label="API 名称"
          rules={[
            { required: true, message: '请输入API名称' },
            noSpaceValidationRule,
          ]}
          readonly={readonly}
        />
      </>
    );
  }

  return (
    <>
      <ProFormText
        name={['kafka', 'address']}
        label="KAFKA地址"
        placeholder="请输入KAFKA地址"
        rules={[
          { required: true, message: '请输入KAFKA地址' },
          noSpaceValidationRule,
        ]}
        readonly={readonly}
      />
      <ProFormText
        name={['kafka', 'topic']}
        label="KAFKA主题"
        placeholder="请输入KAFKA主题"
        rules={[
          { required: true, message: '请输入KAFKA主题' },
          kafkaTopicValidationRule,
        ]}
        readonly={readonly}
      />
      <ProFormText
        name={['kafka', 'group']}
        label="KAFKA 消费组"
        placeholder="请输入KAFKA消费组"
        rules={[
          noSpaceValidationRule,
        ]}
        readonly={readonly}
      />
      <ProFormText
        name={['kafka', 'producer_id']}
        label="KAFKA 生产者 ID"
        rules={[
          noSpaceValidationRule,
        ]}
        readonly={readonly}
      />
      <ProFormSelect
        name={['kafka', 'compression']}
        label="压缩方式"
        options={KAFKA_COMPRESSION_OPTIONS}
        readonly={readonly}
      />
      <ProFormSelect
        name={['kafka', 'partitioner']}
        label="分区策略"
        options={KAFKA_PARTITIONER_OPTIONS}
        readonly={readonly}
      />
      <ProFormSwitch
        name={['kafka', 'read_acl_strategy', 'status']}
        label="是否启用读 ACL"
        readonly={readonly}
      />
      <ProFormDependency name={['kafka', 'read_acl_strategy', 'status']}>
        {({ kafka }) => {
          if (kafka?.read_acl_strategy?.status) {
            return (
              <>
                <ProFormText
                  name={['kafka', 'read_acl_strategy', 'user']}
                  label="读 ACL 用户名"
                  readonly={readonly}
                  rules={[
                    noSpaceValidationRule,
                  ]}
                />
                <ProFormText.Password
                  name={['kafka', 'read_acl_strategy', 'password']}
                  label="读 ACL 密码"
                  readonly={readonly}
                  rules={[
                    noSpaceValidationRule,
                  ]}
                />
              </>
            );
          }
          return null;
        }}
      </ProFormDependency>
      <ProFormSwitch
        name={['kafka', 'write_acl_strategy', 'status']}
        label="是否启用写 ACL"
        readonly={readonly}
      />
      <ProFormDependency name={['kafka', 'write_acl_strategy', 'status']}>
        {({ kafka }) => {
          if (kafka?.write_acl_strategy?.status) {
            return (
              <>
                <ProFormText
                  name={['kafka', 'write_acl_strategy', 'user']}
                  label="写 ACL 用户名"
                  readonly={readonly}
                  rules={[
                    noSpaceValidationRule,
                  ]}
                />
                <ProFormText.Password
                  name={['kafka', 'write_acl_strategy', 'password']}
                  label="写 ACL 密码"
                  readonly={readonly}
                  rules={[
                    noSpaceValidationRule,
                  ]}
                />
              </>
            );
          }
          return null;
        }}
      </ProFormDependency>
      <ProFormSwitch
        name={['kafka', 'log_only']}
        label="是否只打印日志，不实际下发"
        readonly={readonly}
      />
    </>
  );
});
