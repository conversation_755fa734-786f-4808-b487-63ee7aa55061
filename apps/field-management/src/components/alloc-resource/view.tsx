import { memo, useEffect, useRef, useMemo } from 'react';
import { useMemoizedFn } from 'ahooks';
import { useAtom, useDerived } from 'helux';
import { ModalForm, ProFormInstance } from '@ant-design/pro-components';
import type { ProducerAllocResourceRequest, ConsumerAllocResourceRequest } from '@/types/api';
import { orderAtom, isAPIRes, orderActions, useOrderLoading } from '@/model/order';
import type { BusinessMode } from '@/types/common';
import { ResourceType, OrderType } from '@/types/api';
import { ConsumerAllocResourceForm } from './consumer';
import { ProducerAllocResourceForm } from './producer';

export interface ViewResourceModalProps {
  mode: BusinessMode;
}

type FormValues = ProducerAllocResourceRequest | ConsumerAllocResourceRequest;

const ViewResourceModal = ({ mode }: ViewResourceModalProps) => {
  const [orderState] = useAtom(orderAtom);
  const [isAPI] = useDerived(isAPIRes);
  const { setOrderDetail } = useOrderLoading();
  const formRef = useRef<ProFormInstance<FormValues>>(null);

  const handleFinish = useMemoizedFn(async () => {
    orderActions.setViewResourceOpen(false);
    return true;
  });

  const handleOpenChange = useMemoizedFn((open: boolean) => {
    orderActions.setViewResourceOpen(open);
    if (!open) {
      orderActions.resetOrderDetail();
    }
  });

  // 确定标题
  const getTitle = () => {
    if (orderState.resourceType === ResourceType.RESOURCE_TYPE_TEST) {
      return '查看测试资源信息';
    }
    return '查看正式资源信息';
  };

  const initialValues = useMemo(() => {
    if (!orderState.currentOrder) {
      return {};
    }
    const { type } = orderState.currentOrder || {};
    const isTestResource = orderState.resourceType === ResourceType.RESOURCE_TYPE_TEST;
    if (type === OrderType.ORDER_TYPE_CONSUMER_CREATE || type === OrderType.ORDER_TYPE_CONSUMER_UPDATE) {
      const { subscribe_service: subscribeService } = orderState.curConsumer || {};
      const { test_api: testApi, prod_api: prodApi, test_kafka: testKafka, prod_kafka: prodKafka } = subscribeService || {};
      if (isAPI) {
        const apiService = isTestResource ? testApi : prodApi;
        return {
          api: {
            check_app_key: apiService?.check_app_key,
            app_key: apiService?.app_key,
            check_caller: apiService?.check_caller,
            service_name: apiService?.service_name,
            api_name: apiService?.api_name,
          },
        };
      }
      const kafkaService = isTestResource ? testKafka : prodKafka;
      return {
        kafka: kafkaService,
      };
    }
    if (type === OrderType.ORDER_TYPE_PRODUCER_CREATE || type === OrderType.ORDER_TYPE_PRODUCER_UPDATE) {
      const {
        kafka_addr,
        kafka_topic,
        test_kafka_addr,
        test_kafka_topic,
        api_name,
      } = orderState?.curProducer || {};
      if (isAPI) {
        return {
          api: {
            api_name: api_name?.split(',') || undefined,
          },
        };
      }
      return {
        kafka: {
          address: isTestResource ? test_kafka_addr : kafka_addr,
          topic: isTestResource ? test_kafka_topic : kafka_topic,
        },
      };
    }
    return {};
  }, [
    orderState.currentOrder,
    isAPI,
    orderState.curConsumer,
    orderState.curProducer,
    orderState.resourceType,
  ]);

  useEffect(() => {
    if (!formRef.current || !orderState.viewResourceOpen) {
      return;
    }
    formRef.current.setFieldsValue(initialValues);
  }, [formRef.current, orderState.viewResourceOpen, initialValues]);

  const handleInit = useMemoizedFn((_: FormValues, form: ProFormInstance<FormValues>) => {
    form.setFieldsValue(initialValues);
  });

  return (
    <ModalForm<FormValues>
      formRef={formRef}
      title={getTitle()}
      layout="horizontal"
      width={600}
      open={orderState.viewResourceOpen}
      onFinish={handleFinish}
      onOpenChange={handleOpenChange}
      submitter={{
        searchConfig: {
          submitText: '关闭',
        },
      }}
      readonly
      onInit={handleInit}
      modalProps={{
        destroyOnClose: true,
        loading: setOrderDetail.loading,
      }}
    >
      {mode === 'consumer' ? (
        <ConsumerAllocResourceForm isAPI={isAPI} readonly />
      ) : (
        <ProducerAllocResourceForm isAPI={isAPI} readonly />
      )}
    </ModalForm>
  );
};

export default memo(ViewResourceModal);
