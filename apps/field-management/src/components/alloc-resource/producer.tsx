import { memo } from 'react';
import { useAtom } from 'helux';
import { ProFormText, ProFormSelect } from '@ant-design/pro-components';
import { orderAtom } from '@/model/order';
import { kafkaTopicValidationRule, noSpaceValidationRule } from '@/constants/rules';

interface ProducerAllocResourceFormProps {
  isAPI: boolean;
  readonly?: boolean;
}

export const ProducerAllocResourceForm = memo(({ isAPI, readonly = false }: ProducerAllocResourceFormProps) => {
  const [orderState] = useAtom(orderAtom);
  if (isAPI) {
    return (
      <>
        <ProFormSelect
          name={['api', 'api_name']}
          label="API 名称"
          rules={[{ required: true, message: '请输入API名称' }]}
          options={[]}
          mode="tags"
          readonly={readonly}
        />
        <ProFormText
          label="服务名称"
          readonly
          fieldProps={{
            value: orderState.curProducer?.receiver_service,
          }}
          rules={[
            noSpaceValidationRule,
          ]}
        />
      </>
    );
  }
  return (
    <>
      <ProFormText
        name={['kafka', 'address']}
        label="KAFKA地址"
        placeholder="请输入KAFKA地址"
        rules={[
          { required: true, message: '请输入KAFKA地址' },
          noSpaceValidationRule,
        ]}
        readonly={readonly}
      />
      <ProFormText
        name={['kafka', 'topic']}
        label="KAFKA主题"
        placeholder="请输入KAFKA主题"
        rules={[
          { required: true, message: '请输入KAFKA主题' },
          kafkaTopicValidationRule,
        ]}
        readonly={readonly}
      />
    </>
  );
});
