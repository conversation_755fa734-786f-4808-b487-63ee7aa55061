import { useMemo } from 'react';
import { Button, ButtonProps, Tooltip } from 'antd';
import { useAccess } from '@/hooks/useAccess';

export interface AuthButtonProps extends ButtonProps {
  auth?: number;
  authUsers?: string | string[];
  tooltip?: string;
  adminAccess?: boolean;
  adminOnly?: boolean;
}

export const AuthButton: React.FC<AuthButtonProps> = ({ auth, tooltip, authUsers, adminAccess, adminOnly, ...props }) => {
  const { hasAuth, userInfo, isAdmin } = useAccess();
  const isAdminRole = isAdmin();

  const hasUserAuth = useMemo(() => {
    if (adminOnly) {
      return isAdminRole;
    }
    if (adminAccess && isAdminRole) {
      return true;
    }
    const formattedAuthUsers = Array.isArray(authUsers) ? authUsers : authUsers?.split(';');
    if (formattedAuthUsers?.length) {
      return formattedAuthUsers.includes(userInfo.name);
    }
    if (auth) {
      return hasAuth(auth);
    }
    return true;
  }, [authUsers, userInfo.name, hasAuth, auth, adminOnly, adminAccess, isAdminRole]);

  const defaultTips = useMemo(() => {
    if (adminOnly) {
      return '无权限，只有管理员可以操作';
    }
    if (authUsers) {
      return `无权限，只有${Array.isArray(authUsers) ? authUsers.join('、') : authUsers}可以操作`;
    }
    return '无权限，请前往盘古申请';
  }, [authUsers]);

  if (hasUserAuth) {
    return <Button {...props} />;
  }
  return (
    <Tooltip title={tooltip || defaultTips}>
      <Button {...props} disabled />
    </Tooltip>
  );
};
