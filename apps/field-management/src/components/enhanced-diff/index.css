.enhanced-diff-container {
  width: 100%;
}

.enhanced-diff-container .diff-card-normal {
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.enhanced-diff-container .diff-card-old {
  border: 1px solid #faad14;
  background: linear-gradient(135deg, #fff7e6 0%, #ffffff 100%);
  transition: all 0.3s ease;
  position: relative;
}

.enhanced-diff-container .diff-card-old::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: #faad14;
  border-radius: 2px 0 0 2px;
}

.enhanced-diff-container .diff-card-new {
  border: 1px solid #52c41a;
  background: linear-gradient(135deg, #f6ffed 0%, #ffffff 100%);
  transition: all 0.3s ease;
  position: relative;
}

.enhanced-diff-container .diff-card-new::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: #52c41a;
  border-radius: 2px 0 0 2px;
}

.enhanced-diff-container .diff-card-old .ant-pro-card-header {
  background: rgba(250, 173, 20, 0.1);
  border-bottom: 1px solid #faad14;
}

.enhanced-diff-container .diff-card-new .ant-pro-card-header {
  background: rgba(82, 196, 26, 0.1);
  border-bottom: 1px solid #52c41a;
}

.enhanced-diff-container .diff-card-old:hover {
  box-shadow: 0 4px 12px rgba(250, 173, 20, 0.15);
  transform: translateY(-2px);
}

.enhanced-diff-container .diff-card-new:hover {
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.15);
  transform: translateY(-2px);
}

.enhanced-diff-container .diff-card-normal:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enhanced-diff-container .ant-row {
    flex-direction: column;
  }
  
  .enhanced-diff-container .ant-col {
    width: 100% !important;
    margin-bottom: 16px;
  }
  
  .enhanced-diff-container .diff-card-old::before,
  .enhanced-diff-container .diff-card-new::before {
    width: 100%;
    height: 4px;
    top: 0;
    left: 0;
    border-radius: 2px 2px 0 0;
  }
}

/* Badge 样式优化 */
.enhanced-diff-container .ant-badge-count {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Tag 样式优化 */
.enhanced-diff-container .ant-tag {
  border-radius: 12px;
  padding: 4px 12px;
  font-weight: 500;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
