import React, { useMemo } from 'react';
import { Row, Col, Badge, Tag, Space } from 'antd';
import { ProCard } from '@ant-design/pro-components';
import { isEqual } from 'lodash';
import './index.css';

interface EnhancedDiffProps {
  /** 旧数据 */
  oldData?: any;
  /** 新数据 */
  newData?: any;
  /** 左侧标题 */
  leftTitle?: string;
  /** 右侧标题 */
  rightTitle?: string;
  /** 左侧内容渲染组件 */
  leftContent?: React.ReactNode;
  /** 右侧内容渲染组件 */
  rightContent?: React.ReactNode;
  /** 是否显示差异统计 */
  showDiffStats?: boolean;
}

/**
 * 深度比较两个对象，计算差异统计
 */
const calculateDiffStats = (oldData: any, newData: any) => {
  if (!oldData && !newData) {
    return { hasChanges: false, changeCount: 0 };
  }
  
  if (!oldData || !newData) {
    return { hasChanges: true, changeCount: 1 };
  }
  
  const hasChanges = !isEqual(oldData, newData);
  
  // 简单的变更计数（实际项目中可能需要更精确的算法）
  let changeCount = 0;
  
  const countChanges = (obj1: any, obj2: any, path = '') => {
    if (typeof obj1 !== typeof obj2) {
      changeCount++;
      return;
    }
    
    if (typeof obj1 === 'object' && obj1 !== null && obj2 !== null) {
      const keys1 = Object.keys(obj1);
      const keys2 = Object.keys(obj2);
      const allKeys = new Set([...keys1, ...keys2]);
      
      allKeys.forEach(key => {
        const newPath = path ? `${path}.${key}` : key;
        if (!(key in obj1)) {
          changeCount++; // 新增
        } else if (!(key in obj2)) {
          changeCount++; // 删除
        } else if (!isEqual(obj1[key], obj2[key])) {
          if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
            countChanges(obj1[key], obj2[key], newPath);
          } else {
            changeCount++; // 修改
          }
        }
      });
    } else if (obj1 !== obj2) {
      changeCount++;
    }
  };
  
  if (hasChanges) {
    countChanges(oldData, newData);
  }
  
  return { hasChanges, changeCount };
};

const EnhancedDiff: React.FC<EnhancedDiffProps> = ({
  oldData,
  newData,
  leftTitle = '变更前',
  rightTitle = '变更后',
  leftContent,
  rightContent,
  showDiffStats = true,
}) => {
  const diffStats = useMemo(() => {
    return calculateDiffStats(oldData, newData);
  }, [oldData, newData]);

  const renderTitle = (title: string, isNew: boolean) => {
    if (!showDiffStats || !diffStats.hasChanges) {
      return title;
    }
    
    return (
      <Space>
        {title}
        {diffStats.hasChanges && (
          <Badge 
            count={diffStats.changeCount} 
            style={{ 
              backgroundColor: isNew ? '#52c41a' : '#faad14',
              fontSize: '12px',
            }}
            title={`${diffStats.changeCount} 处变更`}
          />
        )}
      </Space>
    );
  };

  const renderDiffStatus = () => {
    if (!showDiffStats) return null;
    
    if (!diffStats.hasChanges) {
      return (
        <div style={{ textAlign: 'center', marginBottom: 16 }}>
          <Tag color="success">无变更</Tag>
        </div>
      );
    }
    
    return (
      <div style={{ textAlign: 'center', marginBottom: 16 }}>
        <Tag color="warning">检测到 {diffStats.changeCount} 处变更</Tag>
      </div>
    );
  };

  return (
    <div className="enhanced-diff-container">
      {renderDiffStatus()}
      <Row gutter={16}>
        <Col span={12}>
          <ProCard 
            title={renderTitle(leftTitle, false)}
            bordered 
            headerBordered 
            direction='column'
            className={diffStats.hasChanges ? 'diff-card-old' : 'diff-card-normal'}
          >
            {leftContent}
          </ProCard>
        </Col>
        <Col span={12}>
          <ProCard 
            title={renderTitle(rightTitle, true)}
            bordered 
            headerBordered 
            direction='column'
            className={diffStats.hasChanges ? 'diff-card-new' : 'diff-card-normal'}
          >
            {rightContent}
          </ProCard>
        </Col>
      </Row>
    </div>
  );
};

export default EnhancedDiff;
