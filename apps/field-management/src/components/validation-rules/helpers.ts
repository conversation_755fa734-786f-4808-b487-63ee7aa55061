import type { Rule } from 'antd/es/form';
import { ValueType } from '@/constants/field';
import { ValidationRule, ValidationRuleObject } from './types';
// 不同数据类型的校验规则选项
export const VALIDATION_OPTIONS = {
  [ValueType.BOOL]: [
    { label: '允许为空', value: 'allow_null' },
    { label: '必填字段', value: 'required' },
  ],
  [ValueType.INT]: [
    { label: '小于', value: 'lt' },
    { label: '小于等于', value: 'lte' },
    { label: '大于', value: 'gt' },
    { label: '大于等于', value: 'gte' },
    { label: '枚举数值', value: 'in' },
    { label: '不在枚举中', value: 'not_in' },
    { label: '允许为空', value: 'allow_null' },
    { label: '必填字段', value: 'required' },
  ],
  [ValueType.INT_ARRAY]: [
    { label: '数组长度', value: 'arr_len' },
    { label: '数组最小长度', value: 'arr_min_len' },
    { label: '数组最大长度', value: 'arr_max_len' },
    { label: '小于', value: 'lt' },
    { label: '小于等于', value: 'lte' },
    { label: '大于', value: 'gt' },
    { label: '大于等于', value: 'gte' },
    { label: '枚举数值', value: 'in' },
    { label: '不在枚举中', value: 'not_in' },
    { label: '允许为空', value: 'allow_null' },
    { label: '必填字段', value: 'required' },
  ],
  [ValueType.FLOAT]: [
    { label: '小于', value: 'lt' },
    { label: '小于等于', value: 'lte' },
    { label: '大于', value: 'gt' },
    { label: '大于等于', value: 'gte' },
    { label: '枚举数值', value: 'in' },
    { label: '不在枚举中', value: 'not_in' },
    { label: '允许为空', value: 'allow_null' },
    { label: '必填字段', value: 'required' },
  ],
  [ValueType.FLOAT_ARRAY]: [
    { label: '数组长度', value: 'arr_len' },
    { label: '数组最小长度', value: 'arr_min_len' },
    { label: '数组最大长度', value: 'arr_max_len' },
    { label: '小于', value: 'lt' },
    { label: '小于等于', value: 'lte' },
    { label: '大于', value: 'gt' },
    { label: '大于等于', value: 'gte' },
    { label: '枚举数值', value: 'in' },
    { label: '不在枚举中', value: 'not_in' },
    { label: '允许为空', value: 'allow_null' },
    { label: '必填字段', value: 'required' },
  ],
  [ValueType.STRING]: [
    { label: '长度', value: 'len' },
    { label: '最小长度', value: 'min_len' },
    { label: '最大长度', value: 'max_len' },
    { label: '枚举值', value: 'in' },
    { label: '不在枚举中', value: 'not_in' },
    { label: '正则表达式', value: 'pattern' },
    { label: '允许为空', value: 'allow_null' },
    { label: '必填字段', value: 'required' },
  ],
  [ValueType.STRING_ARRAY]: [
    { label: '数组长度', value: 'arr_len' },
    { label: '数组最小长度', value: 'arr_min_len' },
    { label: '数组最大长度', value: 'arr_max_len' },
    { label: '长度', value: 'len' },
    { label: '最小长度', value: 'min_len' },
    { label: '最大长度', value: 'max_len' },
    { label: '枚举值', value: 'in' },
    { label: '不在枚举中', value: 'not_in' },
    { label: '正则表达式', value: 'pattern' },
    { label: '允许为空', value: 'allow_null' },
    { label: '必填字段', value: 'required' },
  ],
  [ValueType.OBJECT]: [
    { label: '允许为空', value: 'allow_null' },
    { label: '必填字段', value: 'required' },
  ],
  [ValueType.OBJECT_ARRAY]: [
    { label: '数组长度', value: 'arr_len' },
    { label: '数组最小长度', value: 'arr_min_len' },
    { label: '数组最大长度', value: 'arr_max_len' },
    { label: '允许为空', value: 'allow_null' },
    { label: '必填字段', value: 'required' },
  ],
  [ValueType.ARRAY]: [
    { label: '允许为空', value: 'allow_null' },
    { label: '必填字段', value: 'required' },
  ],
  [ValueType.MAP]: [
    { label: '允许为空', value: 'allow_null' },
    { label: '必填字段', value: 'required' },
  ],
};

// 判断是否为枚举类型的规则
export const isEnumRule = (parameter: string): boolean => ['in', 'not_in'].includes(parameter);

// 判断是否为布尔类型的规则
export const isBooleanRule = (parameter: string): boolean => ['allow_null', 'required'].includes(parameter);

// 获取规则的帮助说明
export const getRuleDescription = (parameter: string): string => {
  switch (parameter) {
    case 'lt':
      return '数值小于此值';
    case 'lte':
      return '数值小于或等于此值';
    case 'gt':
      return '数值大于此值';
    case 'gte':
      return '数值大于或等于此值';
    case 'in':
      return '枚举值，请逐个输入枚举值，输入后先选中再继续添加';
    case 'not_in':
      return '不在枚举中，请逐个输入枚举值，输入后先选中再继续添加';
    case 'allow_null':
      return '是否允许为空值 (0: 不允许[默认], 1: 允许)';
    case 'required':
      return '是否必填字段';
    case 'arr_len':
      return '数组长度';
    case 'arr_min_len':
      return '数组最小长度';
    case 'arr_max_len':
      return '数组最大长度';
    case 'len':
      return '长度';
    case 'min_len':
      return '最小长度';
    case 'max_len':
      return '最大长度';
    case 'pattern':
      return '正则表达式';
    default:
      return '';
  }
};

// 将规则数组转换为对象格式
export const rulesToObject = (rules: ValidationRule[]): ValidationRuleObject => {
  const obj: ValidationRuleObject = {};
  return rules.reduce((obj, rule) => {
    obj[rule.parameter] = rule.value;
    return obj;
  }, obj);
};

// 将对象格式转换为规则数组
export const objectToRules = (obj?: ValidationRuleObject): ValidationRule[] => {
  // 如果已经是数组格式，直接返回
  if (Array.isArray(obj)) {
    return obj;
  }
  // 将对象转换为数组格式
  return Object.entries(obj || {}).map(([parameter, value]) => ({
    parameter,
    value,
  }));
};

export const getRuleValidations = (parameter: string): Rule[] => {
  // 数值类型规则
  if (['lt', 'lte', 'gt', 'gte', 'arr_len', 'arr_min_len', 'arr_max_len', 'len', 'min_len', 'max_len'].includes(parameter)) {
    return [
      { required: true, message: '请输入数值' },
      { type: 'number', min: 0, message: '数值不能小于0' },
    ];
  }
  // 枚举类型规则
  if (isEnumRule(parameter)) {
    return [
      { required: true, message: '请输入枚举值，多个值用英文逗号分隔' },
      // 数字英文字母，英文逗号，最不能以逗号开始和结束
      { pattern: /^[0-9a-zA-Z]+(,[0-9a-zA-Z]+)*$/, message: '请输入有效的枚举值，多个值用英文逗号分隔' },
    ];
  }
  // 正则表达式规则
  if (parameter === 'pattern') {
    return [
      { required: true, message: '请输入正则表达式' },
      {
        validator: (_: any, value: string) => {
          try {
            if (value) new RegExp(value);
            return Promise.resolve();
          } catch (e) {
            return Promise.reject(new Error('请输入有效的正则表达式'));
          }
        },
      },
    ];
  }
  // 默认规则
  return [
    { required: true, message: '请输入值' },
  ];
};

export const getRuleInitialValue = (parameter: string) => {
  if (['required', 'allow_null'].includes(parameter)) {
    return 0;
  }
  return undefined;
};
