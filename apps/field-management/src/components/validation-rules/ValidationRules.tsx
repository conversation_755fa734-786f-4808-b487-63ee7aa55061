import React, { useState, useEffect } from 'react';
import { Form, Select, InputNumber, Button, Space, Input, Switch, Flex } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { useMemoizedFn } from 'ahooks';
import { ValueType } from '@/constants/field';
import { ValidationRule, ValidationRulesProps } from './types';
import {
  VALIDATION_OPTIONS,
  isBooleanRule,
  isEnumRule,
  getRuleDescription,
  objectToRules,
  rulesToObject,
  getRuleValidations,
  getRuleInitialValue,
} from './helpers';

const { Option } = Select;

const ValidationRules: React.FC<ValidationRulesProps> = ({ valueType, value, onChange }) => {
  // 内部使用数组格式，但对外输出对象格式
  const [rules, setRules] = useState<ValidationRule[]>(objectToRules(value));

  // 当外部value变化时更新内部状态
  useEffect(() => {
    if (value) {
      setRules(objectToRules(value));
    }
  }, [value]);

  const handleChange = useMemoizedFn((value: ValidationRule[]) => {
    const newRules = value.map((item) => {
      if (isEnumRule(item.parameter)) {
        if (typeof item.value === 'string') {
          item.value = item.value?.includes('[') && item.value?.includes(']') ? JSON.parse(item.value) : item.value?.split(',').map(item => item.trim());
        }
        if (valueType === ValueType.INT || valueType === ValueType.FLOAT || valueType === ValueType.INT_ARRAY || valueType === ValueType.FLOAT_ARRAY) {
          item.value = item.value?.map((item: string) => Number(item));
        } else if (valueType === ValueType.STRING || valueType === ValueType.STRING_ARRAY) {
          item.value = item.value?.map((item: string) => String(item));
        }
      }
      return item;
    });
    setRules(newRules);
    onChange?.(rulesToObject(newRules));
  });

  // 获取当前数据类型可用的校验规则选项
  const getOptions = useMemoizedFn(() => VALIDATION_OPTIONS[valueType as keyof typeof VALIDATION_OPTIONS] || []);

  // 添加新规则
  const handleAddRule = useMemoizedFn(() => {
    const options = getOptions();
    if (options.length === 0) return;

    // 找到一个未使用的规则
    const usedParameters = rules.map(rule => rule.parameter);
    const availableOption = options.find(option => !usedParameters.includes(option.value));

    if (availableOption) {
      // 为布尔类型规则设置默认值
      let defaultValue;
      if (isBooleanRule(availableOption.value)) {
        defaultValue = 0; // 默认为0（不允许/非必填）
      }
      const newRules = [...rules, { parameter: availableOption.value, value: defaultValue }];
      handleChange(newRules);
    }
  });

  // 删除规则
  const handleRemoveRule = useMemoizedFn((index: number) => {
    const newRules = [...rules];
    newRules.splice(index, 1);
    handleChange(newRules);
  });

  // 更新规则参数
  const handleParameterChange = useMemoizedFn((index: number, parameter: string) => {
    const newRules = [...rules];

    // 为布尔类型规则设置默认值
    let defaultValue;
    if (isBooleanRule(parameter)) {
      defaultValue = 0; // 默认为0（不允许/非必填）
    }

    newRules[index] = { ...newRules[index], parameter, value: defaultValue };
    handleChange(newRules);
  });

  // 更新规则值
  const handleValueChange = useMemoizedFn((index: number, value: any) => {
    const newRules = [...rules];
    newRules[index] = { ...newRules[index], value };
    handleChange(newRules);
  });

  // 获取已使用的规则参数
  const getUsedParameters = useMemoizedFn(() => rules.map(rule => rule.parameter));

  // 渲染值输入组件
  const renderValueInput = useMemoizedFn((rule: ValidationRule, index: number) => {
    const { parameter } = rule;

    // 布尔类型规则（允许为空、必填字段）
    if (isBooleanRule(parameter)) {
      return (
        <Switch
          checked={rule.value === 1}
          onChange={checked => handleValueChange(index, checked ? 1 : 0)}
          checkedChildren="是"
          unCheckedChildren="否"
        />
      );
    }

    // 枚举类型规则（in、not_in）
    if (isEnumRule(parameter)) {
      return (
        <Select
          placeholder="请逐个输入枚举值，输入后先选中再继续添加"
          value={rule.value}
          mode="tags"
          showSearch={false}
          style={{ width: '100%' }}
          onChange={value => handleValueChange(index, value)}
        />
      );
    }

    // 数值类型规则
    if (
      ['lt', 'lte', 'gt', 'gte', 'arr_len', 'arr_min_len', 'arr_max_len', 'len', 'min_len', 'max_len'].includes(parameter)
    ) {
      return (
        <InputNumber
          placeholder="请输入数值"
          value={rule.value}
          onChange={value => handleValueChange(index, value)}
          style={{ width: '100%' }}
        />
      );
    }

    // 正则表达式规则
    if (parameter === 'pattern') {
      return (
        <Input
          placeholder="请输入正则表达式"
          value={rule.value}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleValueChange(index, e.target.value)}
        />
      );
    }

    // 默认输入框
    return (
      <Input
        placeholder="请输入值"
        value={rule.value}
        onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleValueChange(index, e.target.value)}
      />
    );
  });

  // 获取可用的规则选项（排除已使用的）
  const getAvailableOptions = useMemoizedFn((currentParameter: string) => {
    const options = getOptions();
    const usedParameters = getUsedParameters();

    // 当前规则的参数可以选择，其他已使用的参数不可选择
    return options.map(option => ({
      ...option,
      disabled: option.value !== currentParameter && usedParameters.includes(option.value),
    }));
  });

  return (
    <div className="validation-rules">
      <Space direction="vertical" style={{ width: '100%' }}>
        {rules.map((rule, index) => (
          <Flex justify="space-between" align='center' key={rule.parameter}>
            <div style={{ width: '100%', marginBottom: 10 }}>
              <Form.Item
                label="校验规则"
                tooltip={getRuleDescription(rule.parameter)}
                style={{ marginBottom: 5, width: '100%' }}
              >
                <Select
                  value={rule.parameter}
                  onChange={value => handleParameterChange(index, value)}
                  style={{ width: '100%' }}
                >
                  {getAvailableOptions(rule.parameter).map(option => (
                    <Option
                      key={option.value}
                      value={option.value}
                      disabled={option.disabled}
                    >
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item
                label="校验值"
                style={{ marginBottom: 0, width: '100%' }}
                rules={getRuleValidations(rule.parameter)}
                required
                initialValue={getRuleInitialValue(rule.parameter)}
              >
                {renderValueInput(rule, index)}
              </Form.Item>
            </div>
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleRemoveRule(index)}
            />
          </Flex>
        ))}

        <Button
          type="dashed"
          onClick={handleAddRule}
          block
          icon={<PlusOutlined />}
          disabled={getOptions().length === rules.length}
        >
          添加校验规则
        </Button>

        {rules.length === 0 && (
          <div style={{ textAlign: 'center', padding: '16px 0', color: '#999' }}>
            暂无校验规则，点击上方按钮添加
          </div>
        )}
      </Space>
    </div>
  );
};

export default ValidationRules;
