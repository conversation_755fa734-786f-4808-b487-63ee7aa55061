import { ProDescriptions } from '@ant-design/pro-components';
import { ORDER_TYPE_MAP, ORDER_STATUS_MAP, ORDER_STEP_MAP } from '@/constants/order';
import { OrderDetail } from '@/types/api';

interface OrderDetailProps {
  order?: OrderDetail;
}

export default function OrderInfo({ order }: OrderDetailProps) {
  return (
    <ProDescriptions
      title="工单信息"
      bordered
      column={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 2, xxl: 2 }}
    >
      <ProDescriptions.Item label="工单ID">
        {order?.order_id}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="工单类型" valueEnum={ORDER_TYPE_MAP} valueType='select'>
        {order?.type}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="申请人">
        {order?.applicant}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="工单状态" valueEnum={ORDER_STATUS_MAP} valueType='select'>
        {order?.status}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="当前步骤" valueEnum={ORDER_STEP_MAP} valueType='select'>
        {order?.step}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="流云ID">
        {order?.workflow_id}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="创建时间" valueType='dateTime'>
        {order?.create_time}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="更新时间" valueType='dateTime'>
        {order?.update_time}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="工单描述" span={3}>
        {order?.description}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="TAPD地址" span={3}>
        {order?.tapd}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="审核意见" span={3}>
        {order?.comment}
      </ProDescriptions.Item>
    </ProDescriptions>
  );
}
