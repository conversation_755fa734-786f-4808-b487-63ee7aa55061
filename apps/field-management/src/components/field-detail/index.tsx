import { ProDescriptions } from '@ant-design/pro-components';
import { FIELD_CLASS_MAP, FIELD_STATUS_MAP, BLOCK_RULE_MAP } from '@/constants/field';
import { fetchEntityTypes, fetchColumnFamilies } from '@/services/field';
import { FieldDetail as FieldDetailType } from '@/types/api';

interface FieldDetailProps {
  field?: FieldDetailType | null;
  title?: string;
  style?: React.CSSProperties;
  span?: number;
  isOrder?: boolean;
}

export default function FieldDetail({ field, title = '字段基本信息', style, span = 1, isOrder = false }: FieldDetailProps) {
  if (!field) {
    return null;
  }
  return (
    <ProDescriptions
      title={title}
      bordered
      style={style}
      column={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 2, xxl: 2 }}
    >
      <ProDescriptions.Item label="字段分类" valueEnum={FIELD_CLASS_MAP} valueType='select' span={span}>
        {field?.field_class}
      </ProDescriptions.Item>
      <ProDescriptions.Item
        label="介质类型"
        request={fetchEntityTypes}
        params={{ field_class: field?.field_class }}
        valueType='select'
        span={span}
      >
        {field?.entity_type}
      </ProDescriptions.Item>
      <ProDescriptions.Item
        label="列簇名称"
        request={fetchColumnFamilies}
        valueType='select'
        span={span}
        params={{
          field_class: field?.field_class,
          entity_type: field?.entity_type,
        }}
      >
        {field?.column_family}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="源字段路径" span={span}>
        {field?.src_field_path}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="字段路径" span={span}>
        {field?.field_path}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="字段名称" span={span}>
        {field?.field_name}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="数据类型" span={span}>
        {field?.value_type}
      </ProDescriptions.Item>
      {isOrder ? null : (
        <ProDescriptions.Item label="字段状态" valueEnum={FIELD_STATUS_MAP} valueType='select' span={span}>
          {field?.status}
        </ProDescriptions.Item>
      )}
      <ProDescriptions.Item label="负责人" span={6}>
        {field?.owner}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="字段描述" span={6}>
        {field?.description}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="校验规则" span={6}>
        {field?.validation}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="字段示例" span={6}>
        {field?.example}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="生产逻辑" span={6}>
        {field?.produce_logic}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="拦截规则" valueEnum={BLOCK_RULE_MAP} valueType='select' span={6}>
        {field?.block_rule}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="TAPD地址" span={6}>
        {field?.tapd}
      </ProDescriptions.Item>
    </ProDescriptions>
  );
}
