import { share, derive } from 'helux';
import pick from 'lodash/pick';
import { fetchConsumerOrderDetail, offlineConsumer } from '@/services/consumer';
import { fetchProducerOrderDetail, offlineProducer, fetchProducerDetail } from '@/services/producer';
import { fetchFieldOrderDetail, offlineField } from '@/services/field';
import {
  type OrderDetail,
  type Consumer,
  type Producer,
  type FieldDetail,
  type OrderUpdateData,
  type OrderStep,
  InvokeType,
  ResourceType,
  OrderType,
} from '@/types/api';
import { TEST_STEPS } from '@/constants/order';
import type { OrderBusinessMode } from '@/types/common';

export interface OfflineFieldDetail extends FieldDetail {
  consumer_count?: number;
}

const [orderAtom, setOrderAtom, orderCtx] = share({
  offlineTitle: '下线',
  offlineType: OrderType.ORDER_TYPE_UNKNOWN,
  offlineOpen: false,
  resourceOpen: false,
  viewResourceOpen: false,
  currentOrder: null as OrderDetail | null,
  curConsumer: null as Consumer | null,
  curProducer: null as Producer | null,
  offlineProducer: null as Producer | null,
  offlineFields: [] as OfflineFieldDetail[] | null,
  currentField: null as FieldDetail | null,
  appName: '',
  resourceType: ResourceType.RESOURCE_TYPE_UNKNOWN,
});

const isConsumer = derive(() => Boolean(orderAtom.curConsumer));
const isProducer = derive(() => Boolean(orderAtom.curProducer));

const isConsumerKafka = derive(() => isConsumer.val && orderAtom.curConsumer?.subscribe_service?.invoke_type === InvokeType.INVOKE_TYPE_KAFKA);
const isConsumerAPI = derive(() => isConsumer.val && orderAtom.curConsumer?.subscribe_service?.invoke_type === InvokeType.INVOKE_TYPE_API);

const isProducerKafka = derive(() => isProducer.val && orderAtom.curProducer?.invoke_type === InvokeType.INVOKE_TYPE_KAFKA);
const isProducerAPI = derive(() => isProducer.val && orderAtom.curProducer?.invoke_type === InvokeType.INVOKE_TYPE_API);

const isKafkaRes = derive(() => isConsumerKafka.val || isProducerKafka.val);
const isAPIRes = derive(() => isConsumerAPI.val || isProducerAPI.val);

const isTestOrder = derive(() => TEST_STEPS.includes(orderAtom.currentOrder?.step as OrderStep));

interface Payloads {
  setOfflineOpen: boolean;
  setResourceOpen: boolean;
  setViewResourceOpen: boolean;
  setAppName: string;
  setCurrentOrder: OrderDetail | null;
  setCurConsumer: Consumer | null;
  setCurProducer: Producer | null;
  setCurrentField: FieldDetail | null;
  setOrderDetail: {
    id?: number;
    type: OrderBusinessMode;
  };
  setOfflineProducer: string;
  offline: {
    order?: OrderUpdateData;
    type: OrderBusinessMode;
  };
  setResourceType: ResourceType;
  setOfflineTitle: string;
  setOfflineType: OrderType;
}

const { actions: orderActions, useLoading: useOrderLoading } = orderCtx.defineActions<Payloads>()({
  setOfflineOpen({ draft, payload }) {
    draft.offlineOpen = payload;
  },
  setResourceOpen({ draft, payload }) {
    draft.resourceOpen = payload;
  },
  setViewResourceOpen({ draft, payload }) {
    draft.viewResourceOpen = payload;
  },
  setAppName({ draft, payload }) {
    draft.appName = payload;
  },
  setCurrentOrder({ draft, payload }) {
    draft.currentOrder = payload;
  },
  setCurConsumer({ draft, payload }) {
    draft.curConsumer = payload;
  },
  setCurProducer({ draft, payload }) {
    draft.curProducer = payload;
  },
  setCurrentField({ draft, payload }) {
    draft.currentField = payload;
  },
  resetOrderDetail({ draft }) {
    draft.currentOrder = null;
    draft.curConsumer = null;
    draft.curProducer = null;
    draft.currentField = null;
    draft.appName = '';
    draft.resourceType = ResourceType.RESOURCE_TYPE_UNKNOWN;
  },
  async setOrderDetail({ draft, payload }) {
    const { id, type } = payload;
    if (type === 'consumer') {
      const res = await fetchConsumerOrderDetail({ order_id: id });
      draft.currentOrder = res?.order || null;
      draft.curConsumer = res?.consumer || null;
    } else if (type === 'producer') {
      const res = await fetchProducerOrderDetail({ order_id: id });
      draft.currentOrder = res?.order || null;
      draft.curProducer = res?.producer || null;
    } else if (type === 'field') {
      const res = await fetchFieldOrderDetail({ order_id: id });
      draft.currentOrder = res?.order || null;
      draft.currentField = res?.field || null;
    }
  },
  async setOfflineProducer({ draft, payload }) {
    const res = await fetchProducerDetail({ app_name: payload });
    draft.offlineProducer = res?.producer || null;
    const { fieldDetails } = res || {};
    const offlineFields = fieldDetails?.map(item => ({
      ...item.field_detail,
      consumer_count: item.consumer_count,
    })) || null;
    draft.offlineFields = offlineFields?.filter(item => item.consumer_count) || [];
  },
  async offline({ payload }) {
    const { order, type } = payload;
    if (type === 'consumer') {
      const res = await offlineConsumer({
        order,
        app_name: orderAtom.appName,
      });
      return res;
    }
    if (type === 'producer') {
      const res = await offlineProducer({
        order,
        app_name: orderAtom.appName,
      });
      return res;
    }
    const res = await offlineField({
      order,
      field_class: orderAtom.currentField?.field_class,
      fields: [pick(orderAtom.currentField, ['entity_type', 'column_family', 'field_path'])],
    });
    return res;
  },
  setResourceType({ draft, payload }) {
    draft.resourceType = payload;
  },
  setOfflineTitle({ draft, payload }) {
    draft.offlineTitle = payload;
  },
  setOfflineType({ draft, payload }) {
    draft.offlineType = payload;
  },
});

export {
  orderAtom,
  orderActions,
  setOrderAtom,
  useOrderLoading,
  isConsumer,
  isProducer,
  isConsumerKafka,
  isConsumerAPI,
  isProducerKafka,
  isProducerAPI,
  isKafkaRes,
  isAPIRes,
  isTestOrder,
};
