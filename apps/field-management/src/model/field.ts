import { share } from 'helux';
import { type FieldDetail } from '@/types/api';

const [fieldAtom, setFieldAtom, orderCtx] = share({
  formOpen: false,
  currentField: null as FieldDetail | null,
});

interface Payloads {
  setFormOpen: boolean;
  setCurrentField: FieldDetail | null;
}

const { actions: fieldActions, useLoading: useFieldLoading } = orderCtx.defineActions<Payloads>()({
  setFormOpen({ draft, payload }) {
    draft.formOpen = payload;
  },
  setCurrentField({ draft, payload }) {
    draft.currentField = payload;
  },
});

export {
  fieldAtom,
  fieldActions,
  setFieldAtom,
  useFieldLoading,
};
