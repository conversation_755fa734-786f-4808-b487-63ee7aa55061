import { share } from 'helux';
import Cookies from 'js-cookie';
import { fetchUserName, fetchRoleList, fetchAuths } from '@packages/services';
import type { UserInfo } from '@packages/types';
import { allAuths, allRoles } from '@/constants/auth';

const staffname = Cookies.get('staffname');

const [userAtom, setUserAtom, userCtx] = share<UserInfo>({
  name: staffname || '',
  avatar: `//r.hrc.woa.com/photo/100/${staffname}.png`,
  auths: [],
  roles: [],
  authLoading: true,
}, {
  moduleName: 'auth',
});

type Payloads = {
  setUserName: void;
  setRoleList: void;
  setAuths: void;
};

const { actions: authActions, useLoading: useAuthLoading } = userCtx.defineActions<Payloads>()({
  async setUserName({ draft }) {
    try {
      const res = await fetchUserName();
      draft.name = res;
      draft.avatar = `//r.hrc.woa.com/photo/100/${res}.png`;
    } catch (error) {
      console.error('获取用户名失败:', error);
      // 使用 cookie 中的用户名作为备选
      draft.name = staffname || '未知用户';
    }
  },
  async setRoleList({ draft }) {
    try {
      if (process.env.NODE_ENV === 'development') {
        draft.roles = Object.values(allRoles);
        return;
      }
      const ruleList = await fetchRoleList();
      draft.roles = ruleList.map(item => item.roleId);
    } catch (error) {
      console.error('获取角色列表失败:', error);
      draft.roles = []; // 设置为空数组，避免权限检查出错
    }
  },
  async setAuths({ draft }) {
    try {
      if (process.env.NODE_ENV === 'development') {
        draft.auths = Object.values(allAuths);
        draft.authLoading = false;
        return;
      }
      const authList = await fetchAuths();
      draft.auths = authList.map(item => item.id);
    } catch (error) {
      console.error('获取权限列表失败:', error);
      draft.auths = []; // 设置为空数组，避免权限检查出错
    } finally {
      // 无论成功还是失败，都要设置 authLoading 为 false
      draft.authLoading = false;
    }
  },
});

export {
  userAtom,
  authActions,
  useAuthLoading,
  setUserAtom,
};
