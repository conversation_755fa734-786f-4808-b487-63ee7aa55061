import { ProLayout, MenuDataItem } from '@ant-design/pro-components';
import { Link, Outlet, useLocation } from 'react-router-dom';
import { ConfigProvider, App as AntdApp } from 'antd';
import { useAtom } from 'helux';
import zhCN from 'antd/locale/zh_CN';
import { ROUTES } from '@/routes';
import { userAtom } from '@/model/auth';
import { getEnvText } from '@/utils/env';
import { GlobalLoading } from '@/components/global-loading';

function App() {
  const [userInfo] = useAtom(userAtom);
  const location = useLocation();

  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        components: {
          Descriptions: {
            labelBg: '#f0f0f0',
          },
          Table: {
            headerBg: '#f0f0f0',
          },
        },
      }}
    >
      <AntdApp>
        <div style={{ height: '100vh' }}>
          <ProLayout
            title={`总库管理系统-${getEnvText()}`}
            layout="mix"
            fixedHeader={true}
            route={{ routes: ROUTES }}
            location={{ pathname: location.pathname }}
            logo="https://tnfe.gtimg.com/maker/prod/statics/a9538b863438e4ab532008512.png"
            avatarProps={{
              src: userInfo.avatar,
              title: userInfo.name,
              size: 'small',
            }}
            menuDataRender={(menuData) => {
              // 递归过滤菜单项
              const filterMenuData = (menus: MenuDataItem[]): MenuDataItem[] => menus
                .filter(item => !item.auth || userInfo.auths.includes(item.auth))
                .map((item) => {
                  // 如果有子菜单，也需要递归过滤
                  if (item.children && item.children.length > 0) {
                    return {
                      ...item,
                      children: filterMenuData(item.children),
                    };
                  }
                  return item;
                });
              return filterMenuData(menuData);
            }}
            menuItemRender={(item, dom) => (
              <Link to={item.path || '/'}>
                {dom}
              </Link>
            )}
          >
            {userInfo.authLoading ? <GlobalLoading /> : <Outlet />}
          </ProLayout>
        </div>
      </AntdApp>
    </ConfigProvider>
  );
}

export default App;
