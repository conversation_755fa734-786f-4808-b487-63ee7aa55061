import { useEffect, useRef } from 'react';
import { App } from 'antd';
import { useMemoizedFn, useDebounceFn } from 'ahooks';
import { Storage } from '@tencent/qn-storage';

interface UseDraftOptions<T> {
  key: string;
  disabled?: boolean;
  onLoadDraft: (draft: T) => void;
}

export function useDraft<T>({ key, onLoadDraft, disabled }: UseDraftOptions<T>) {
  const hasCheckedRef = useRef(false);
  const { modal } = App.useApp();
  useEffect(() => {
    if (disabled) {
      return;
    }
    if (hasCheckedRef.current) {
      return;
    }
    const draft = Storage.local.getItem(key);
    if (draft) {
      hasCheckedRef.current = true;
      modal.confirm({
        title: '发现草稿',
        content: '检测到上次编辑的草稿，是否继续编辑？',
        okText: '继续编辑',
        cancelText: '重新开始',
        onOk: () => {
          onLoadDraft(draft);
        },
        onCancel: () => {
          clearDraft();
        },
      });
    }
  }, [key, disabled, onLoadDraft]);

  const saveDraft = useMemoizedFn((newValues: T) => {
    Storage.local.setItem(key, newValues);
  });

  const { run: saveDraftDebounce } = useDebounceFn((newValues: T) => {
    saveDraft(newValues);
  }, { wait: 1000 });

  const clearDraft = useMemoizedFn(() => {
    Storage.local.removeItem(key);
  });

  return {
    saveDraft: saveDraftDebounce,
    clearDraft,
  };
}
