import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { orderActions } from '@/model/order';
import { type AppSummary, AppStatus, OrderType } from '@/types/api';
import type { BusinessMode } from '@/types/common';

export const useAppList = (mode: BusinessMode) => {
  const navigate = useNavigate();

  const canEdit = useCallback((app: AppSummary) => app.status !== AppStatus.APP_STATUS_OFFLINE, []);

  const handleEdit = useCallback((app: AppSummary) => {
    navigate(`/${mode}/edit/${app.app_name}`);
  }, []);

  const handleDetail = useCallback((app: AppSummary) => {
    navigate(`/${mode}/detail/${app.app_name}`);
  }, []);

  const handleOffline = useCallback((app: AppSummary) => {
    orderActions.setAppName(app.app_name as string);
    const isConsumer = mode === 'consumer';
    if (!isConsumer) {
      orderActions.setOfflineProducer(app.app_name as string);
    }
    orderActions.setOfflineTitle(isConsumer ? '下线消费方' : '下线生产方');
    orderActions.setOfflineType(isConsumer ? OrderType.ORDER_TYPE_CONSUMER_OFFLINE : OrderType.ORDER_TYPE_PRODUCER_OFFLINE);
    orderActions.setOfflineOpen(true);
  }, [mode]);

  return {
    handleEdit,
    handleDetail,
    handleOffline,
    canEdit,
  };
};
