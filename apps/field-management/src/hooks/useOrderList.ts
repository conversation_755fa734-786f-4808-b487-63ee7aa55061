import { useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { TEST_RESOURCE_VIEW_STEPS, PROD_RESOURCE_VIEW_STEPS, ORDER_COLUMNS } from '@/constants/order';
import { useAccess } from '@/hooks/useAccess';
import { orderActions } from '@/model/order';
import { OrderDetail, OrderType, OrderStep, ResourceType, OrderStatus } from '@/types/api';
import type { OrderBusinessMode } from '@/types/common';

export const useOrderList = (mode: OrderBusinessMode) => {
  const { isAdmin } = useAccess();
  const navigate = useNavigate();

  const handleFlowDetail = useCallback((order: OrderDetail) => {
    window.open(`https://autoflow.woa.com/workflow/playbook/record/detail/${order.workflow_id}`, '_blank');
  }, []);

  const handleOrderDetial = useCallback((order: OrderDetail) => {
    navigate(`/${mode}/order/${order.order_id}`);
  }, [mode]);

  const handleAllocResource = useCallback((order: OrderDetail) => {
    orderActions.setCurrentOrder(order);
    orderActions.setOrderDetail({
      id: order.order_id,
      type: mode,
    });
    orderActions.setResourceOpen(true);
  }, [mode]);

  const handleViewTestResource = useCallback((order: OrderDetail) => {
    orderActions.setCurrentOrder(order);
    orderActions.setResourceType(ResourceType.RESOURCE_TYPE_TEST);
    orderActions.setOrderDetail({
      id: order.order_id,
      type: mode,
    });
    orderActions.setViewResourceOpen(true);
  }, [mode]);

  const handleViewProdResource = useCallback((order: OrderDetail) => {
    orderActions.setCurrentOrder(order);
    orderActions.setResourceType(ResourceType.RESOURCE_TYPE_PRODUCTION);
    orderActions.setOrderDetail({
      id: order.order_id,
      type: mode,
    });
    orderActions.setViewResourceOpen(true);
  }, [mode]);

  const canAllocResource = useCallback((order: OrderDetail) => {
    if (!isAdmin()) {
      return false;
    }
    const { type, step, status } = order;
    if (status !== OrderStatus.ORDER_STATUS_REVIEW) {
      return false;
    }
    if (type === OrderType.ORDER_TYPE_CONSUMER_CREATE) {
      return [OrderStep.CONSUMER_CREATE_STEP_ALLOC_TEST_RESOURCE, OrderStep.CONSUMER_CREATE_STEP_ALLOC_PROD_RESOURCE].includes(step as OrderStep);
    }
    if (type === OrderType.ORDER_TYPE_CONSUMER_UPDATE) {
      return [OrderStep.CONSUMER_UPDATE_STEP_ALLOC_TEST_RESOURCE, OrderStep.CONSUMER_UPDATE_STEP_ALLOC_PROD_RESOURCE].includes(step as OrderStep);
    }
    if (type === OrderType.ORDER_TYPE_PRODUCER_CREATE) {
      return [OrderStep.PRODUCER_CREATE_STEP_ALLOC_TEST_RESOURCE, OrderStep.PRODUCER_CREATE_STEP_ALLOC_PROD_RESOURCE].includes(step as OrderStep);
    }
    if (type === OrderType.ORDER_TYPE_PRODUCER_UPDATE) {
      return [OrderStep.PRODUCER_UPDATE_STEP_ALLOC_TEST_RESOURCE, OrderStep.PRODUCER_UPDATE_STEP_ALLOC_PROD_RESOURCE].includes(step as OrderStep);
    }
    return false;
  }, []);

  // 判断是否可以测试资源
  const canViewTestResource = useCallback((order: OrderDetail) => {
    const { type, step } = order;

    // 只有创建和更新类型的工单才能查看资源
    const createOrUpdateTypes = [
      OrderType.ORDER_TYPE_CONSUMER_CREATE,
      OrderType.ORDER_TYPE_CONSUMER_UPDATE,
      OrderType.ORDER_TYPE_PRODUCER_CREATE,
      OrderType.ORDER_TYPE_PRODUCER_UPDATE,
    ];

    if (!createOrUpdateTypes.includes(type as OrderType)) {
      return false;
    }

    // 测试资源的查看条件：分配测试资源后的步骤
    return TEST_RESOURCE_VIEW_STEPS.includes(step as OrderStep) || PROD_RESOURCE_VIEW_STEPS.includes(step as OrderStep);
  }, []);

  // 判断是否可以正式资源
  const canViewProdResource = useCallback((order: OrderDetail) => {
    const { type, step } = order;

    // 只有创建和更新类型的工单才能查看资源
    const createOrUpdateTypes = [
      OrderType.ORDER_TYPE_CONSUMER_CREATE,
      OrderType.ORDER_TYPE_CONSUMER_UPDATE,
      OrderType.ORDER_TYPE_PRODUCER_CREATE,
      OrderType.ORDER_TYPE_PRODUCER_UPDATE,
    ];

    if (!createOrUpdateTypes.includes(type as OrderType)) {
      return false;
    }

    // 正式资源的查看条件：分配正式资源后的步骤
    return PROD_RESOURCE_VIEW_STEPS.includes(step as OrderStep);
  }, []);

  const columns = useMemo(() => ORDER_COLUMNS.map((column) => {
    if (column.dataIndex === 'app_name') {
      return {
        ...column,
        title: mode === 'field' ? '字段名称' : '应用名称',
        hideInTable: mode === 'field',
      };
    }
    return column;
  }), []);

  return {
    handleFlowDetail,
    handleOrderDetial,
    handleAllocResource,
    handleViewTestResource,
    handleViewProdResource,
    canAllocResource,
    canViewTestResource,
    canViewProdResource,
    columns,
    isAdmin: isAdmin(),
  };
};
