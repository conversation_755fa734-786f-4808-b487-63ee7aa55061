import { useAtom } from 'helux';
import { userAtom } from '@/model/auth';
import { allRoles } from '@/constants/auth';

export function useAccess() {
  const [userInfo] = useAtom(userAtom);

  return {
    userInfo,
    /**
     * 检查是否有指定权限
     * @param auth 权限ID
     */
    hasAuth: (auth: number) => userInfo.auths.includes(auth),

    /**
     * 检查是否有指定角色
     * @param role 角色ID
     */
    hasRole: (role: number) => userInfo.roles.includes(role),

    /**
     * 检查是否是管理员
     */
    isAdmin: () => userInfo.roles.includes(allRoles.admin),

    /**
     * 检查是否是生产方
     */
    isProducer: () => userInfo.roles.includes(allRoles.producer),

    /**
     * 检查是否是消费方
     */
    isConsumer: () => userInfo.roles.includes(allRoles.consumer),
  };
}
