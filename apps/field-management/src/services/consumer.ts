import dayjs from 'dayjs';
import omit from 'lodash/omit';
import { requestGet, requestPost } from '@packages/utils';
import { userAtom } from '@/model/auth';
import type {
  GetConsumersRequest,
  GetConsumersReply,
  GetMyConsumersRequest,
  GetMyConsumersReply,
  GetConsumerDetailRequest,
  GetConsumerDetailReply,
  CreateConsumerRequest,
  CreateConsumerReply,
  UpdateConsumerRequest,
  UpdateConsumerReply,
  OfflineConsumerRequest,
  OfflineConsumerReply,
  GetOrdersRequest,
  GetOrdersReply,
  GetMyOrdersRequest,
  GetMyOrdersReply,
  GetConsumerOrderDetailRequest,
  GetConsumerOrderDetailReply,
  ConsumerAllocResourceRequest,
  AllocResourceReply,
} from '@/types/api';
import type { TablePage } from '@/types/common';

export const fetchConsumerOrders = async (params: GetOrdersRequest & TablePage) => {
  try {
    const res = await requestGet<GetOrdersReply>('/api/consumer/order/list', {
      ...omit(params, 'current', 'pageSize'),
      page_num: params.current,
      page_size: params.pageSize,
    });
    const { code, orders, total } = res;
    return {
      data: orders,
      total,
      success: code === 0,
    };
  } catch (error) {
    return {
      data: [],
      total: 0,
      success: false,
    };
  }
};

export const fetchConsumerOrderDetail = async (params: GetConsumerOrderDetailRequest) => {
  try {
    const res = await requestGet<GetConsumerOrderDetailReply>('/api/consumer/order/detail', params);
    const { code, order, consumer, application } = res;
    if (code !== 0) {
      return null;
    }
    return {
      order,
      consumer,
      application,
    };
  } catch (error) {
    return null;
  }
};

export const fetchMyConsumerOrders = async (params: GetMyOrdersRequest & TablePage) => {
  try {
    const res = await requestGet<GetMyOrdersReply>('/api/consumer/my/orders', {
      ...omit(params, 'current', 'pageSize'),
      page_num: params.current,
      page_size: params.pageSize,
      applicant: userAtom.name,
    });
    const { code, orders, total } = res;
    return {
      data: orders,
      success: code === 0,
      total,
    };
  } catch (error) {
    return {
      data: [],
      success: false,
      total: 0,
    };
  }
};

export const fetchConsumerApps = async (params: GetConsumersRequest & TablePage) => {
  try {
    const res = await requestGet<GetConsumersReply>('/api/consumer/list', {
      ...omit(params, 'current', 'pageSize'),
      page_num: params.current,
      page_size: params.pageSize,
    });
    const { code, apps, total } = res;
    return {
      data: apps,
      success: code === 0,
      total,
    };
  } catch (error) {
    return {
      data: [],
      success: false,
      total: 0,
    };
  }
};

export const fetchMyConsumerApps = async (params: GetMyConsumersRequest & TablePage) => {
  try {
    const res = await requestGet<GetMyConsumersReply>('/api/consumer/my', {
      ...omit(params, 'current', 'pageSize'),
      page_num: params.current,
      page_size: params.pageSize,
      owner: userAtom.name,
    });
    const { code, apps, total } = res;
    return {
      data: apps,
      success: code === 0,
      total,
    };
  } catch (error) {
    return {
      data: [],
      success: false,
      total: 0,
    };
  }
};

export const fetchConsumerDetail = async (params: GetConsumerDetailRequest) => {
  try {
    const res = await requestGet<GetConsumerDetailReply>('/api/consumer/detail', params);
    const { code, consumer, application, orders } = res;
    if (code !== 0) {
      return null;
    }
    return {
      consumer,
      application,
      // 按创建时间降序排序
      orders: orders?.sort((a, b) => dayjs(b.create_time).diff(dayjs(a.create_time))),
    };
  } catch (error) {
    return null;
  }
};

export const createConsumer = async (params: CreateConsumerRequest) => {
  try {
    const res = await requestPost<CreateConsumerReply>('/api/consumer/create', params);
    const { code, workflow_id, message } = res;
    return {
      success: code === 0,
      workflowId: workflow_id,
      message,
    };
  } catch (error) {
    return {
      success: false,
      workflowId: undefined,
      message: '创建失败',
    };
  }
};

export const updateConsumer = async (params: UpdateConsumerRequest) => {
  try {
    const res = await requestPost<UpdateConsumerReply>('/api/consumer/update', params);
    const { code, workflow_id, message } = res;
    return {
      success: code === 0,
      workflowId: workflow_id,
      message,
    };
  } catch (error) {
    return {
      success: false,
      workflowId: undefined,
      message: '更新失败',
    };
  }
};

export const offlineConsumer = async (params: OfflineConsumerRequest) => {
  try {
    const res = await requestPost<OfflineConsumerReply>('/api/consumer/offline', params);
    const { code, workflow_id, message } = res;
    return {
      success: code === 0,
      workflowId: workflow_id,
      message,
    };
  } catch (error) {
    return {
      success: false,
      workflowId: undefined,
      message: '提交失败',
    };
  }
};

export const allocConsumerResource = async (params: ConsumerAllocResourceRequest) => {
  try {
    const res = await requestPost<AllocResourceReply>('/api/consumer/resource/alloc', params);
    const { code, message } = res;
    return {
      success: code === 0,
      message,
    };
  } catch (error) {
    return {
      success: false,
      message: '分配资源失败',
    };
  }
};
