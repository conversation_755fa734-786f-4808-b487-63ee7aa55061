import { StorageType } from '@tencent/qn-storage';
import { requestGet, requestPost } from '@packages/utils';
import omit from 'lodash/omit';
import { userAtom } from '@/model/auth';
import { ColumnFamilyStatus, FieldClass } from '@/types/api';
import type {
  GetFieldExportableRequest,
  GetFieldExportableReply,
  GetFieldProducibleRequest,
  GetFieldProducibleReply,
  GetFieldsReply,
  GetFieldsRequest,
  GetEntityTypesRequest,
  GetEntityTypesReply,
  GetColumnFamiliesRequest,
  GetColumnFamiliesReply,
  UpdateFieldRequest,
  UpdateFieldReply,
  OfflineFieldRequest,
  OfflineFieldReply,
  GetFieldOrderDetailRequest,
  GetFieldOrderDetailReply,
  GetOrdersRequest,
  GetOrdersReply,
  GetMyOrdersRequest,
  GetMyOrdersReply,
  GetFieldDetailRequest,
  GetFieldDetailReply,
} from '@/types/api';
import { TablePage } from '@/types/common';

export const fetchAllFields = async (params: GetFieldsRequest & TablePage & { useCache?: boolean }) => {
  try {
    const formatParams = omit({
      ...params,
      page_num: params.current,
      page_size: params.pageSize,
    }, ['current', 'pageSize', 'useCache']);
    const cacheKey = Object.values(formatParams).join('_');
    const res = await requestGet<GetFieldsReply>('/api/field/list', formatParams, {
      cacheConfig: params.useCache ? {
        key: `fetch_all_fields_${cacheKey}`,
        type: StorageType.session,
      } : undefined,
    });
    const { code, field_details, total = 0 } = res;
    return {
      success: code === 0,
      data: field_details || [],
      total,
    };
  } catch (error) {
    return {
      success: false,
      data: [],
      total: 0,
    };
  }
};

export const fetchFieldDetail = async (params: GetFieldDetailRequest) => {
  try {
    const res = await requestGet<GetFieldDetailReply>('/api/field/detail', params);
    const { code, field, producers, consumers } = res;
    return {
      success: code === 0,
      field,
      producers,
      consumers,
    };
  } catch (error) {
    return {
      success: false,
      field: null,
      producers: [],
      consumers: [],
    };
  }
};

export const fetchConsumerFields = async (params: GetFieldExportableRequest & TablePage) => {
  try {
    const formatParams = omit({
      ...params,
      page_num: params.current,
      page_size: params.pageSize,
    }, ['current', 'pageSize']);
    const res = await requestGet<GetFieldExportableReply>('/api/field/exportable', formatParams);
    const { code, fields, total = 0 } = res;
    return {
      success: code === 0,
      data: fields || [],
      total,
    };
  } catch (error) {
    return {
      success: false,
      data: [],
      total: 0,
    };
  }
};

export const fetchProducerFields = async (params: GetFieldProducibleRequest & TablePage) => {
  try {
    const formatParams = omit({
      ...params,
      page_num: params.current,
      page_size: params.pageSize,
    }, ['current', 'pageSize']);
    const res = await requestGet<GetFieldProducibleReply>('/api/field/producible', formatParams);
    const { code, fields, total = 0 } = res;
    return {
      success: code === 0,
      data: fields || [],
      total,
    };
  } catch (error) {
    return {
      success: false,
      data: [],
      total: 0,
    };
  }
};

export const fetchEntityTypes = async (params: GetEntityTypesRequest & { currentEntityType?: string, keyWords?: string }) => {
  try {
    const formatParams = omit(params, ['currentEntityType', 'keyWords']);
    formatParams.field_class = params.field_class || FieldClass.FIELD_CLASS_STATIC;
    const cacheKey = Object.values(formatParams).join('_');
    const res = await requestGet<GetEntityTypesReply>('/api/field/entity_type/list', formatParams, {
      cacheConfig: {
        key: `fetch_entity_types_${cacheKey}`,
        type: StorageType.memory,
      },
    });
    const { code, entities } = res;
    if (code !== 0) {
      return [];
    }
    let formatEntities = entities?.map(entity => ({
      value: entity.type as string,
      label: `${entity.name_ch}（${entity.type}）`,
    })) || [];
    if (params.currentEntityType) {
      formatEntities = formatEntities.filter(entity => entity.value !== params.currentEntityType);
    }
    if (params.keyWords) {
      // @ts-ignore
      formatEntities = formatEntities.filter(entity => entity.label.includes(params.keyWords));
    }
    return formatEntities;
  } catch (error) {
    return [];
  }
};

export const fetchColumnFamilies = async (params: GetColumnFamiliesRequest & { keyWords?: string }) => {
  if (!params.entity_type) {
    return [];
  }
  try {
    const formatParams = omit({
      ...params,
      column_family_status: ColumnFamilyStatus.COLUMN_FAMILY_STATUS_ONLINE,
    }, ['currentEntityType']);
    formatParams.field_class = params.field_class || FieldClass.FIELD_CLASS_STATIC;
    const cacheKey = Object.values(formatParams).join('_');
    const res = await requestGet<GetColumnFamiliesReply>('/api/field/column_family/list', formatParams, {
      cacheConfig: {
        key: `fetch_column_families_${cacheKey}`,
        type: StorageType.memory,
      },
    });
    const { code, column_families: columnFamilies } = res;
    if (code !== 0) {
      return [];
    }
    let formatColumnFamilies = columnFamilies?.map(columnFamily => ({
      value: columnFamily.name as string,
      label: columnFamily.name as string,
    })) || [];
    const { keyWords } = params;
    if (keyWords) {
      // @ts-ignore
      formatColumnFamilies = formatColumnFamilies.filter(({ label, value }) => label.includes(keyWords) || value.includes(keyWords));
    }
    return formatColumnFamilies;
  } catch (error) {
    return [];
  }
};

export const updateField = async (params: UpdateFieldRequest) => {
  try {
    const res = await requestPost<UpdateFieldReply>('/api/field/update', params);
    const { code, message } = res;
    return {
      success: code === 0,
      message,
    };
  } catch (error) {
    return {
      success: false,
      message: '提交失败',
    };
  }
};

export const offlineField = async (params: OfflineFieldRequest) => {
  try {
    const res = await requestPost<OfflineFieldReply>('/api/field/offline', params);
    const { code, workflow_id, message } = res;
    return {
      success: code === 0,
      workflowId: workflow_id,
      message,
    };
  } catch (error) {
    return {
      success: false,
      workflowId: undefined,
      message: '提交失败',
    };
  }
};

export const fetchFieldOrders = async (params: GetOrdersRequest & TablePage) => {
  try {
    const formatParams = omit({
      ...params,
      page_num: params.current,
      page_size: params.pageSize,
    }, ['current', 'pageSize']);
    const res = await requestGet<GetOrdersReply>('/api/field/order/list', formatParams);
    const { code, orders, total = 0 } = res;
    return {
      success: code === 0,
      data: orders || [],
      total,
    };
  } catch (error) {
    return {
      success: false,
      data: [],
      total: 0,
    };
  }
};

export const fetchMyFieldOrders = async (params: GetMyOrdersRequest & TablePage) => {
  try {
    const formatParams = omit({
      ...params,
      page_num: params.current,
      page_size: params.pageSize,
      applicant: userAtom.name,
    }, ['current', 'pageSize']);
    const res = await requestGet<GetMyOrdersReply>('/api/field/my/orders', formatParams);
    const { code, orders, total = 0 } = res;
    return {
      success: code === 0,
      data: orders || [],
      total,
    };
  } catch (error) {
    return {
      success: false,
      data: [],
      total: 0,
    };
  }
};

export const fetchFieldOrderDetail = async (params: GetFieldOrderDetailRequest) => {
  try {
    const res = await requestGet<GetFieldOrderDetailReply>('/api/field/order/detail', params);
    const { code, order, fields } = res;
    if (code !== 0) {
      return null;
    }
    return {
      field: fields?.[0] || null,
      fields,
      order,
    };
  } catch (error) {
    return null;
  }
};
