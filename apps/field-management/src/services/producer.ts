import dayjs from 'dayjs';
import omit from 'lodash/omit';
import { requestGet, requestPost } from '@packages/utils';
import { userAtom } from '@/model/auth';
import { InvokeType } from '@/types/api';
import type {
  GetProducersRequest,
  GetProducersReply,
  GetMyProducersRequest,
  GetMyProducersReply,
  GetProducerDetailRequest,
  GetProducerDetailReply,
  CreateProducerRequest,
  CreateProducerReply,
  UpdateProducerRequest,
  UpdateProducerReply,
  OfflineProducerRequest,
  OfflineProducerReply,
  GetOrdersRequest,
  GetOrdersReply,
  GetMyOrdersRequest,
  GetMyOrdersReply,
  GetProducerOrderDetailRequest,
  GetProducerOrderDetailReply,
  ProducerAllocResourceRequest,
  AllocResourceReply,
} from '@/types/api';
import type { TablePage } from '@/types/common';

export const fetchProducerOrders = async (params: GetOrdersRequest & TablePage) => {
  try {
    const res = await requestGet<GetOrdersReply>('/api/producer/order/list', {
      ...omit(params, 'current', 'pageSize'),
      page_num: params.current,
      page_size: params.pageSize,
    });
    const { code, orders, total } = res;
    return {
      data: orders,
      total,
      success: code === 0,
    };
  } catch (error) {
    return {
      data: [],
      total: 0,
      success: false,
    };
  }
};

export const fetchProducerOrderDetail = async (params: GetProducerOrderDetailRequest) => {
  try {
    const res = await requestGet<GetProducerOrderDetailReply>('/api/producer/order/detail', params);
    const { code, order, producer, field_detail_sets: fieldDetailSets } = res;
    if (code !== 0) {
      return null;
    }
    return {
      order,
      producer,
      fieldDetailSets,
    };
  } catch (error) {
    return null;
  }
};

export const fetchMyProducerOrders = async (params: GetMyOrdersRequest & TablePage) => {
  try {
    const res = await requestGet<GetMyOrdersReply>('/api/producer/my/orders', {
      ...omit(params, 'current', 'pageSize'),
      page_num: params.current,
      page_size: params.pageSize,
      applicant: userAtom.name,
    });
    const { code, orders, total } = res;
    return {
      data: orders,
      total,
      success: code === 0,
    };
  } catch (error) {
    return {
      data: [],
      total: 0,
      success: false,
    };
  }
};

export const fetchProducerApps = async (params: GetProducersRequest & TablePage) => {
  try {
    const res = await requestGet<GetProducersReply>('/api/producer/list', {
      ...omit(params, 'current', 'pageSize'),
      page_num: params.current,
      page_size: params.pageSize,
    });
    const { code, apps, total } = res;
    return {
      data: apps,
      success: code === 0,
      total,
    };
  } catch (error) {
    return {
      data: [],
      success: false,
      total: 0,
    };
  }
};

export const fetchMyProducerApps = async (params: GetMyProducersRequest & TablePage) => {
  try {
    const res = await requestGet<GetMyProducersReply>('/api/producer/my', {
      ...omit(params, 'current', 'pageSize'),
      page_num: params.current,
      page_size: params.pageSize,
      owner: userAtom.name,
    });
    const { code, apps, total } = res;
    return {
      data: apps,
      success: code === 0,
      total,
    };
  } catch (error) {
    return {
      data: [],
      success: false,
      total: 0,
    };
  }
};

export const fetchProducerDetail = async (params: GetProducerDetailRequest) => {
  try {
    const res = await requestGet<GetProducerDetailReply>('/api/producer/detail', params);
    const { code, producer, orders, field_details } = res;
    if (code !== 0 || !producer) {
      return null;
    }
    const formattedProducer = {
      field_class: producer?.field_class,
      invoke_type: producer?.invoke_type,
      api: producer?.invoke_type === InvokeType.INVOKE_TYPE_API ? {
        caller: producer?.producer_service,
      } : undefined,
      rate_limit: producer?.rate_limit,
      field_sets: [] as any[],
      is_all_fields: producer?.is_all_fields,
    };
    // 根据entity_type分组字段
    if (field_details?.length) {
      // 获取所有唯一的entity_type
      const entityTypes = [...new Set(field_details.map(detail => detail.field_detail?.entity_type))];
      // 为每个entity_type创建一个field_set
      formattedProducer.field_sets = entityTypes.map((entityType) => {
        // 过滤出当前entity_type的所有字段
        const fieldsOfType = field_details?.filter(detail => detail.field_detail?.entity_type === entityType);
        return {
          entity_type: entityType,
          fields: fieldsOfType?.map(detail => detail.field_detail?.field_path) || [],
          add_fields: [],
        };
      });
    }
    return {
      producer,
      formattedProducer,
      // 按创建时间降序排序
      orders: orders?.sort((a, b) => dayjs(b.create_time).diff(dayjs(a.create_time))),
      fieldDetails: field_details,
    };
  } catch (error) {
    return null;
  }
};

export const createProducer = async (params: CreateProducerRequest) => {
  try {
    const res = await requestPost<CreateProducerReply>('/api/producer/create', params);
    const { code, workflow_id, message } = res;
    return {
      success: code === 0,
      workflowId: workflow_id,
      message,
    };
  } catch (error) {
    return {
      success: false,
      workflowId: undefined,
      message: '创建失败',
    };
  }
};

export const updateProducer = async (params: UpdateProducerRequest) => {
  try {
    const res = await requestPost<UpdateProducerReply>('/api/producer/update', params);
    const { code, workflow_id, message } = res;
    return {
      success: code === 0,
      workflowId: workflow_id,
      message,
    };
  } catch (error) {
    return {
      success: false,
      workflowId: undefined,
      message: '更新失败',
    };
  }
};

export const offlineProducer = async (params: OfflineProducerRequest) => {
  try {
    const res = await requestPost<OfflineProducerReply>('/api/producer/offline', params);
    const { code, workflow_id, message } = res;
    return {
      success: code === 0,
      workflowId: workflow_id,
      message,
    };
  } catch (error) {
    return {
      success: false,
      workflowId: undefined,
      message: '提交失败',
    };
  }
};

export const allocProducerResource = async (params: ProducerAllocResourceRequest) => {
  try {
    const res = await requestPost<AllocResourceReply>('/api/producer/resource/alloc', params);
    const { code, message } = res;
    return {
      success: code === 0,
      message,
    };
  } catch (error) {
    return {
      success: false,
      message: '分配资源失败',
    };
  }
};
