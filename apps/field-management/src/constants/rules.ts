export const jsonValidationRule = {
  validator: async (_: any, value: string) => {
    if (!value) {
      return Promise.resolve();
    }
    try {
      if (!value.startsWith('{') && !value.startsWith('[')) {
        return Promise.reject(new Error('请输入正确的 JSON 格式'));
      }
      JSON.parse(value);
    } catch (error) {
      return Promise.reject(new Error('请输入正确的 JSON 格式'));
    }
  },
};

export const fieldExampleValidationRule = {
  validator: async (_: any, value: string) => {
    if (!value) {
      return Promise.resolve();
    }
    try {
      if (!value.startsWith('{')) {
        return Promise.reject(new Error('请输入正确的 JSON 格式'));
      }
      JSON.parse(value);
    } catch (error) {
      return Promise.reject(new Error('请输入正确的 JSON 格式'));
    }
  },
};

const HttpUrlPattern = /^https?:\/\/[\w.-]+(?:\.[\w.-]+)+(?:\/[^\s?]*)?(?:\?[^\s]*)?$/;

export const httpUrlValidationRule = {
  pattern: HttpUrlPattern,
  message: '请输入正确的 URL 格式',
};

const NamePattern = /^[a-zA-Z0-9]+(?:[._-][a-zA-Z0-9]+)*$/;

export const appNameValidationRule = {
  pattern: NamePattern,
  message: '只允许英文字母、数字和下划线的组合，下划线不能在开头和结尾',
};

// kafka地址校验
const KafkaAddressPattern = /^[a-zA-Z0-9.-]+:\d+$/;

export const kafkaAddressValidationRule = {
  pattern: KafkaAddressPattern,
  message: '请输入正确的 Kafka 地址格式',
};

// kafka主题校验
const KafkaTopicPattern = /^[a-zA-Z0-9._-]+$/;

export const kafkaTopicValidationRule = {
  pattern: KafkaTopicPattern,
  message: '只允许英文字母、数字、点、下划线和横杠的组合',
};

// 不允许空格、换行符
export const noSpaceValidationRule = {
  validator: async (_: any, value: string) => {
    if (value?.includes(' ') || value?.includes('\n')) {
      return Promise.reject(new Error('不允许空格、换行符'));
    }
  },
};
