import type { ProColumns } from '@ant-design/pro-components';
import { OrderType, OrderStep, OrderStatus, OrderDetail } from '@/types/api';
import { getMapByOptions } from '@/utils';
import { getUsers } from '@/services/user';

export const FIELD_ORDER_TYPE_OPTIONS = [
  { label: '静态字段更新', value: OrderType.ORDER_TYPE_STATIC_FIELD_UPDATE },
  { label: '静态字段下线', value: OrderType.ORDER_TYPE_STATIC_FIELD_OFFLINE },
  { label: '动态字段更新', value: OrderType.ORDER_TYPE_DYNAMIC_FIELD_UPDATE },
  { label: '动态字段下线', value: OrderType.ORDER_TYPE_DYNAMIC_FIELD_OFFLINE },
];

export const PRODUCER_ORDER_TYPE_OPTIONS = [
  { label: '生产方创建', value: OrderType.ORDER_TYPE_PRODUCER_CREATE },
  { label: '生产方更新', value: OrderType.ORDER_TYPE_PRODUCER_UPDATE },
  { label: '生产方下线', value: OrderType.ORDER_TYPE_PRODUCER_OFFLINE },
];

export const CONSUMER_ORDER_TYPE_OPTIONS = [
  { label: '消费方创建', value: OrderType.ORDER_TYPE_CONSUMER_CREATE },
  { label: '消费方更新', value: OrderType.ORDER_TYPE_CONSUMER_UPDATE },
  { label: '消费方下线', value: OrderType.ORDER_TYPE_CONSUMER_OFFLINE },
];

export const ORDER_TYPE_OPTIONS = [
  ...FIELD_ORDER_TYPE_OPTIONS,
  ...PRODUCER_ORDER_TYPE_OPTIONS,
  ...CONSUMER_ORDER_TYPE_OPTIONS,
];

export const ORDER_TYPE_MAP = getMapByOptions<Record<OrderType, string>>(ORDER_TYPE_OPTIONS);

export const ORDER_STATUS_OPTIONS = [
  {
    label: '审核中',
    value: OrderStatus.ORDER_STATUS_REVIEW,
  },
  {
    label: '已通过',
    value: OrderStatus.ORDER_STATUS_APPROVE,
  },
  {
    label: '已拒绝',
    value: OrderStatus.ORDER_STATUS_REFUSAL,
  },
];

export const ORDER_STATUS_MAP = {
  [OrderStatus.ORDER_STATUS_REVIEW]: { text: '审核中', status: 'Processing' },
  [OrderStatus.ORDER_STATUS_APPROVE]: { text: '已通过', status: 'Success' },
  [OrderStatus.ORDER_STATUS_REFUSAL]: { text: '已拒绝', status: 'Error' },
};

export const ORDER_STEP_OPTIONS = [
  {
    label: '准备中...',
    value: OrderStep.ORDER_STEP_READY,
  },
  {
    label: '字段负责人审核',
    value: OrderStep.FIELD_UPDATE_STEP_FIELD_OWNER_AUDIT,
  },
  {
    label: '总库初审',
    value: OrderStep.FIELD_UPDATE_STEP_DATABUS_OWNER_AUDIT,
  },
  {
    label: '工单发起方联调测试确认',
    value: OrderStep.FIELD_UPDATE_STEP_DEBUG_CONFIRM,
  },
  {
    label: '总库终审',
    value: OrderStep.FIELD_UPDATE_STEP_DATABUS_OWNER_FINAL_AUDIT,
  },
  {
    label: '介质负责人审核',
    value: OrderStep.FIELD_OFFLINE_STEP_FIELD_OWNER_AUDIT,
  },
  {
    label: '总库审核',
    value: OrderStep.FIELD_OFFLINE_STEP_DATABUS_OWNER_AUDIT,
  },
  {
    label: '观察业务反馈确认',
    value: OrderStep.FIELD_OFFLINE_STEP_FEEDBACK_CONFIRM,
  },
  {
    label: '生产方上级审核',
    value: OrderStep.PRODUCER_CREATE_STEP_PRODUCER_LEADER_AUDIT,
  },
  {
    label: '介质负责人审批',
    value: OrderStep.PRODUCER_CREATE_STEP_ENTITY_OWNER_AUDIT,
  },
  {
    label: '总库初审',
    value: OrderStep.PRODUCER_CREATE_STEP_DATABUS_OWNER_AUDIT,
  },
  {
    label: '分配测试资源',
    value: OrderStep.PRODUCER_CREATE_STEP_ALLOC_TEST_RESOURCE,
  },
  {
    label: '生产方联调测试',
    value: OrderStep.PRODUCER_CREATE_STEP_DEBUG_CONFIRM,
  },
  {
    label: '总库终审',
    value: OrderStep.PRODUCER_CREATE_STEP_DATABUS_OWNER_FINAL_AUDIT,
  },
  {
    label: '分配正式资源',
    value: OrderStep.PRODUCER_CREATE_STEP_ALLOC_PROD_RESOURCE,
  },
  {
    label: '生产方上级审核',
    value: OrderStep.PRODUCER_UPDATE_STEP_PRODUCER_LEADER_AUDIT,
  },
  {
    label: '介质负责人审批',
    value: OrderStep.PRODUCER_UPDATE_STEP_ENTITY_OWNER_AUDIT,
  },
  {
    label: '总库初审',
    value: OrderStep.PRODUCER_UPDATE_STEP_DATABUS_OWNER_AUDIT,
  },
  {
    label: '分配测试资源',
    value: OrderStep.PRODUCER_UPDATE_STEP_ALLOC_TEST_RESOURCE,
  },
  {
    label: '生产方联调测试',
    value: OrderStep.PRODUCER_UPDATE_STEP_DEBUG_CONFIRM,
  },
  {
    label: '总库终审',
    value: OrderStep.PRODUCER_UPDATE_STEP_DATABUS_OWNER_FINAL_AUDIT,
  },
  {
    label: '分配正式资源',
    value: OrderStep.PRODUCER_UPDATE_STEP_ALLOC_PROD_RESOURCE,
  },
  {
    label: '生产方上级审核',
    value: OrderStep.PRODUCER_OFFLINE_STEP_PRODUCER_LEADER_AUDIT,
  },
  {
    label: '总库审核',
    value: OrderStep.PRODUCER_OFFLINE_STEP_DATABUS_OWNER_AUDIT,
  },
  {
    label: '消费方上级审核',
    value: OrderStep.CONSUMER_CREATE_STEP_CONSUMER_LEADER_AUDIT,
  },
  {
    label: '总库初审',
    value: OrderStep.CONSUMER_CREATE_STEP_DATABUS_OWNER_AUDIT,
  },
  {
    label: '分配测试资源',
    value: OrderStep.CONSUMER_CREATE_STEP_ALLOC_TEST_RESOURCE,
  },
  {
    label: '联调成功确认',
    value: OrderStep.CONSUMER_CREATE_STEP_DEBUG_CONFIRM,
  },
  {
    label: '分配正式资源',
    value: OrderStep.CONSUMER_CREATE_STEP_ALLOC_PROD_RESOURCE,
  },
  {
    label: '消费方上级审核',
    value: OrderStep.CONSUMER_UPDATE_STEP_CONSUMER_LEADER_AUDIT,
  },
  {
    label: '总库初审',
    value: OrderStep.CONSUMER_UPDATE_STEP_DATABUS_OWNER_AUDIT,
  },
  {
    label: '分配测试资源',
    value: OrderStep.CONSUMER_UPDATE_STEP_ALLOC_TEST_RESOURCE,
  },
  {
    label: '联调成功确认',
    value: OrderStep.CONSUMER_UPDATE_STEP_DEBUG_CONFIRM,
  },
  {
    label: '分配正式资源',
    value: OrderStep.CONSUMER_UPDATE_STEP_ALLOC_PROD_RESOURCE,
  },
  {
    label: '消费方上级审核',
    value: OrderStep.CONSUMER_OFFLINE_STEP_CONSUMER_LEADER_AUDIT,
  },
  {
    label: '总库审核',
    value: OrderStep.CONSUMER_OFFLINE_STEP_DATABUS_OWNER_AUDIT,
  },
  {
    label: '观察业务反馈确认',
    value: OrderStep.CONSUMER_OFFLINE_STEP_FEEDBACK_CONFIRM,
  },
  {
    label: '已结束',
    value: OrderStep.ORDER_STEP_FINAL,
  },
];

export const TEST_STEPS = [
  OrderStep.CONSUMER_CREATE_STEP_ALLOC_TEST_RESOURCE,
  OrderStep.CONSUMER_UPDATE_STEP_ALLOC_TEST_RESOURCE,
  OrderStep.PRODUCER_CREATE_STEP_ALLOC_TEST_RESOURCE,
  OrderStep.PRODUCER_UPDATE_STEP_ALLOC_TEST_RESOURCE,
];

// 测试资源查看步骤（分配测试资源后的步骤）
export const TEST_RESOURCE_VIEW_STEPS = [
  OrderStep.CONSUMER_CREATE_STEP_DEBUG_CONFIRM,
  OrderStep.CONSUMER_UPDATE_STEP_DEBUG_CONFIRM,
  OrderStep.PRODUCER_CREATE_STEP_DEBUG_CONFIRM,
  OrderStep.PRODUCER_UPDATE_STEP_DEBUG_CONFIRM,
];

// 正式资源查看步骤（分配正式资源后的步骤）
export const PROD_RESOURCE_VIEW_STEPS = [
  OrderStep.PRODUCER_CREATE_STEP_DATABUS_OWNER_FINAL_AUDIT,
  OrderStep.PRODUCER_UPDATE_STEP_DATABUS_OWNER_FINAL_AUDIT,
  OrderStep.ORDER_STEP_FINAL,
];

// 能够查看资源的步骤
export const RESOURCE_VIEW_STEPS = [
  ...TEST_RESOURCE_VIEW_STEPS,
  ...PROD_RESOURCE_VIEW_STEPS,
  OrderStep.CONSUMER_CREATE_STEP_ALLOC_PROD_RESOURCE,
  OrderStep.CONSUMER_UPDATE_STEP_ALLOC_PROD_RESOURCE,
  OrderStep.PRODUCER_CREATE_STEP_ALLOC_PROD_RESOURCE,
  OrderStep.PRODUCER_UPDATE_STEP_ALLOC_PROD_RESOURCE,
];

export const ORDER_STEP_MAP = getMapByOptions<Record<OrderStep, string>>(ORDER_STEP_OPTIONS);

export const ORDER_COLUMNS: ProColumns<OrderDetail>[] = [
  {
    title: '工单ID',
    dataIndex: 'order_id',
    width: 150,
  },
  {
    title: '工单类型',
    dataIndex: 'type',
    valueType: 'select',
    valueEnum: ORDER_TYPE_MAP,
    width: 120,
  },
  {
    title: '名称',
    dataIndex: 'app_name',
    width: 200,
    search: false,
  },
  {
    title: '申请人',
    dataIndex: 'applicant',
    valueType: 'select',
    request: getUsers,
    width: 200,
    fieldProps: {
      showSearch: true,
    },
  },
  {
    title: '审核意见',
    dataIndex: 'comment',
    search: false,
    ellipsis: true,
    minWidth: 100,
    hideInTable: true,
  },
  {
    title: 'TAPD 地址',
    dataIndex: 'tapd',
    search: false,
    hideInTable: true,
  },
  {
    title: '工单状态',
    dataIndex: 'status',
    valueEnum: ORDER_STATUS_MAP,
    width: 100,
  },
  {
    title: '当前步骤',
    dataIndex: 'step',
    valueEnum: ORDER_STEP_MAP,
    search: false,
    width: 150,
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
    valueType: 'dateTime',
    search: false,
    width: 200,
  },
  {
    title: '更新时间',
    dataIndex: 'update_time',
    valueType: 'dateTime',
    search: false,
    width: 200,
  },
];
