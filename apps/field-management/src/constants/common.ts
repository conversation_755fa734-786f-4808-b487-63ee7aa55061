import { getMapByOptions } from '@/utils';

export const searchFormLayout = {
  xs: 24,
  sm: 24,
  md: 12,
  lg: 12,
  xl: 8,
  xxl: 6,
};

export const ENTITY_TYPE_OPTIONS = [
  {
    label: '图文',
    value: 'article',
  },
  {
    label: '视频',
    value: 'video',
  },
  {
    label: '直播',
    value: 'live_streaming',
  },
  {
    label: '热点事件',
    value: 'hot_event',
  },
  {
    label: '长视频专辑',
    value: 'long_album',
  },
  {
    label: '长视频栏目',
    value: 'long_column',
  },
  {
    label: '长视频',
    value: 'long_video',
  },
  {
    label: '微博',
    value: 'weibo',
  },
  {
    label: '外显事件',
    value: 'thing',
  },
  {
    label: '问答',
    value: 'question',
  },
  {
    label: '日历',
    value: 'calendar',
  },
  {
    label: '评论',
    value: 'comment',
  },
  {
    label: 'CP',
    value: 'cp',
  },
  {
    label: '正能量',
    value: 'positive_energy',
  },
  {
    label: '推送数据',
    value: 'push',
  },
  {
    label: '标签',
    value: 'omg_tag',
  },
  {
    label: '有序列表',
    value: 'sorted_list',
  },
  {
    label: '体育赛程',
    value: 'sports_schedule',
  },
  {
    label: '用户',
    value: 'user',
  },
  {
    label: '短带长',
    value: 'video_scene',
  },
  {
    label: '投票',
    value: 'vote',
  },
  {
    label: '榜单',
    value: 'rank_sorted_list',
  },
];

export const ENTITY_TYPE_MAP = getMapByOptions<Record<string, string>>(ENTITY_TYPE_OPTIONS);

export const FILTER_TYPE_OPTIONS = [
  {
    label: '特征变化时下发upsert消息',
    value: 'change',
  },
  {
    label: '条件表达式',
    value: 'expr',
  },
  {
    label: '视图条件表达式',
    value: 'view',
  },
  {
    label: '视图条件表达式（无退出）',
    value: 'view_enter',
  },
  {
    label: '由全局下发条件决定',
    value: 'pass',
  },
];

export const DYNAMIC_FILTER_TYPE_OPTIONS = [
  {
    label: '全部下发',
    value: 'all',
  },
  {
    label: '条件表达式',
    value: 'expr',
  },
];

export const SWITCH_PROPS = {
  checkedChildren: '是',
  unCheckedChildren: '否',
};

export const FILTER_TYPE_MAP = getMapByOptions<Record<string, string>>(FILTER_TYPE_OPTIONS);

