import { ProColumns } from '@ant-design/pro-components';
import { fetchColumnFamilies, fetchEntityTypes } from '@/services/field';
import { getUsers } from '@/services/user';
import { FieldClass, FieldStatus, FieldDetail, BlockRule } from '@/types/api';
import { getMapByOptions } from '@/utils';

// 校验规则类型
export enum ValueType {
  BOOL = 'bool',
  INT = 'int',
  INT_ARRAY = '[]int',
  FLOAT = 'float64',
  FLOAT_ARRAY = '[]float64',
  STRING = 'string',
  STRING_ARRAY = '[]string',
  OBJECT = 'object',
  OBJECT_ARRAY = '[]object',
  ARRAY = 'array',
  MAP = 'map',
}

/** 字段分类 */
export const FIELD_CLASS_OPTIONS = [
  { label: '静态字段', value: FieldClass.FIELD_CLASS_STATIC },
  { label: '动态字段', value: FieldClass.FIELD_CLASS_DYNAMIC },
];

export const FIELD_CLASS_MAP = getMapByOptions<Record<FieldClass, string>>(FIELD_CLASS_OPTIONS);

/** 字段状态 */
export const FIELD_STATUS_OPTIONS = [
  { label: '正常', value: FieldStatus.FIELD_STATUS_ONLINE, status: 'success' },
  { label: '已下线', value: FieldStatus.FIELD_STATUS_OFFLINE, status: 'error' },
  { label: '不可消费', value: FieldStatus.FIELD_STATUS_UNCONSUMABLE, status: 'error' },
];

export const FIELD_STATUS_MAP = getMapByOptions<Record<FieldStatus, string>>(FIELD_STATUS_OPTIONS);

export const VALUE_TYPE_OPTIONS = [
  { label: ValueType.INT, value: ValueType.INT },
  { label: ValueType.BOOL, value: ValueType.BOOL },
  { label: ValueType.INT_ARRAY, value: ValueType.INT_ARRAY },
  { label: ValueType.FLOAT, value: ValueType.FLOAT },
  { label: ValueType.FLOAT_ARRAY, value: ValueType.FLOAT_ARRAY },
  { label: ValueType.STRING, value: ValueType.STRING },
  { label: ValueType.STRING_ARRAY, value: ValueType.STRING_ARRAY },
  { label: ValueType.OBJECT, value: ValueType.OBJECT },
  { label: ValueType.OBJECT_ARRAY, value: ValueType.OBJECT_ARRAY },
  { label: ValueType.ARRAY, value: ValueType.ARRAY },
  { label: ValueType.MAP, value: ValueType.MAP },
];

export const VALUE_TYPE_MAP = getMapByOptions<Record<ValueType, string>>(VALUE_TYPE_OPTIONS);

export const BLOCK_RULE_OPTIONS = [
  { label: '不拦截', value: BlockRule.BLOCK_RULE_NO },
  { label: '拦截校验失败的字段', value: BlockRule.BLOCK_RULE_BLOCK_ERR },
  { label: '拦截一级字段', value: BlockRule.BLOCK_RULE_BLOCK_FIRST },
  { label: '拦截二级字段', value: BlockRule.BLOCK_RULE_BLOCK_SECOND },
];

export const BLOCK_RULE_MAP = getMapByOptions<Record<BlockRule, string>>(BLOCK_RULE_OPTIONS);

export const COLUMN_FAMILY_TOOLTIP = '字段列簇是一组相关字段的集合，可包含任意多个字段，用来划定字段存储、校验拦截范围。例如，可设置该列簇下的任意字段异常时，将导致本次更新中该列簇下的所有字段更新失败。';

export const FIELD_COLUMNS: ProColumns<FieldDetail>[] = [
  {
    title: '介质类型',
    dataIndex: 'entity_type',
    width: 130,
    valueType: 'select',
    request: fetchEntityTypes,
    fieldProps: form => ({
      onChange: () => {
        form.setFieldsValue({
          column_family: undefined,
        });
      },
      showSearch: true,
    }),
    formItemProps: {
      rules: [{ required: true, message: '请选择介质类型' }],
    },
  },
  {
    title: '列簇名称',
    dataIndex: 'column_family',
    valueType: 'select',
    tooltip: COLUMN_FAMILY_TOOLTIP,
    width: 100,
    request: fetchColumnFamilies,
    dependencies: ['entity_type'],
    fieldProps: (form) => {
      const entityType = form?.getFieldValue('entity_type');
      if (!entityType) {
        return {
          disabled: true,
        };
      }
      return {
        showSearch: true,
      };
    },
  },
  {
    title: '字段路径',
    dataIndex: 'field_path',
    width: 200,
  },
  {
    title: '源字段路径',
    dataIndex: 'src_field_path',
    width: 200,
  },
  {
    title: '字段名称',
    dataIndex: 'field_name',
    width: 200,
  },
  {
    title: '数据类型',
    dataIndex: 'value_type',
    valueEnum: VALUE_TYPE_MAP,
    search: false,
    width: 100,
  },
  {
    title: '字段状态',
    dataIndex: 'status',
    valueEnum: FIELD_STATUS_MAP,
    width: 100,
  },
  {
    title: '字段描述',
    dataIndex: 'description',
    search: false,
    minWidth: 200,
    ellipsis: true,
    hideInTable: true,
  },
  {
    title: '校验规则',
    dataIndex: 'validation',
    valueType: 'jsonCode',
    search: false,
    width: 200,
    hideInTable: true,
  },
  {
    title: '字段示例',
    dataIndex: 'example',
    valueType: 'jsonCode',
    search: false,
    width: 200,
    hideInTable: true,
  },
  {
    title: '生产逻辑',
    dataIndex: 'produce_logic',
    valueType: 'jsonCode',
    search: false,
    width: 200,
    hideInTable: true,
  },
  {
    title: '负责人',
    dataIndex: 'owner',
    valueType: 'select',
    request: getUsers,
    width: 100,
    fieldProps: {
      showSearch: true,
    },
  },
  {
    title: 'TAPD地址',
    dataIndex: 'tapd',
    valueType: 'text',
    search: false,
    hideInTable: true,
  },
  {
    title: '拦截规则',
    dataIndex: 'block_rule',
    valueEnum: BLOCK_RULE_MAP,
    search: false,
    hideInTable: true,
    width: 100,
  },
];

export const SELECT_FIELD_COLUMNS: ProColumns<FieldDetail>[] = FIELD_COLUMNS.map((column) => {
  if (['example', 'produce_logic', 'description', 'validation'].includes(column.dataIndex as string)) {
    return {
      ...column,
      hideInTable: true,
    };
  }
  return column;
});
