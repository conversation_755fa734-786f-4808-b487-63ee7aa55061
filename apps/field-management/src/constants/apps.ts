import { ProColumns } from '@ant-design/pro-components';
import { AppStatus, AppSummary } from '@/types/api';
import { getMapByOptions } from '@/utils';
import { getUsers } from '@/services/user';
import { FIELD_CLASS_MAP } from '@/constants/field';
import { SUBSCRIBE_SERVICE_TYPE_MAP } from '@/constants/consumer';

export const APP_STATUS_OPTIONS = [
  {
    label: '正常',
    value: AppStatus.APP_STATUS_ONLINE,
  },
  {
    label: '已下线',
    value: AppStatus.APP_STATUS_OFFLINE,
  },
];

export const APP_STATUS_MAP = getMapByOptions<Record<AppStatus, string>>(APP_STATUS_OPTIONS);

export const APP_COLUMNS: ProColumns<AppSummary>[] = [
  {
    title: '应用ID',
    dataIndex: 'app_id',
    width: 100,
  },
  {
    title: '应用名称',
    dataIndex: 'app_name',
    width: 300,
  },
  {
    title: '应用中文名',
    dataIndex: 'app_name_ch',
    width: 300,
  },
  {
    dataIndex: 'field_class',
    title: '字段分类',
    valueType: 'select',
    valueEnum: FIELD_CLASS_MAP,
    width: 100,
  },
  {
    dataIndex: 'invoke_type',
    title: '对接方式',
    valueType: 'select',
    valueEnum: SUBSCRIBE_SERVICE_TYPE_MAP,
    width: 100,
  },
  {
    title: '负责人',
    dataIndex: 'owner',
    valueType: 'select',
    request: getUsers,
    width: 200,
    fieldProps: {
      showSearch: true,
    },
  },
  {
    title: '描述',
    dataIndex: 'description',
    search: false,
    ellipsis: true,
    minWidth: 200,
  },
  {
    title: '应用状态',
    dataIndex: 'status',
    valueEnum: APP_STATUS_MAP,
    width: 100,
  },
];
