import dayjs from 'dayjs';
import {
  SerializationType,
  EncodeType,
  ExpressionAction,
  InvokeType,
  SubscribeType,
  FieldFilterMode,
  FieldClass,
  ProtocolVersion,
} from '@/types/api';
import { FormatComsumer } from '@/types/consumer';
import { getMapByOptions } from '@/utils';

export const SUBSCRIBE_SERVICE_TYPE_OPTIONS = [
  { label: 'KAFKA', value: InvokeType.INVOKE_TYPE_KAFKA },
  { label: 'API', value: InvokeType.INVOKE_TYPE_API },
];

export const SUBSCRIBE_SERVICE_TYPE_MAP = getMapByOptions<Record<InvokeType, string>>(SUBSCRIBE_SERVICE_TYPE_OPTIONS);

/** Kafka 分区策略 */
export const KAFKA_PARTITIONER_OPTIONS = [
  { label: 'hash', value: 'hash' },
  { label: 'random', value: 'random' },
];

export const KAFKA_PARTITIONER_MAP = getMapByOptions<Record<string, string>>(KAFKA_PARTITIONER_OPTIONS);

/** Kafka 压缩方式 */
export const KAFKA_COMPRESSION_OPTIONS = [
  { label: '无', value: 'none' },
  { label: 'lz4', value: 'lz4' },
  { label: 'gzip', value: 'gzip' },
];

export const KAFKA_COMPRESSION_MAP = getMapByOptions<Record<string, string>>(KAFKA_COMPRESSION_OPTIONS);

/** 数据序列化类型 */
export const DATA_SERIALIZATION_TYPE_OPTIONS = [
  { label: 'JSON', value: SerializationType.SERIALIZATION_TYPE_JSON },
  { label: 'Protocol Buffers', value: SerializationType.SERIALIZATION_TYPE_PB },
];

export const DATA_SERIALIZATION_TYPE_MAP = getMapByOptions<Record<SerializationType, string>>(DATA_SERIALIZATION_TYPE_OPTIONS);

/** 数据编码类型 */
export const DATA_COMPRESSION_TYPE_OPTIONS = [
  { label: 'None', value: EncodeType.ENCODE_TYPE_NONE },
  { label: 'Base64', value: EncodeType.ENCODE_TYPE_BASE64 },
  { label: 'Snappy', value: EncodeType.ENCODE_TYPE_SNAPPY },
];

export const DATA_COMPRESSION_TYPE_MAP = getMapByOptions<Record<EncodeType, string>>(DATA_COMPRESSION_TYPE_OPTIONS);

/** 表达式动作 */
export const EXPRESSION_ACTION_OPTIONS = [
  { label: '符合该条件就下发upsert消息', value: ExpressionAction.ACTION_UPSERT },
  { label: '符合该条件就下发delete消息', value: ExpressionAction.ACTION_DELETE },
  { label: '符合该条件就不下发', value: ExpressionAction.ACTION_STOP },
];

export const EXPRESSION_ACTION_MAP = getMapByOptions<Record<ExpressionAction, string>>(EXPRESSION_ACTION_OPTIONS);

/** 订阅类型 */
export const SUBSCRIBE_TYPE_OPTIONS = [
  { label: '普通订阅', value: SubscribeType.SUBSCRIBE_TYPE_NORMAL },
  { label: '事件订阅', value: SubscribeType.SUBSCRIBE_TYPE_EVENT },
];

export const SUBSCRIBE_TYPE_MAP = getMapByOptions<Record<SubscribeType, string>>(SUBSCRIBE_TYPE_OPTIONS);

/** 数据来源,increment:总库变更流,api:手动触发,refresh:刷库任务 */
export enum DataSource {
  DATA_SOURCE_INCREMENT = 'increment',
  DATA_SOURCE_API = 'api',
  DATA_SOURCE_REFRESH = 'refresh',
  DATA_SOURCE_RELATION = 'relation',
  DATA_SOURCE_DYNAMIC = 'dynamic',
}
export const DATA_SOURCE_OPTIONS = [
  { label: '总库变更流', value: DataSource.DATA_SOURCE_INCREMENT },
  { label: '手动触发', value: DataSource.DATA_SOURCE_API },
  { label: '刷库任务', value: DataSource.DATA_SOURCE_REFRESH },
  { label: '关系流', value: DataSource.DATA_SOURCE_RELATION },
  { label: '动态字段边缘触发', value: DataSource.DATA_SOURCE_DYNAMIC },
];

export const DATA_SOURCE_MAP = getMapByOptions<Record<DataSource, string>>(DATA_SOURCE_OPTIONS);

export const LOG_LEVEL_OPTIONS = [
  { label: 'DEBUG', value: 'debug' },
  { label: 'INFO', value: 'info' },
  { label: 'WARN', value: 'warn' },
  { label: 'ERROR', value: 'error' },
];

export const LOG_LEVEL_MAP = getMapByOptions<Record<string, string>>(LOG_LEVEL_OPTIONS);

export const FIELD_FILTER_MODE_OPTIONS = [
  { label: '普通模式', value: FieldFilterMode.FIELD_FILTER_MODE_NORMAL },
  { label: '黑名单模式', value: FieldFilterMode.FIELD_FILTER_MODE_BLACK },
  { label: '全量模式', value: FieldFilterMode.FIELD_FILTER_MODE_ALL },
];

export const FIELD_FILTER_MODE_MAP = getMapByOptions<Record<FieldFilterMode, string>>(FIELD_FILTER_MODE_OPTIONS);

export const PROTOCOL_VERSION_OPTIONS = [
  { label: '动态普通订阅协议', value: ProtocolVersion.PROTOCOL_VERSION_V11 },
  { label: '动态事件订阅协议', value: ProtocolVersion.PROTOCOL_VERSION_V12 },
  { label: '统一分发协议', value: ProtocolVersion.PROTOCOL_VERSION_V20 },
];

export const PROTOCOL_VERSION_MAP = getMapByOptions<Record<ProtocolVersion, string>>(PROTOCOL_VERSION_OPTIONS);

export const kafkaInitialConsumer: FormatComsumer = {
  subscribe_fields: {
    serialization_type: SerializationType.SERIALIZATION_TYPE_JSON,
    encode_type: EncodeType.ENCODE_TYPE_NONE,
    field_sets: [],
    global_transform: {
      many: JSON.stringify({}),
      component: JSON.stringify({}),
    },
  },
  subscribe_control: {
    global_trigger: {
      status: false,
      field_changed: false,
    },
    need_full_data: false,
    need_pre_data: false,
    is_clean_filter: false,
    sources: [DataSource.DATA_SOURCE_INCREMENT],
    subscribe_type: SubscribeType.SUBSCRIBE_TYPE_NORMAL,
    field_filter_mode: FieldFilterMode.FIELD_FILTER_MODE_NORMAL,
    protocol_version: ProtocolVersion.PROTOCOL_VERSION_V20,
  },
  subscribe_service: {
    invoke_type: InvokeType.INVOKE_TYPE_KAFKA,
    field_class: FieldClass.FIELD_CLASS_STATIC,
  },
  observability: {
    log_sample: {
      sample_rate: 0.01,
      // @ts-ignore
      start_time: dayjs().add(14, 'day'),
    },
  },
};

export const apiInitialConsumer: FormatComsumer = {
  subscribe_fields: {
    field_sets: [],
  },
  subscribe_service: {
    invoke_type: InvokeType.INVOKE_TYPE_API,
    field_class: FieldClass.FIELD_CLASS_STATIC,
  },
  subscribe_control: {
    field_filter_mode: FieldFilterMode.FIELD_FILTER_MODE_NORMAL,
  },
};
