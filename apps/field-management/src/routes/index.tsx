import {
  DatabaseOutlined,
  DeliveredProcedureOutlined,
  FundProjectionScreenOutlined,
} from '@ant-design/icons';
import type { MenuDataItem } from '@ant-design/pro-components';
import { Navigate } from 'react-router-dom';
import { AuthWrapper } from '@/components/auth-wrapper';
import ConsumerFormPage from '@/pages/consumer/form';
import ConsumerDetailPage from '@/pages/consumer/detail';
import ConsumerAppPage from '@/pages/consumer/apps';
import ProducerAppPage from '@/pages/producer/apps';
import ConsumerMyOrderPage from '@/pages/consumer/my-orders';
import ProducerMyOrderPage from '@/pages/producer/my-orders';
import ConsumerMyAppPage from '@/pages/consumer/my-apps';
import ProducerMyAppPage from '@/pages/producer/my-apps';
import ConsumerOrderPage from '@/pages/consumer/orders';
import ProducerOrderPage from '@/pages/producer/orders';
import ProducerFormPage from '@/pages/producer/form';
import ProducerDetailPage from '@/pages/producer/detail';
import ProducerOrderDetailPage from '@/pages/producer/order-detail';
import ConsumerOrderDetailPage from '@/pages/consumer/order-detail';
import ProducerDiffPage from '@/pages/producer/diff';
import ConsumerDiffPage from '@/pages/consumer/diff';
import FieldDiffPage from '@/pages/field/diff';
import FieldOrderDetailPage from '@/pages/field/order-detail';
import FieldPage from '@/pages/field';
import FieldDetailPage from '@/pages/field/detail';
import FieldOrderPage from '@/pages/field/orders';
import FieldMyOrderPage from '@/pages/field/my-orders';
import NoAuthPage from '@/pages/403';
import NotFoundPage from '@/pages/404';
import ErrorPage from '@/pages/500';
import { allAuths } from '@/constants/auth';

export const ROUTES: MenuDataItem[] = [
  {
    path: '/',
    hideInMenu: true,
    element: <Navigate to="/field/static" replace />,
  },
  {
    path: '/field',
    name: '字段管理',
    icon: <DatabaseOutlined />,
    children: [
      {
        path: '/field/static',
        name: '静态字段',
        element: <FieldPage />,
      },
      {
        path: '/field/dynamic',
        name: '动态字段',
        element: <FieldPage />,
      },
      {
        path: '/field/orders',
        name: '工单管理',
        element: (
          <AuthWrapper auth={allAuths.fieldOrderManagement} adminAccess>
            <FieldOrderPage />
          </AuthWrapper>
        ),
        auth: allAuths.fieldOrderManagement,
      },
      {
        path: '/field/my-orders',
        name: '我的工单',
        element: <FieldMyOrderPage />,
      },
      {
        path: '/field/detail/:id',
        name: '字段详情',
        hideInMenu: true,
        element: <FieldDetailPage />,
      },
      {
        path: '/field/order/:id',
        name: '字段工单详情',
        hideInMenu: true,
        element: <FieldOrderDetailPage />,
      },
      {
        path: '/field/diff/order/:id',
        name: '字段工单对比',
        hideInMenu: true,
        element: <FieldDiffPage />,
      },
    ],
  },
  {
    path: '/producer',
    name: '生产方管理',
    icon: <DeliveredProcedureOutlined />,
    children: [
      {
        path: '/producer/apps',
        name: '应用管理',
        element: <ProducerAppPage />,
      },
      {
        path: '/producer/order',
        name: '工单管理',
        element: (
          <AuthWrapper auth={allAuths.producerOrderManagement} adminAccess>
            <ProducerOrderPage />
          </AuthWrapper>
        ),
        auth: allAuths.producerOrderManagement,
      },
      {
        path: '/producer/my-apps',
        name: '我的应用',
        element: <ProducerMyAppPage />,
      },
      {
        path: '/producer/my-orders',
        name: '我的工单',
        element: <ProducerMyOrderPage />,
      },
      {
        path: '/producer/create',
        name: '生产方创建',
        hideInMenu: true,
        element: <ProducerFormPage />,
      },
      {
        path: '/producer/edit/:id',
        name: '生产方编辑',
        hideInMenu: true,
        element: <ProducerFormPage />,
      },
      {
        path: '/producer/detail/:id',
        name: '生产方详情',
        hideInMenu: true,
        element: <ProducerDetailPage />,
      },
      {
        path: '/producer/order/:id',
        name: '生产方工单详情',
        hideInMenu: true,
        element: <ProducerOrderDetailPage />,
      },
      {
        path: '/producer/diff/order/:id',
        name: '生产方工单对比',
        hideInMenu: true,
        element: <ProducerDiffPage />,
      },
    ],
  },
  {
    path: '/consumer',
    name: '消费方管理',
    icon: <FundProjectionScreenOutlined />,
    children: [
      {
        path: '/consumer/apps',
        name: '应用管理',
        element: <ConsumerAppPage />,
      },
      {
        path: '/consumer/order',
        name: '工单管理',
        element: (
          <AuthWrapper auth={allAuths.consumerOrderManagement} adminAccess>
            <ConsumerOrderPage />
          </AuthWrapper>
        ),
        auth: allAuths.consumerOrderManagement,
      },
      {
        path: '/consumer/my-apps',
        name: '我的应用',
        element: <ConsumerMyAppPage />,
      },
      {
        path: '/consumer/my-orders',
        name: '我的工单',
        element: <ConsumerMyOrderPage />,
      },
      {
        path: '/consumer/create',
        name: '消费方创建',
        hideInMenu: true,
        element: <ConsumerFormPage />,
      },
      {
        path: '/consumer/edit/:id',
        name: '消费方编辑',
        hideInMenu: true,
        element: <ConsumerFormPage />,
      },
      {
        path: '/consumer/detail/:id',
        name: '消费方详情',
        hideInMenu: true,
        element: <ConsumerDetailPage />,
      },
      {
        path: '/consumer/order/:id',
        name: '消费方工单详情',
        hideInMenu: true,
        element: <ConsumerOrderDetailPage />,
      },
      {
        path: '/consumer/diff/order/:id',
        name: '消费方工单对比',
        hideInMenu: true,
        element: <ConsumerDiffPage />,
      },
    ],
  },
  {
    path: '/403',
    element: <NoAuthPage />,
    hideInMenu: true,
  },
  {
    path: '/500',
    element: <ErrorPage />,
    hideInMenu: true,
  },
  {
    path: '*',
    element: <NotFoundPage />,
    hideInMenu: true,
  },
];
