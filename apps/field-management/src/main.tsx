import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { RouterProvider, createBrowserRouter } from 'react-router-dom';
import '@ant-design/v5-patch-for-react-19';
import './index.css';
import App from './App';
import { authActions, setUserAtom } from './model/auth';
import { ROUTES } from './routes';

const router = createBrowserRouter([
  {
    path: '/',
    element: <App />,
    children: ROUTES,
  },
], {
  basename: '/',
});

// 先渲染应用，避免白屏
createRoot(document.getElementById('root')!).render(<StrictMode>
  <RouterProvider router={router} />
</StrictMode>);

// 创建超时Promise
const createTimeoutPromise = (timeout: number) => new Promise((_, reject) => {
  setTimeout(() => {
    reject(new Error(`权限获取超时 (${timeout}ms)`));
  }, timeout);
});

// 异步获取权限信息，添加超时机制
const authPromises = [
  authActions.setAuths(),
  authActions.setUserName(),
  authActions.setRoleList(),
];

Promise.race([
  Promise.all(authPromises),
  createTimeoutPromise(10000), // 10秒超时
]).catch((error) => {
  console.error('权限获取失败:', error);
  // 即使权限获取失败，也要设置 authLoading 为 false，避免无限加载
  setUserAtom((draft) => {
    draft.authLoading = false;
    // 设置默认权限，确保应用可以正常使用
    if (draft.auths.length === 0) {
      console.warn('使用默认权限配置');
      // 可以根据需要设置一些基础权限
    }
  });
});
