import omit from 'lodash/omit';
import cloneDeep from 'lodash/cloneDeep';
import { Consumer, ConsumerUpdateData, TransformFieldSet, Trigger, FieldClass, InvokeType } from '@/types/api';
import { FormatComsumer, FormatTransformFieldSet, FormatDetailConsumer } from '@/types/consumer';

/**
 * 获取格式化后的消费方表单值
 * @param values 表单值
 * @returns 格式化后的表单值
 */
export const getFormatFormValue = (values: Consumer): FormatComsumer => {
  const cloneValues = cloneDeep(values);
  if (cloneValues?.subscribe_fields?.field_sets) {
    const fieldSets: FormatTransformFieldSet[] = cloneValues.subscribe_fields.field_sets.map((item) => {
      const fieldsInfo = {
        fields: item.fields,
        dependent_fields: item.dependent_fields || [],
      };
      if (!cloneValues?.subscribe_control?.triggers) {
        return {
          ...omit(item, ['fields', 'dependent_fields']),
          fieldsInfo,
        };
      }
      const trigger = cloneValues.subscribe_control.triggers.find((trigger) => {
        const { field_class: triggerFieldClass, entity_type: triggerEntityType } = trigger;
        return triggerFieldClass === item.field_class && triggerEntityType === item.entity_type;
      });
      return {
        ...omit(item, ['fields', 'dependent_fields']),
        trigger_type: trigger?.trigger_type,
        expressions: trigger?.expressions,
        fieldsInfo,
      };
    });
    return {
      ...omit(cloneValues, ['subscribe_fields', 'subscribe_service']),
      subscribe_service: {
        ...cloneValues.subscribe_service,
        api: {
          caller: cloneValues.subscribe_service?.prod_api?.caller,
          rate_limit: cloneValues.subscribe_service?.prod_api?.rate_limit,
        },
      },
      subscribe_fields: {
        ...omit(cloneValues.subscribe_fields, ['field_sets']),
        field_sets: fieldSets,
      },
    };
  }
  return {
    ...omit(cloneValues, ['subscribe_fields', 'subscribe_service']),
    subscribe_service: {
      ...cloneValues.subscribe_service,
      api: {
        caller: cloneValues.subscribe_service?.prod_api?.caller,
        rate_limit: cloneValues.subscribe_service?.prod_api?.rate_limit,
      },
    },
    subscribe_fields: {
      ...omit(cloneValues?.subscribe_fields, ['field_sets']),
      field_sets: [],
    },
  };
};

/**
 * 获取接口请求参数
 * @param values 表单值
 * @returns 接口请求参数
 */
export const getFormatRequestValue = (values: FormatComsumer, fieldClass?: FieldClass): ConsumerUpdateData => {
  const cloneValues = cloneDeep(values);
  let newJoins = cloneDeep(cloneValues?.subscribe_fields?.joins);
  if (values?.subscribe_fields?.joins) {
    newJoins = cloneValues?.subscribe_fields?.joins?.map((item) => {
      if (item.fields) {
        return {
          ...item,
          fields: {
            ...item.fields,
            field_class: fieldClass,
          },
        };
      }
      return item;
    });
  }
  if (cloneValues?.subscribe_fields?.field_sets) {
    const fieldSets: TransformFieldSet[] = cloneValues.subscribe_fields.field_sets.map(item => ({
      ...omit(item, ['fieldsInfo', 'trigger_type', 'expressions']),
      ...(item.fieldsInfo || {}),
      // api携带，kafka不携带，需要从参数获取
      field_class: item.field_class || fieldClass,
    }));
    const newSubscribeFields = {
      ...omit(cloneValues.subscribe_fields, ['field_sets', 'joins']),
      field_sets: fieldSets,
      joins: newJoins,
    };
    if (cloneValues?.subscribe_control) {
      const triggers: Trigger[] = cloneValues.subscribe_fields.field_sets.map(item => ({
        field_class: fieldClass,
        entity_type: item.entity_type,
        trigger_type: item.trigger_type,
        expressions: item.expressions,
      })).filter(item => item.trigger_type || item.expressions);
      return {
        ...omit(cloneValues, ['subscribe_fields', 'subscribe_control']),
        subscribe_fields: newSubscribeFields,
        subscribe_control: {
          ...cloneValues.subscribe_control,
          triggers,
        },
      };
    }
    return {
      ...omit(cloneValues, ['subscribe_fields']),
      subscribe_fields: newSubscribeFields,
    };
  }
  if (cloneValues?.subscribe_service?.invoke_type === InvokeType.INVOKE_TYPE_KAFKA) {
    return {
      ...omit(cloneValues, ['subscribe_fields']),
      subscribe_fields: {
        ...omit(cloneValues?.subscribe_fields, ['field_sets', 'joins']),
        field_sets: [],
        joins: newJoins,
      },
    };
  }
  return cloneValues;
};

/**
 * 获取格式化后的消费方详情值
 * @param values 消费方详情值
 * @returns 格式化后的消费方详情值
 */
export const getFormatDetailValue = (values?: Consumer): FormatDetailConsumer | undefined => {
  if (!values) {
    return undefined;
  }
  const cloneValues = cloneDeep(values);
  if (cloneValues?.subscribe_fields?.field_sets) {
    const fieldSets: FormatTransformFieldSet[] = cloneValues.subscribe_fields.field_sets.map((item) => {
      if (!cloneValues?.subscribe_control?.triggers) {
        return item;
      }
      const trigger = cloneValues.subscribe_control.triggers.find((trigger) => {
        const { field_class: triggerFieldClass, entity_type: triggerEntityType } = trigger;
        return triggerFieldClass === item.field_class && triggerEntityType === item.entity_type;
      });
      return {
        ...item,
        trigger_type: trigger?.trigger_type,
        expressions: trigger?.expressions,
      };
    });
    return {
      ...omit(cloneValues, ['subscribe_fields']),
      subscribe_fields: {
        ...omit(cloneValues.subscribe_fields, ['field_sets']),
        field_sets: fieldSets,
      },
    };
  }
  return {
    ...omit(cloneValues, ['subscribe_fields']),
    subscribe_fields: {
      ...omit(cloneValues.subscribe_fields, ['field_sets']),
      field_sets: [],
    },
  };
};
