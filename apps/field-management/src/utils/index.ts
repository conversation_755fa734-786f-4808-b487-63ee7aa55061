// 所有的方法
import type { FieldDetail, FieldSet } from '@/types/api';
import type { OptionItem, ProducerFields } from '@/types/common';

/**
 * 根据字符串获取字段
 * @param fields 字符串
 * @param data 字段数据
 * @returns 字段数据
 */
export const getFieldsByString = (fields: string[], data: FieldDetail[], isFilter = true) => fields.map((field) => {
  const fieldData = data.find(item => item.field_path === field);
  if (isFilter) {
    return fieldData as FieldDetail;
  }
  const formatField: FieldDetail = fieldData || {
    field_path: field,
  };
  return formatField;
}).filter(item => Boolean(item));

/**
 * 根据选项列表获取映射表
 * @param options 选项列表
 * @returns 映射表
 */
export function getMapByOptions<T = Record<string, string>>(options: OptionItem[]) {
  const defaultMap: Record<string, string> = {};
  return options.reduce((acc, item) => {
    acc[item.value] = item.label;
    return acc;
  }, defaultMap) as T;
}

/**
 * 安全解析JSON字符串
 * @param jsonString JSON字符串
 * @param defaultValue 解析失败时返回的默认值
 * @returns 解析结果或默认值
 */
export function safeJsonParse<T>(jsonString: string | null | undefined, defaultValue: T): T {
  if (!jsonString) return defaultValue;
  try {
    return JSON.parse(jsonString) as T;
  } catch (error) {
    console.error('JSON解析失败', error);
    return defaultValue;
  }
}

/**
 * 解析生产字段JSON为字段集合
 * @param produceFields 生产字段JSON字符串
 * @returns 字段集合数组
 */
export function parseProducerFields(produceFields?: string): FieldSet[] {
  if (!produceFields) return [];
  const fields = safeJsonParse<ProducerFields>(produceFields, {});
  const fieldSets = Object.entries(fields).map(([entityType, fieldNames]) => ({
    entity_type: entityType,
    fields: fieldNames,
  }));
  return fieldSets.sort((a, b) => a.entity_type.localeCompare(b.entity_type));
}
