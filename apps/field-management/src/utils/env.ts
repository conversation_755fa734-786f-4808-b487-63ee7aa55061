export const getEnv = () => {
  if (process.env.NODE_ENV === 'development') {
    return 'dev';
  }
  const { host } = location;
  if (host.startsWith('newscontent-diff')) {
    return 'diff';
  }
  if (host.startsWith('newscontent.testsite')) {
    return 'test';
  }
  return 'prod';
};

export const isProd = () => getEnv() === 'prod';

export const isTest = () => getEnv() === 'test';

export const isDiff = () => getEnv() === 'diff';

export const isDev = () => getEnv() === 'dev';

export const getEnvText = () => {
  const env = getEnv();
  switch (env) {
    case 'prod':
      return '生产环境';
    case 'test':
      return '测试环境';
    case 'diff':
      return 'Diff环境';
    case 'dev':
      return '开发环境';
    default:
      return '生产环境';
  }
};
