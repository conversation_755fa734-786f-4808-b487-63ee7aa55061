import { useState, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import { useRequest, useMemoizedFn } from 'ahooks';
import { Button, Modal, Typography } from 'antd';
import { PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { fetchFieldOrderDetail } from '@/services/field';
import FieldDetail from '@/components/field-detail';
import OrderInfo from '@/components/order-detail';
import { SELECT_FIELD_COLUMNS } from '@/constants/field';
import { OrderType, FieldDetail as FieldDetailType } from '@/types/api';

export default function FieldOrderDetailPage() {
  const { id } = useParams();
  const [open, setOpen] = useState(false);
  const [curField, setCurField] = useState<FieldDetailType>();

  const { data, loading } = useRequest(fetchFieldOrderDetail, {
    defaultParams: [{ order_id: Number(id) }],
  });

  const { order, field, fields } = data || {};
  const { type } = order || {};

  const handleDetail = useMemoizedFn((record: FieldDetailType) => {
    setCurField(record);
    setOpen(true);
  });

  const isOffline = type === OrderType.ORDER_TYPE_DYNAMIC_FIELD_OFFLINE || type === OrderType.ORDER_TYPE_STATIC_FIELD_OFFLINE;

  const optionColumn: ProColumns<FieldDetailType> = useMemo(() => ({
    title: '操作',
    dataIndex: 'action',
    width: 100,
    fixed: 'right',
    render: (_, record) => <Button type='link' onClick={() => handleDetail(record)}>详情</Button>,
  }), [handleDetail]);

  const fieldColumns: ProColumns<FieldDetailType>[] = useMemo(() => [
    ...SELECT_FIELD_COLUMNS.map((item) => {
      if (item.dataIndex === 'entity_type' || item.dataIndex === 'column_family') {
        return {
          ...item,
          params: {
            field_class: field?.field_class,
          },
        };
      }
      if (item.dataIndex === 'status') {
        return {
          ...item,
          hideInTable: true,
        };
      }
      return item;
    }),
    optionColumn,
  ], [field?.field_class, optionColumn]);

  return (
    <PageContainer title={false} loading={loading}>
      <OrderInfo order={order} />
      {isOffline ? (
        <>
          <Typography.Title level={5} style={{ marginTop: 16 }}>
            下线字段
          </Typography.Title>
          <ProTable
            dataSource={fields}
            columns={fieldColumns}
            search={false}
            options={false}
            scroll={{ x: 'max-content', y: 300 }}
            pagination={{
              pageSize: 20,
            }}
          />
          <Modal
            open={open}
            onCancel={() => setOpen(false)}
            width={800}
            footer={false}
          >
            <FieldDetail field={curField} span={24} isOrder />
          </Modal>
        </>
      ) : (
        <FieldDetail field={field} isOrder style={{ marginTop: 16 }} />
      )}
    </PageContainer>
  );
}
