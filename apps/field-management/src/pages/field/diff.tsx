import { useParams } from 'react-router-dom';
import { useRequest, useMemoizedFn } from 'ahooks';
import { Row, Col, Typography } from 'antd';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { fetchFieldOrderDetail, fetchFieldDetail } from '@/services/field';
import FieldDetail from '@/components/field-detail';
import OrderInfo from '@/components/order-detail';

export default function FieldOrderDiffPage() {
  const { id } = useParams();

  const getDiffField = useMemoizedFn(async () => {
    const orderRes = await fetchFieldOrderDetail({ order_id: Number(id) });
    const { field: newField, order } = orderRes || {};
    const oldFieldRes = await fetchFieldDetail({
      field_path: newField?.field_path,
      field_class: newField?.field_class,
      entity_type: newField?.entity_type,
    });
    const { field: oldField } = oldFieldRes || {};
    return { newField, oldField, order };
  });

  const { data, loading } = useRequest(getDiffField, {
    refreshDeps: [id],
  });

  const { newField, oldField, order } = data || {};

  return (
    <PageContainer title={false} loading={loading}>
      <OrderInfo order={order} />
      <Typography.Title level={5} style={{ marginTop: 16 }}>字段对比</Typography.Title>
      <Row style={{ marginTop: 16 }} gutter={16}>
        <Col span={12}>
          <ProCard title="旧字段" bordered headerBordered direction='column'>
            <FieldDetail field={oldField} isOrder style={{ marginTop: 16 }} span={3} />
          </ProCard>
        </Col>
        <Col span={12}>
          <ProCard title="新字段" bordered headerBordered direction='column'>
            <FieldDetail field={newField} isOrder style={{ marginTop: 16 }} span={3} />
          </ProCard>
        </Col>
      </Row>
    </PageContainer>
  );
}
