import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import { useRequest, useMemoizedFn } from 'ahooks';
import { Button, Typography } from 'antd';
import { PageContainer, ProTable, ProColumns } from '@ant-design/pro-components';
import { fetchFieldDetail } from '@/services/field';
import { APP_COLUMNS } from '@/constants/apps';
import type { AppSummary, FieldClass } from '@/types/api';
import FieldDetail from '@/components/field-detail';

export default function FieldDetailPage() {
  const navigate = useNavigate();
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const { data, loading } = useRequest(fetchFieldDetail, {
    defaultParams: [{
      field_path: id,
      field_class: Number(searchParams.get('field_class')) as FieldClass,
      entity_type: searchParams.get('entity_type') as string,
    }],
  });

  const { field, producers = [], consumers = [] } = data || {};

  const handleConsumerDetail = useMemoizedFn((record: AppSummary) => {
    navigate(`/consumer/detail/${record.app_name}`);
  });
  const consumerColumns: ProColumns<AppSummary>[] = [
    ...APP_COLUMNS,
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Button type="link" onClick={() => handleConsumerDetail(record)}>详情</Button>
      ),
    },
  ];
  const handleProducerDetail = useMemoizedFn((record: AppSummary) => {
    navigate(`/producer/detail/${record.app_name}`);
  });
  const producerColumns: ProColumns<AppSummary>[] = [
    ...APP_COLUMNS,
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Button type="link" onClick={() => handleProducerDetail(record)}>详情</Button>
      ),
    },
  ];
  return (
    <PageContainer title={false} loading={loading}>
      <FieldDetail field={field} />
      <Typography.Title level={5} style={{ marginTop: 16 }}>关联生产方</Typography.Title>
      <ProTable<AppSummary>
        columns={producerColumns}
        dataSource={producers}
        rowKey="app_id"
        search={false}
        pagination={false}
        toolBarRender={false}
        options={false}
        cardBordered
        size="small"
        scroll={{ y: 300, x: 'max-content' }}
      />
      <Typography.Title level={5} style={{ marginTop: 16 }}>关联消费方</Typography.Title>
      <ProTable<AppSummary>
        columns={consumerColumns}
        dataSource={consumers}
        rowKey="app_id"
        search={false}
        pagination={{
          pageSize: 10,
        }}
        toolBarRender={false}
        options={false}
        cardBordered
        size="small"
        scroll={{ y: 300, x: 'max-content' }}
      />
    </PageContainer>
  );
}
