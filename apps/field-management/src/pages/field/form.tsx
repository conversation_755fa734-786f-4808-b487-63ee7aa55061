import { useEffect, useRef, useMemo } from 'react';
import omit from 'lodash/omit';
import { App } from 'antd';
import { ModalForm, ProFormInstance, ProFormSelect, ProFormText, ProFormTextArea, ProFormDependency, ProFormItem } from '@ant-design/pro-components';
import { useAtom } from 'helux';
import { useMemoizedFn } from 'ahooks';
import { CodeEditor } from '@packages/components';
import { safeJsonParse } from '@packages/utils';
import { useAccess } from '@/hooks/useAccess';
import ValidationRules from '@/components/validation-rules';
import { VALUE_TYPE_OPTIONS, FIELD_CLASS_OPTIONS, COLUMN_FAMILY_TOOLTIP, BLOCK_RULE_OPTIONS } from '@/constants/field';
import { httpUrlValidationRule, fieldExampleValidationRule, noSpaceValidationRule } from '@/constants/rules';
import { updateField, fetchEntityTypes, fetchColumnFamilies } from '@/services/field';
import { getUsers } from '@/services/user';
import { fieldAtom, fieldActions } from '@/model/field';
import { UpdateFieldRequest } from '@/types/api';

export default function FieldForm() {
  const [fieldState] = useAtom(fieldAtom);
  const formRef = useRef<ProFormInstance<UpdateFieldRequest>>(null);
  const { message } = App.useApp();
  const { userInfo } = useAccess();

  const handleFinish = useMemoizedFn(async (values: UpdateFieldRequest) => {
    if (values.fields?.[0]?.validation) {
      values.fields[0].validation = JSON.stringify(values.fields[0].validation);
    }
    const res = await updateField(values);
    if (res.success) {
      message.success('已提交更新工单');
      fieldActions.setFormOpen(false);
      fieldActions.setCurrentField(null);
    } else {
      message.error(res.message);
    }
  });

  const initialValues = useMemo(() => ({
    field_class: fieldState.currentField?.field_class,
    fields: [{
      ...omit(fieldState.currentField, ['field_class', 'status', 'validation']),
      src_field_path: fieldState.currentField?.src_field_path || fieldState.currentField?.field_path,
      // @ts-ignore
      validation: safeJsonParse<Record<string, any>>(fieldState.currentField?.validation, {}),
    }],
    order: {
      applicant: userInfo.name,
    },
  }), [fieldState.currentField, userInfo.name]);

  const handleInit = useMemoizedFn((_: UpdateFieldRequest, form: ProFormInstance<UpdateFieldRequest>) => {
    // @ts-ignore
    form.setFieldsValue(initialValues);
  });

  useEffect(() => {
    if (fieldState.formOpen && formRef.current) {
      // @ts-ignore
      formRef.current?.setFieldsValue(initialValues);
    }
  }, [fieldState.formOpen, initialValues, formRef.current]);

  return (
    <ModalForm<UpdateFieldRequest>
      formRef={formRef}
      onFinish={handleFinish}
      layout="horizontal"
      title="字段编辑"
      onInit={handleInit}
      initialValues={initialValues}
      modalProps={{
        width: 800,
        styles: {
          body: {
            maxHeight: '70vh',
            overflow: 'auto',
          },
        },
      }}
      open={fieldState.formOpen}
      onOpenChange={fieldActions.setFormOpen}
    >
      <ProFormSelect
        name="field_class"
        label="字段分类"
        disabled
        options={FIELD_CLASS_OPTIONS}
        rules={[{ required: true, message: '请选择字段分类' }]}
      />
      <ProFormSelect
        name={['fields', 0, 'entity_type']}
        label="介质类型"
        request={fetchEntityTypes}
        showSearch
        disabled
        params={{
          field_class: fieldState.currentField?.field_class,
        }}
        rules={[{ required: true, message: '请选择介质类型' }]}
      />
      <ProFormSelect
        name={['fields', 0, 'column_family']}
        label="列簇名称"
        tooltip={COLUMN_FAMILY_TOOLTIP}
        request={fetchColumnFamilies}
        showSearch
        params={{
          field_class: fieldState.currentField?.field_class,
          entity_type: fieldState.currentField?.entity_type,
        }}
        rules={[{ required: true, message: '请选择列簇名称' }]}
      />
      <ProFormText
        name={['fields', 0, 'src_field_path']}
        label="源字段路径"
        rules={[
          { required: true, message: '请输入源字段路径' },
          noSpaceValidationRule,
        ]}
      />
      <ProFormText
        name={['fields', 0, 'field_path']}
        label="字段路径"
        disabled
        rules={[
          { required: true, message: '请输入字段路径' },
          noSpaceValidationRule,
        ]}
      />
      <ProFormText
        name={['fields', 0, 'field_name']}
        label="字段名称"
        rules={[
          { required: true, message: '请输入字段名称' },
          noSpaceValidationRule,
        ]}
      />
      <ProFormSelect
        name={['fields', 0, 'value_type']}
        label="数据类型"
        disabled
        options={VALUE_TYPE_OPTIONS}
        rules={[{ required: true, message: '请选择数据类型' }]}
      />
      <ProFormText
        name={['fields', 0, 'description']}
        label="字段描述"
        rules={[
          { required: true, message: '请输入字段描述' },
        ]}
      />
      <ProFormDependency name={['fields', 0, 'value_type']}>
        {({ fields }) => (
          <ProFormItem name={['fields', 0, 'validation']} label="字段校验规则">
            <ValidationRules valueType={fields?.[0]?.value_type} />
          </ProFormItem>
        )}
      </ProFormDependency>
      <ProFormItem
        name={['fields', 0, 'example']}
        label="字段示例"
        tooltip='支持JSON格式，如：{"name": "张三", "age": 18}'
        rules={[
          { required: true, message: '请输入字段示例' },
          fieldExampleValidationRule,
        ]}
      >
        <CodeEditor language="json" />
      </ProFormItem>
      <ProFormTextArea
        name={['fields', 0, 'produce_logic']}
        label="生产逻辑"
      />
      <ProFormText
        name={['fields', 0, 'tapd']}
        label="字段TAPD地址"
        rules={[
          { required: true, message: '请输入TAPD地址' },
          httpUrlValidationRule,
        ]}
      />
      <ProFormSelect
        name={['fields', 0, 'owner']}
        label="负责人"
        request={getUsers}
        fieldProps={{
          showSearch: true,
        }}
        rules={[{ required: true, message: '请选择负责人' }]}
      />
      <ProFormSelect
        name={['fields', 0, 'block_rule']}
        label="拦截规则"
        options={BLOCK_RULE_OPTIONS}
      />
      <ProFormText
        name={['order', 'applicant']}
        label="申请人"
        disabled
      />
      <ProFormText
        name={['order', 'tapd']}
        label="工单TAPD地址"
        rules={[
          { required: true, message: '请输入TAPD地址' },
          httpUrlValidationRule,
        ]}
      />
      <ProFormTextArea
        name={['order', 'description']}
        label="工单描述"
        rules={[
          { required: true, message: '请输入工单描述' },
        ]}
      />
    </ModalForm>
  );
}
