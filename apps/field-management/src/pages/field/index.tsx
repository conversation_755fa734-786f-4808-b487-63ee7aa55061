import { useMemo } from 'react';
import { Button, Space } from 'antd';
import { useMemoizedFn } from 'ahooks';
import { useLocation, useNavigate } from 'react-router-dom';
import { PageContainer, ProTable, ProColumns } from '@ant-design/pro-components';
import { fetchAllFields } from '@/services/field';
import { FIELD_COLUMNS } from '@/constants/field';
import OfflineModal from '@/components/offline-modal';
import { FieldDetail, FieldClass, FieldStatus } from '@/types/api';
import { fieldActions } from '@/model/field';
import { orderActions } from '@/model/order';
import { AuthButton } from '@/components/auth-button';
import EditForm from './form';

export default function FieldPage() {
  const location = useLocation();
  const navigate = useNavigate();
  const isStatic = location.pathname.includes('static');
  const formInitialValues = useMemo(() => ({
    field_class: isStatic ? FieldClass.FIELD_CLASS_STATIC : FieldClass.FIELD_CLASS_DYNAMIC,
  }), [isStatic]);
  const handleEdit = useMemoizedFn((record: FieldDetail) => {
    fieldActions.setCurrentField(record);
    fieldActions.setFormOpen(true);
  });
  const handleDetail = useMemoizedFn((record: FieldDetail) => {
    const { field_class, entity_type } = record;
    navigate(`/field/detail/${record.field_path}?field_class=${field_class}&entity_type=${entity_type}`);
  });
  const handleOffline = useMemoizedFn((record: FieldDetail) => {
    orderActions.setOfflineTitle('字段下线');
    orderActions.setCurrentField(record);
    orderActions.setOfflineOpen(true);
  });
  const columns: ProColumns<FieldDetail>[] = useMemo(() => [
    ...FIELD_COLUMNS.map((column) => {
      if (column.dataIndex === 'entity_type') {
        return {
          ...column,
          params: {
            field_class: isStatic ? FieldClass.FIELD_CLASS_STATIC : FieldClass.FIELD_CLASS_DYNAMIC,
          },
        };
      }
      if (column.dataIndex === 'column_family') {
        return {
          ...column,
          params: {
            field_class: isStatic ? FieldClass.FIELD_CLASS_STATIC : FieldClass.FIELD_CLASS_DYNAMIC,
          },
        };
      }
      return column;
    }),
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 150,
      render: (_, record) => (
        <Space>
          <AuthButton
            authUsers={record.owner}
            adminAccess
            style={{ margin: 0, padding: 0 }}
            type="link"
            disabled={record.status === FieldStatus.FIELD_STATUS_OFFLINE}
            onClick={() => handleEdit(record)}
          >
            编辑
          </AuthButton>
          <Button
            style={{ margin: 0, padding: 0 }}
            type="link"
            onClick={() => handleDetail(record)}
          >
            详情
          </Button>
          <AuthButton
            authUsers={record.owner}
            adminAccess
            style={{ margin: 0, padding: 0 }}
            disabled={record.status === FieldStatus.FIELD_STATUS_OFFLINE}
            type="link"
            onClick={() => handleOffline(record)}
          >
            下线
          </AuthButton>
        </Space>
      ),
    },
  ], [isStatic]);
  return (
    <PageContainer
      title={false}
    >
      <ProTable<FieldDetail>
        request={fetchAllFields}
        params={{
          field_class: isStatic ? FieldClass.FIELD_CLASS_STATIC : FieldClass.FIELD_CLASS_DYNAMIC,
        }}
        form={{
          initialValues: formInitialValues,
        }}
        search={{
          defaultCollapsed: false,
        }}
        columns={columns}
        rowKey={record => `${record.field_path}-${record.entity_type}}`}
        pagination={{
          showSizeChanger: true,
        }}
        scroll={{ x: 'max-content', y: 800 }}
      />
      <EditForm />
      <OfflineModal mode='field' />
    </PageContainer>
  );
}
