import { useMemo } from 'react';
import { Button, Space } from 'antd';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-components';
import { fetchFieldOrders } from '@/services/field';
import AllocResourceModal from '@/components/alloc-resource';
import type { OrderDetail } from '@/types/api';
import { useOrderList } from '@/hooks/useOrderList';
import { FIELD_ORDER_TYPE_OPTIONS } from '@/constants/order';

export default function OrderPage() {
  const { handleFlowDetail, handleOrderDetial, columns } = useOrderList('field');

  const orderColumns = useMemo<ProColumns<OrderDetail>[]>(() => [
    ...columns.map((column) => {
      if (column.dataIndex === 'type') {
        return {
          ...column,
          fieldProps: {
            options: FIELD_ORDER_TYPE_OPTIONS,
          },
        };
      }
      return column;
    }),
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      width: 140,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Button
            style={{ margin: 0, padding: 0 }}
            type="link"
            onClick={() => handleFlowDetail(record)}
          >
            审批进度
          </Button>
          <Button
            style={{ margin: 0, padding: 0 }}
            type="link"
            onClick={() => handleOrderDetial(record)}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ], [columns, handleFlowDetail, handleOrderDetial]);

  return (
    <PageContainer
      title={false}
    >
      <ProTable<OrderDetail>
        columns={orderColumns}
        request={fetchFieldOrders}
        rowKey="order_id"
        search={{
          defaultCollapsed: false,
        }}
        pagination={{
          showSizeChanger: true,
        }}
        scroll={{
          y: 800,
          x: 'max-content',
        }}
      />
      <AllocResourceModal mode="producer" />
    </PageContainer>
  );
}
