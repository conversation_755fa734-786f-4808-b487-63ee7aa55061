import { useMemoizedFn } from 'ahooks';
import { fetchEntityTypes } from '@/services/field';
import { FieldClass, TransformFieldSet } from '@/types/api';
import KafkaStaticForm from './form-kafka-static';
import KafkaDynamicForm from './form-kafka-dynamic';
import { KafkaFormProps } from './consts';
import './form.css';

export default function KafkaForm(props: KafkaFormProps) {
  const { subFieldClass, formRef } = props;

  const getEntityTypeOptions = useMemoizedFn(async (params: { field_class: FieldClass, keyWords?: string }) => {
    const fieldSets = formRef.current?.getFieldValue([
      'consumer',
      'subscribe_fields',
      'field_sets',
    ]);
    const res = await fetchEntityTypes({
      field_class: params.field_class,
      keyWords: params.keyWords,
    });
    return res?.filter(item => !fieldSets?.some((fieldSet: TransformFieldSet) => fieldSet.entity_type === item.value));
  });

  return (
    <>
      {subFieldClass === FieldClass.FIELD_CLASS_STATIC ? (
        <KafkaStaticForm {...props} getEntityTypeOptions={getEntityTypeOptions} />
      ) : (
        <KafkaDynamicForm {...props} getEntityTypeOptions={getEntityTypeOptions} />
      )}
    </>
  );
}
