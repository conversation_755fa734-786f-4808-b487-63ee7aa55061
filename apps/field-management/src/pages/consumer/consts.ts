import { RefObject } from 'react';
import type { ProFormInstance } from '@ant-design/pro-components';
import { FieldClass } from '@/types/api';
import { OptionItem } from '@/types/common';
import { FormatConsumerRequest } from '@/types/consumer';

export const proCardStyle = {
  marginBlockEnd: 16,
  width: '100%',
};

export interface KafkaFormProps {
  subFieldClass: FieldClass;
  formRef: RefObject<ProFormInstance<FormatConsumerRequest> | undefined>;
  isRestoringFromDraft: boolean;
  isEdit: boolean;
  getEntityTypeOptions?: (params: { field_class: FieldClass, keyWords?: string }) => Promise<OptionItem[]>;
}

export const fieldSetValidator = (value: any, subFieldClass: FieldClass) => {
  // 按字段类型分组
  const groupByFieldClass: Record<string, string[]> = value.reduce((acc: Record<string, string[]>, item: any) => {
    if (!acc[subFieldClass]) {
      acc[subFieldClass] = [];
    }
    acc[subFieldClass].push(item.entity_type);
    return acc;
  }, {});
  // 检查每个字段类型下的介质类型是否有重复
  for (const [fieldClass, entityTypes] of Object.entries(groupByFieldClass)) {
    const uniqueEntityTypes = new Set<string>(entityTypes);
    if (uniqueEntityTypes.size < entityTypes.length) {
      return Promise.reject(new Error(`字段类型 ${fieldClass} 下存在重复的介质类型`));
    }
  }
  return Promise.resolve();
};
