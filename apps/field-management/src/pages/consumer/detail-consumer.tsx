import dayjs from 'dayjs';
import { Empty } from 'antd';
import { ProDescriptions, ProCard, ProTable } from '@ant-design/pro-components';
import {
  EXPRESSION_ACTION_MAP,
  DATA_SOURCE_MAP,
  SUBSCRIBE_TYPE_MAP,
  FIELD_FILTER_MODE_MAP,
  SUBSCRIBE_SERVICE_TYPE_MAP,
  PROTOCOL_VERSION_MAP,
  DATA_SERIALIZATION_TYPE_MAP,
  DATA_COMPRESSION_TYPE_MAP,
} from '@/constants/consumer';
import { APP_STATUS_MAP } from '@/constants/apps';
import { useAccess } from '@/hooks/useAccess';
import { FIELD_CLASS_MAP } from '@/constants/field';
import { FILTER_TYPE_MAP, SWITCH_PROPS } from '@/constants/common';
import FieldView from '@/components/field-select/view';
import { InvokeType, Consumer, AppSummary, FieldClass } from '@/types/api';
import { fetchEntityTypes } from '@/services/field';
import { getFormatDetailValue } from '@/utils/consumer';
import KafkaDetail from './detail-kafka';
import ApiDetail from './detail-api';

interface ConsumerDetailProps {
  application?: AppSummary;
  consumer?: Consumer;
  isOrder?: boolean;
  marginTop?: number;
}

export default function ConsumerDetail({ consumer, application, marginTop = 0 }: ConsumerDetailProps) {
  const formatConsumer = getFormatDetailValue(consumer);
  const isKafka = formatConsumer?.subscribe_service?.invoke_type === InvokeType.INVOKE_TYPE_KAFKA;
  const { isAdmin, userInfo } = useAccess();

  const isOwner = userInfo.name === application?.owner;
  const isAdminRole = isAdmin();
  const isAdminOrOwner = isAdminRole || isOwner;

  const isStatic = formatConsumer?.subscribe_service?.field_class === FieldClass.FIELD_CLASS_STATIC;

  const expressionColumns = [
    {
      title: '表达式名称',
      dataIndex: 'name',
      minWidth: 100,
    },
    {
      title: '表达式动作',
      dataIndex: 'action',
      valueEnum: EXPRESSION_ACTION_MAP,
      minWidth: 200,
    },
    {
      title: '表达式条件',
      dataIndex: 'condition',
      minWidth: 300,
      ellipsis: true,
    },
    {
      title: '事件类型',
      dataIndex: 'event_type',
      minWidth: 100,
    },
    {
      title: '事件ID',
      dataIndex: 'event_id',
      minWidth: 100,
    },
  ];

  const fieldMonitorColumns = [
    {
      title: '监控名称',
      dataIndex: 'name',
      minWidth: 100,
      ellipsis: true,
    },
    {
      title: '介质类型',
      dataIndex: 'entity_types',
      minWidth: 100,
      ellipsis: true,
    },
    {
      title: '配置',
      dataIndex: 'config',
      renderText: (text: any) => {
        if (!text) {
          return '--';
        }
        return <pre>{JSON.stringify(text, null, 2)}</pre>;
      },
    },
  ];

  return (
    <>
      <ProDescriptions
        title="消费方基本信息"
        bordered
        column={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 2, xxl: 2 }}
        style={{ marginTop }}
        labelStyle={{ width: 200 }}
      >
        <ProDescriptions.Item label="应用 ID">
          {application?.app_id}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="应用名称">
          {application?.app_name}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="应用中文名">
          {application?.app_name_ch}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="应用状态" valueType="select" valueEnum={APP_STATUS_MAP}>
          {application?.status}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="负责人" span={3}>
          {application?.owner}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="描述" span={3}>
          {application?.description}
        </ProDescriptions.Item>
      </ProDescriptions>
      <ProDescriptions
        title="订阅服务配置"
        bordered
        style={{ marginTop: 16 }}
        column={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 2, xxl: 2 }}
        labelStyle={{ width: 200 }}
      >
        <ProDescriptions.Item label="对接方式" valueEnum={SUBSCRIBE_SERVICE_TYPE_MAP} valueType='select'>
          {formatConsumer?.subscribe_service?.invoke_type}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="字段分类" valueEnum={FIELD_CLASS_MAP} valueType='select'>
          {formatConsumer?.subscribe_service?.field_class}
        </ProDescriptions.Item>
        {/** API 和 Kafka 静态数据 有字段过滤模式 */}
        {(!isKafka || (isKafka && isStatic)) && (
          <ProDescriptions.Item
            title="字段过滤模式"
            valueEnum={FIELD_FILTER_MODE_MAP}
            valueType='select'
          >
            {formatConsumer?.subscribe_control?.field_filter_mode}
          </ProDescriptions.Item>
        )}
      </ProDescriptions>
      {isKafka ? (
        <>
          <KafkaDetail
            data={formatConsumer?.subscribe_service?.test_kafka}
            hasAuth={isAdminOrOwner}
            isAdmin={isAdminRole}
            title="测试环境KAFKA配置"
          />
          <KafkaDetail
            data={formatConsumer?.subscribe_service?.prod_kafka}
            hasAuth={isAdminOrOwner}
            title="正式环境KAFKA配置"
            isAdmin={isAdminRole}
          />
        </>
      ) : (
        <>
          <ApiDetail
            data={formatConsumer?.subscribe_service?.test_api}
            hasAuth={isAdminOrOwner}
            title="测试环境API配置"
          />
          <ApiDetail
            data={formatConsumer?.subscribe_service?.prod_api}
            hasAuth={isAdminOrOwner}
            title="正式环境API配置"
          />
        </>
      )}
      <ProCard title="订阅字段" bordered headerBordered wrap style={{ marginTop: 16 }}>
        {formatConsumer?.subscribe_fields?.field_sets?.map(item => (
          <ProCard
            title={item.entity_type}
            key={item.entity_type}
            colSpan={24}
            style={{ marginTop: 16 }}
            headerBordered
            boxShadow
          >
            <ProDescriptions bordered>
              <ProDescriptions.Item title="字段分类" valueEnum={FIELD_CLASS_MAP} valueType='select'>
                {item?.field_class}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                title="介质类型"
                request={fetchEntityTypes}
                params={{ field_class: item?.field_class }}
                valueType="select"
              >
                {item?.entity_type}
              </ProDescriptions.Item>
              {isKafka && item.trigger_type ? (
                <ProDescriptions.Item title="触发器类型" valueEnum={FILTER_TYPE_MAP} valueType='select'>
                  {item.trigger_type}
                </ProDescriptions.Item>
              ) : null}
            </ProDescriptions>
            <FieldView
              isConsumer
              fieldNames={item?.fields}
              onlyDependentFields={item?.dependent_fields}
              entityType={item?.entity_type}
              fieldClass={item?.field_class}
            />
            {isKafka && item?.transform?.many ? (
              <ProDescriptions bordered title="数据转换配置" style={{ marginTop: 16 }}>
                <ProDescriptions.Item title="JSON转换插件(many)的表达式" valueType='jsonCode' span={3}>
                  {item?.transform?.many}
                </ProDescriptions.Item>
              </ProDescriptions>
            ) : null}
            {isKafka && Boolean(item.expressions?.length) && (
              <ProTable
                columns={expressionColumns}
                dataSource={item.expressions}
                pagination={false}
                search={false}
                style={{ marginTop: 16 }}
                scroll={{ y: 300, x: 'max-content' }}
              />
            )}
          </ProCard>
        ))}
        {!formatConsumer?.subscribe_fields?.field_sets?.length && (
          <Empty description="暂无订阅字段" />
        )}
      </ProCard>
      {isKafka && Boolean(formatConsumer?.subscribe_fields?.joins?.length) && (
        <ProCard title="数据关联配置" bordered headerBordered style={{ marginTop: 16 }}>
          {formatConsumer?.subscribe_fields?.joins?.map((join, index) => (
            <ProCard
              key={index}
              title={`数据关联配置${index + 1}`}
              style={{ marginTop: 16, width: '100%' }}
              headerBordered
              boxShadow
              direction='column'
            >
              <ProDescriptions bordered>
                <ProDescriptions.Item title="数据名称">
                  {join.name}
                </ProDescriptions.Item>
                <ProDescriptions.Item title="ID路径">
                  {join.id_path}
                </ProDescriptions.Item>
                <ProDescriptions.Item title="连接条件" valueType='jsonCode' span={3}>
                  {join.condition}
                </ProDescriptions.Item>
                <ProDescriptions.Item title="字段分类" valueEnum={FIELD_CLASS_MAP} valueType='select'>
                  {join.fields?.field_class}
                </ProDescriptions.Item>
                <ProDescriptions.Item
                  title="介质类型"
                  request={fetchEntityTypes}
                  params={{ field_class: join.fields?.field_class }}
                  valueType='select'
                >
                  {join.fields?.entity_type}
                </ProDescriptions.Item>
              </ProDescriptions>
              <ProCard title="字段列表" style={{ marginTop: 16 }} headerBordered>
                <FieldView
                  isConsumer
                  fieldNames={join.fields?.fields}
                  entityType={join.fields?.entity_type}
                  fieldClass={join.fields?.field_class}
                />
              </ProCard>
            </ProCard>
          ))}
        </ProCard>
      )}
      {isKafka && isStatic && (
        <ProDescriptions
          title="数据转换配置"
          bordered
          style={{ marginTop: 16 }}
          column={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 2, xxl: 2 }}
          labelStyle={{ width: 200 }}
        >
          <ProDescriptions.Item title="数据序列化类型" valueEnum={DATA_SERIALIZATION_TYPE_MAP} valueType='select' span={4}>
            {formatConsumer?.subscribe_fields?.serialization_type}
          </ProDescriptions.Item>
          <ProDescriptions.Item title="数据编码类型" valueEnum={DATA_COMPRESSION_TYPE_MAP} valueType='select' span={4}>
            {formatConsumer?.subscribe_fields?.encode_type}
          </ProDescriptions.Item>
          <ProDescriptions.Item title="字段映射参数" valueType='jsonCode' span={4}>
            {formatConsumer?.subscribe_fields?.global_transform?.many}
          </ProDescriptions.Item>
          <ProDescriptions.Item title="Component参数" valueType='jsonCode' span={4}>
            {formatConsumer?.subscribe_fields?.global_transform?.component}
          </ProDescriptions.Item>
        </ProDescriptions>
      )}
      {isKafka && (
        <ProCard title="订阅条件配置" bordered headerBordered style={{ marginTop: 16 }}>
          <ProDescriptions
            bordered
            column={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 2, xxl: 2 }}
            labelStyle={{ width: 200 }}
          >
            {isStatic ? (
              <>
                <ProDescriptions.Item
                  title="是否过滤清洗流量"
                  valueType='switch'
                  fieldProps={SWITCH_PROPS}
                >
                  {formatConsumer?.subscribe_control?.is_clean_filter}
                </ProDescriptions.Item>
                <ProDescriptions.Item
                  title="数据来源"
                  valueEnum={DATA_SOURCE_MAP}
                  valueType='select'
                >
                  {formatConsumer?.subscribe_control?.sources}
                </ProDescriptions.Item>
              </>
            ) : null}
            <ProDescriptions.Item title="协议版本" valueEnum={PROTOCOL_VERSION_MAP} valueType='select'>
              {formatConsumer?.subscribe_control?.protocol_version}
            </ProDescriptions.Item>
            <ProDescriptions.Item title="订阅类型" valueEnum={SUBSCRIBE_TYPE_MAP} valueType='select'>
              {formatConsumer?.subscribe_control?.subscribe_type}
            </ProDescriptions.Item>
            <ProDescriptions.Item title="是否需要全量数据" valueType='switch' fieldProps={SWITCH_PROPS}>
              {formatConsumer?.subscribe_control?.need_full_data}
            </ProDescriptions.Item>
            <ProDescriptions.Item title="是否需要前值" valueType='switch' fieldProps={SWITCH_PROPS}>
              {formatConsumer?.subscribe_control?.need_pre_data}
            </ProDescriptions.Item>
            {isStatic ? (
              <ProDescriptions.Item
                title="是否启用全局过滤器"
                valueType='switch'
                fieldProps={SWITCH_PROPS}
              >
                {formatConsumer?.subscribe_control?.global_trigger?.status}
              </ProDescriptions.Item>
            ) : null}
            {formatConsumer?.subscribe_control?.global_trigger?.status && isStatic && (
              <>
                <ProDescriptions.Item title="字段是否改变" valueType='switch' fieldProps={SWITCH_PROPS}>
                  {formatConsumer?.subscribe_control?.global_trigger?.field_changed}
                </ProDescriptions.Item>
                <ProDescriptions.Item title="更新或插入操作" valueType='code' span={3}>
                  {formatConsumer?.subscribe_control?.global_trigger?.upsert}
                </ProDescriptions.Item>
                <ProDescriptions.Item title="删除操作" valueType='code' span={3}>
                  {formatConsumer?.subscribe_control?.global_trigger?.delete}
                </ProDescriptions.Item>
              </>
            )}
          </ProDescriptions>
        </ProCard>
      )}
      {isKafka && isStatic && formatConsumer?.observability && (
        <ProCard title="可观测性配置" bordered headerBordered wrap style={{ marginTop: 16 }}>
          <ProDescriptions
            bordered
            title="日志采样"
            column={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 2, xxl: 2 }}
            labelStyle={{ width: 200 }}
          >
            <ProDescriptions.Item title="采样率">
              {formatConsumer?.observability?.log_sample?.sample_rate}
            </ProDescriptions.Item>
            <ProDescriptions.Item title="开启时间">
              {formatConsumer?.observability?.log_sample?.start_time ? dayjs.unix(formatConsumer?.observability?.log_sample?.start_time).format('YYYY-MM-DD HH:mm:ss') : '--'}
            </ProDescriptions.Item>
          </ProDescriptions>
          {Boolean(formatConsumer?.observability?.field_monitors?.length) && (
            <ProCard title="字段监控" bordered headerBordered style={{ marginTop: 16 }}>
              <ProTable
                columns={fieldMonitorColumns}
                dataSource={formatConsumer?.observability?.field_monitors}
                pagination={false}
                search={false}
                toolBarRender={false}
                bordered
                size="small"
                scroll={{ y: 300, x: 'max-content' }}
              />
            </ProCard>
          )}
        </ProCard>
      )}
    </>
  );
}
