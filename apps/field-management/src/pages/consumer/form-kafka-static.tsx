import { Space } from 'antd';
import { useUpdate, useMemoizedFn } from 'ahooks';
import {
  ProCard,
  ProFormDependency,
  ProFormText,
  ProFormSwitch,
  ProFormSelect,
  ProFormList,
  ProForm,
  ProFormGroup,
  ProFormDigit,
  ProFormDateTimePicker,
} from '@ant-design/pro-components';
import { CodeEditor } from '@packages/components';
import FieldSelect from '@/components/field-select';
import { fetchEntityTypes } from '@/services/field';
import {
  DATA_SERIALIZATION_TYPE_OPTIONS,
  DATA_COMPRESSION_TYPE_OPTIONS,
  DATA_SOURCE_OPTIONS,
  SUBSCRIBE_TYPE_OPTIONS,
  FIELD_FILTER_MODE_OPTIONS,
  PROTOCOL_VERSION_OPTIONS,
} from '@/constants/consumer';
import { FILTER_TYPE_OPTIONS } from '@/constants/common';
import { jsonValidationRule, noSpaceValidationRule } from '@/constants/rules';
import { FIELD_CLASS_OPTIONS } from '@/constants/field';
import { FieldClass } from '@/types/api';
import Expressions from './expressions';
import { proCardStyle, KafkaFormProps, fieldSetValidator } from './consts';

export default function KafkaStaticForm(props: KafkaFormProps) {
  const { subFieldClass, getEntityTypeOptions, isRestoringFromDraft, isEdit, formRef } = props;
  const update = useUpdate();

  const handleJoinsFieldClassChange = useMemoizedFn((value: FieldClass, index: number) => {
    const key = ['consumer', 'subscribe_fields', 'joins', index];
    // @ts-ignore
    const curJions = formRef.current?.getFieldValue(key);
    // @ts-ignore
    formRef.current?.setFieldValue(key, {
      ...curJions,
      fields: {
        field_class: value,
        entity_type: undefined,
        fields: [],
      },
    });
  });

  return (
    <>
      <ProCard
        title='订阅字段配置'
        bordered
        headerBordered
        collapsible
        style={proCardStyle}
      >
        <ProFormList
          name={['consumer', 'subscribe_fields', 'field_sets']}
          label='订阅字段集合'
          tooltip='同一字段类型下介质类型不能重复'
          rules={[{ validator: (_, value) => fieldSetValidator(value, subFieldClass) }]}
          copyIconProps={false}
          itemRender={({ listDom, action }, { index }) => (
            <ProCard
              boxShadow
              collapsible
              style={{ marginBlockEnd: 8 }}
              title={`订阅字段集合${index + 1}（介质类型不能重复）`}
              extra={action}
              bodyStyle={{ paddingBlockEnd: 0 }}
            >
              {listDom}
            </ProCard>
          )}
        >
          <ProFormSelect
            name={['entity_type']}
            label='介质类型'
            placeholder='请选择介质类型'
            tooltip='切换介质类型后，订阅字段会重置'
            rules={[{ required: true, message: '请选择介质类型' }]}
            onChange={update}
            request={getEntityTypeOptions}
            showSearch
            params={{
              field_class: subFieldClass,
            }}
          />
          <ProFormDependency name={['entity_type']}>
            {({ entity_type }) => (
              <ProForm.Item
                name={['fieldsInfo']}
                label='字段列表'
              >
                <FieldSelect
                  fieldClass={subFieldClass}
                  entityType={entity_type}
                  isConsumer
                  hasDependentOnly
                  skipClearOnParamsChange={isRestoringFromDraft}
                />
              </ProForm.Item>
            )}
          </ProFormDependency>
          <ProFormSwitch
            name={['transform', 'status']}
            label='是否启用字段映射'
            initialValue={false}
          />
          <ProFormDependency
            name={['transform', 'status']}
            key='transform-status'
          >
            {({ transform }) => {
              if (transform?.status) {
                return (
                  <ProForm.Item
                    label='字段映射参数'
                    name={['transform', 'many']}
                    initialValue={'{}'}
                  >
                    <CodeEditor language='json' />
                  </ProForm.Item>
                );
              }
            }}
          </ProFormDependency>
          <ProFormSelect
            name='trigger_type'
            label='触发器类型'
            initialValue='change'
            style={{ width: 230 }}
            options={FILTER_TYPE_OPTIONS}
          />
          <ProFormDependency
            name={['trigger_type']}
            key='dependency-filter-type'
          >
            {({ trigger_type }) => {
              if (trigger_type !== 'expr' && trigger_type !== 'view') {
                return null;
              }
              return <Expressions fieldClass={subFieldClass} />;
            }}
          </ProFormDependency>
        </ProFormList>
        <ProFormGroup>
          <ProFormSelect
            name={[
              'consumer',
              'subscribe_fields',
              'serialization_type',
            ]}
            width={200}
            label='数据序列化类型'
            options={DATA_SERIALIZATION_TYPE_OPTIONS}
            rules={[
              { required: true, message: '请选择数据序列化类型' },
            ]}
          />
          <ProFormSelect
            name={['consumer', 'subscribe_fields', 'encode_type']}
            label='数据编码类型'
            rules={[{ required: true, message: '请选择数据编码类型' }]}
            options={DATA_COMPRESSION_TYPE_OPTIONS}
          />
        </ProFormGroup>
        <ProForm.Item
          label='字段映射参数'
          name={[
            'consumer',
            'subscribe_fields',
            'global_transform',
            'many',
          ]}
          rules={[jsonValidationRule]}
        >
          <CodeEditor language='json' />
        </ProForm.Item>
        <ProForm.Item
          label='Component参数'
          name={[
            'consumer',
            'subscribe_fields',
            'global_transform',
            'component',
          ]}
          rules={[jsonValidationRule]}
        >
          <CodeEditor language='json' />
        </ProForm.Item>
        <ProFormList
          name={['consumer', 'subscribe_fields', 'joins']}
          label='数据关联配置'
          itemRender={({ listDom, action }, { index }) => (
            <ProCard
              boxShadow
              collapsible
              style={{ marginBlockEnd: 8 }}
              title={`数据关联配置${index + 1}`}
              extra={action}
              bodyStyle={{ paddingBlockEnd: 0 }}
            >
              {listDom}
            </ProCard>
          )}
        >
          {(_f, index, _action) => (
            <>
              <ProFormGroup>
                <ProFormText
                  name='name'
                  rules={[
                    { required: true, message: '请输入数据名称' },
                    noSpaceValidationRule,
                  ]}
                  label='数据名称'
                />
                <ProFormText
                  name='id_path'
                  rules={[
                    { required: true, message: '请输入ID路径' },
                    noSpaceValidationRule,
                  ]}
                  label='ID路径'
                />
              </ProFormGroup>
              <ProForm.Item
                name='condition'
                rules={[{ required: true, message: '请输入连接条件' }]}
                label='连接条件'
              >
                <CodeEditor language='go' />
              </ProForm.Item>
              <ProFormSelect
                name={['fields', 'field_class']}
                label='字段分类'
                placeholder='请选择字段分类'
                rules={[{ required: true, message: '请选择字段分类' }]}
                options={FIELD_CLASS_OPTIONS}
                onChange={value => handleJoinsFieldClassChange(value as FieldClass, index)}
              />
              <ProFormDependency
                name={['fields', 'field_class']}
                key='dependency-fields-class'
              >
                {({ fields }) => (
                  <ProFormSelect
                    name={['fields', 'entity_type']}
                    label='介质类型'
                    placeholder='请选择介质类型'
                    rules={[{ required: true, message: '请选择介质类型' }]}
                    request={fetchEntityTypes}
                    showSearch
                    params={{
                      field_class: fields?.field_class,
                    }}
                  />
                )}
              </ProFormDependency>
              <ProFormDependency
                name={[['fields', 'field_class'], ['fields', 'entity_type']]}
                key='dependency-fields-entity-type'
              >
                {({ fields }) => (
                  <ProForm.Item
                    name={['fields', 'fields']}
                    label='字段列表'
                    rules={[
                      {
                        required: true,
                        message: '请选择字段列表',
                      },
                    ]}
                  >
                    <FieldSelect
                      fieldClass={fields?.field_class}
                      entityType={fields?.entity_type}
                      isConsumer
                      skipClearOnParamsChange={isRestoringFromDraft}
                    />
                  </ProForm.Item>
                )}
              </ProFormDependency>
            </>
          )}
        </ProFormList>
      </ProCard>
      <ProCard
        title='订阅条件配置'
        bordered
        headerBordered
        collapsible
        direction='column'
        style={proCardStyle}
      >
        <ProCard
          title='全局下发条件'
          bordered
          headerBordered
          collapsible
          style={{ width: '100%' }}
        >
          <ProFormSwitch
            name={[
              'consumer',
              'subscribe_control',
              'global_trigger',
              'status',
            ]}
            label='是否启用全局下发条件'
            rules={[
              {
                required: true,
                message: '请选择是否启用全局下发条件',
              },
            ]}
          />
          <ProFormDependency
            name={[
              'consumer',
              'subscribe_control',
              'global_trigger',
              'status',
            ]}
          >
            {({ consumer }) => {
              if (consumer?.subscribe_control?.global_trigger?.status) {
                return (
                  <>
                    <ProFormSwitch
                      name={[
                        'consumer',
                        'subscribe_control',
                        'global_trigger',
                        'field_changed',
                      ]}
                      label='订阅字段是否有变更'
                      rules={[
                        {
                          required: true,
                          message: '请选择特性是否改变',
                        },
                      ]}
                    />
                    <ProForm.Item
                      name={[
                        'consumer',
                        'subscribe_control',
                        'global_trigger',
                        'upsert',
                      ]}
                      label='upsert'
                    >
                      <CodeEditor language='go' />
                    </ProForm.Item>
                    <ProForm.Item
                      name={[
                        'consumer',
                        'subscribe_control',
                        'global_trigger',
                        'delete',
                      ]}
                      label='delete'
                    >
                      <CodeEditor language='go' />
                    </ProForm.Item>
                  </>
                );
              }
            }}
          </ProFormDependency>
        </ProCard>
        <ProFormSwitch
          name={['consumer', 'subscribe_control', 'is_clean_filter']}
          label='是否过滤清洗流量'
          rules={[
            {
              required: true,
              message: '请选择是否过滤清洗流量',
            },
          ]}
        />
        <ProFormSelect
          name={['consumer', 'subscribe_control', 'sources']}
          label='数据来源'
          mode='multiple'
          options={DATA_SOURCE_OPTIONS}
        />
        <ProFormSelect
          name={['consumer', 'subscribe_control', 'protocol_version']}
          label='协议版本'
          disabled={isEdit}
          options={PROTOCOL_VERSION_OPTIONS}
        />
        <ProFormSelect
          name={['consumer', 'subscribe_control', 'subscribe_type']}
          label='订阅类型'
          options={SUBSCRIBE_TYPE_OPTIONS}
          rules={[{ required: true, message: '请选择订阅类型' }]}
        />
        <ProFormSelect
          name={['consumer', 'subscribe_control', 'field_filter_mode']}
          label='字段过滤模式'
          options={FIELD_FILTER_MODE_OPTIONS}
          rules={[{ required: true, message: '请选择字段过滤模式' }]}
        />
        <ProFormSwitch
          name={['consumer', 'subscribe_control', 'need_full_data']}
          label='是否需要全量数据'
        />
        <ProFormSwitch
          name={['consumer', 'subscribe_control', 'need_pre_data']}
          label='是否需要前值'
        />
      </ProCard>
      <ProCard
        title='可观测性配置'
        bordered
        headerBordered
        collapsible
        style={proCardStyle}
      >
        <ProFormGroup title='日志采样'>
          <ProFormDigit
            name={[
              'consumer',
              'observability',
              'log_sample',
              'sample_rate',
            ]}
            label='采样率'
            min={0}
            max={1}
            fieldProps={{
              step: 0.1,
              precision: 2,
            }}
            tooltip='取值范围: 0-1，0表示不采样，1表示全采样'
            rules={[{ required: true, message: '请输入采样率' }]}
          />
          <ProFormDateTimePicker
            name={[
              'consumer',
              'observability',
              'log_sample',
              'start_time',
            ]}
            label='开始时间'
            rules={[{ required: true, message: '请选择开始时间' }]}
          />
        </ProFormGroup>
        <ProFormList
          name={['consumer', 'observability', 'field_monitors']}
          label='字段监控'
          tooltip='可以监控特定字段的数据质量'
          alwaysShowItemLabel
          creatorButtonProps={{
            creatorButtonText: '添加字段监控',
          }}
          itemRender={({ listDom, action }) => (
            <ProCard
              boxShadow
              collapsible
              style={{ marginBlockEnd: 8 }}
              extra={action}
              bodyStyle={{ paddingBlockEnd: 0 }}
            >
              {listDom}
            </ProCard>
          )}
        >
          <ProFormGroup>
            <ProFormText
              name='name'
              label='监控名称'
              placeholder='请输入监控名称'
              tooltip='请输入监控名称'
              style={{ width: 200 }}
              rules={[
                { required: true, message: '请输入监控名称' },
                noSpaceValidationRule,
              ]}
            />
            <ProFormSelect
              name='entity_types'
              label='介质类型'
              mode='multiple'
              placeholder='请选择介质类型'
              tooltip='可以选择多个介质类型'
              request={fetchEntityTypes}
              style={{ width: 400 }}
              showSearch
              params={{
                field_class: subFieldClass,
              }}
              rules={[{ required: true, message: '请选择介质类型' }]}
            />
          </ProFormGroup>
          <ProFormList
            name='config'
            label='字段监控配置'
            itemRender={({ listDom, action }) => (
              <ProCard
                boxShadow
                style={{ marginBlockEnd: 8 }}
                bodyStyle={{ paddingBlockEnd: 0 }}
              >
                <Space>
                  {listDom}
                  {action}
                </Space>
              </ProCard>
            )}
          >
            <ProFormGroup>
              <ProFormText
                name='path'
                label='字段路径'
                placeholder='请输入字段路径'
                tooltip='监控的字段路径'
                rules={[
                  { required: true, message: '请输入字段路径' },
                  noSpaceValidationRule,
                ]}
                style={{ width: 200 }}
              />
              <ProFormSelect
                name='values'
                label='可能的取值'
                tooltip='字段可能的取值列表'
                mode='tags'
                placeholder='请输入可能的取值，按回车分隔'
                fieldProps={{
                  tokenSeparators: [','],
                }}
                rules={[{ required: true, message: '请输入可能的取值' }]}
                style={{ width: 400 }}
              />
            </ProFormGroup>
          </ProFormList>
        </ProFormList>
      </ProCard>
    </>
  );
}
