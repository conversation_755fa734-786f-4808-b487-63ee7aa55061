import { useUpdate } from 'ahooks';
import {
  ProCard,
  ProFormDependency,
  ProFormSwitch,
  ProFormSelect,
  ProFormList,
  ProForm,
} from '@ant-design/pro-components';
import FieldSelect from '@/components/field-select';
import {
  SUBSCRIBE_TYPE_OPTIONS,
  PROTOCOL_VERSION_OPTIONS,
} from '@/constants/consumer';
import { DYNAMIC_FILTER_TYPE_OPTIONS } from '@/constants/common';
import Expressions from './expressions';
import { proCardStyle, KafkaFormProps, fieldSetValidator } from './consts';

export default function KafkaDynamicForm(props: KafkaFormProps) {
  const { subFieldClass, isRestoringFromDraft, isEdit, getEntityTypeOptions } = props;
  const update = useUpdate();

  return (
    <>
      <ProCard
        title='订阅字段配置'
        bordered
        headerBordered
        collapsible
        style={proCardStyle}
      >
        <ProFormList
          name={['consumer', 'subscribe_fields', 'field_sets']}
          label='订阅字段集合'
          tooltip='同一字段类型下介质类型不能重复'
          rules={[{ validator: (_, value) => fieldSetValidator(value, subFieldClass) }]}
          copyIconProps={false}
          itemRender={({ listDom, action }, { index }) => (
            <ProCard
              boxShadow
              collapsible
              style={{ marginBlockEnd: 8 }}
              title={`订阅字段集合${index + 1}（介质类型不能重复）`}
              extra={action}
              bodyStyle={{ paddingBlockEnd: 0 }}
            >
              {listDom}
            </ProCard>
          )}
        >
          <ProFormSelect
            name={['entity_type']}
            label='介质类型'
            placeholder='请选择介质类型'
            tooltip='切换介质类型后，订阅字段会重置'
            rules={[{ required: true, message: '请选择介质类型' }]}
            onChange={update}
            request={getEntityTypeOptions}
            showSearch
            params={{
              field_class: subFieldClass,
            }}
          />
          <ProFormDependency name={['entity_type']} key='dependency-entity-type'>
            {({ entity_type }) => (
              <ProForm.Item
                name={['fieldsInfo']}
                label='字段列表'
              >
                <FieldSelect
                  fieldClass={subFieldClass}
                  entityType={entity_type}
                  isConsumer
                  hasDependentOnly
                  skipClearOnParamsChange={isRestoringFromDraft}
                />
              </ProForm.Item>
            )}
          </ProFormDependency>
          <ProFormSelect
            name='trigger_type'
            label='触发器类型'
            initialValue='all'
            style={{ width: 230 }}
            options={DYNAMIC_FILTER_TYPE_OPTIONS}
          />
          <ProFormDependency
            name={['trigger_type']}
            key='dependency-filter-type'
          >
            {({ trigger_type }) => {
              if (trigger_type !== 'expr' && trigger_type !== 'view') {
                return null;
              }
              return <Expressions fieldClass={subFieldClass} />;
            }}
          </ProFormDependency>
        </ProFormList>
      </ProCard>
      <ProCard
        title='订阅条件配置'
        bordered
        headerBordered
        collapsible
        direction='column'
        style={proCardStyle}
      >
        <ProFormSelect
          name={['consumer', 'subscribe_control', 'protocol_version']}
          label='协议版本'
          disabled={isEdit}
          options={PROTOCOL_VERSION_OPTIONS}
        />
        <ProFormSelect
          name={['consumer', 'subscribe_control', 'subscribe_type']}
          label='订阅类型'
          options={SUBSCRIBE_TYPE_OPTIONS}
          rules={[{ required: true, message: '请选择订阅类型' }]}
        />
        <ProFormSwitch
          name={['consumer', 'subscribe_control', 'need_full_data']}
          label='是否需要全量数据'
        />
        <ProFormSwitch
          name={['consumer', 'subscribe_control', 'need_pre_data']}
          label='是否需要前值'
        />
      </ProCard>
    </>
  );
}
