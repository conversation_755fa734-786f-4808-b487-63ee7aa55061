import { useParams } from 'react-router-dom';
import { useRequest } from 'ahooks';
import { PageContainer } from '@ant-design/pro-components';
import { fetchConsumerOrderDetail } from '@/services/consumer';
import OrderInfo from '@/components/order-detail';
import ConsumerDetail from './consumer-detail';

export default function ConsumerOrderDetailPage() {
  const { id } = useParams();

  const { data, loading } = useRequest(fetchConsumerOrderDetail, {
    defaultParams: [{ order_id: Number(id) }],
  });

  const { order, consumer, application } = data || {};

  return (
    <PageContainer title={false} loading={loading}>
      <OrderInfo order={order} />
      <ConsumerDetail consumer={consumer} application={application} marginTop={16} />
    </PageContainer>
  );
}
