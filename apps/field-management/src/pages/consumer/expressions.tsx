import { useRef } from 'react';
import {
  ProFormList,
  ProFormGroup,
  ProFormText,
  ProFormSelect,
  ProCard,
  ProForm,
} from '@ant-design/pro-components';
import { nanoid } from 'nanoid';
import { Space, Button } from 'antd';
import { UpOutlined, DownOutlined, DeleteOutlined } from '@ant-design/icons';
import { CodeEditor } from '@packages/components';
import type { FormListActionType } from '@ant-design/pro-components';
import { EXPRESSION_ACTION_OPTIONS } from '@/constants/consumer';
import { FieldClass } from '@/types/api';
import { noSpaceValidationRule } from '@/constants/rules';

export interface ExpressionsProps {
  fieldClass?: FieldClass;
}

export default function Expressions({ fieldClass }: ExpressionsProps) {
  const expressionsActionRef = useRef<FormListActionType>(undefined);
  return (
    <ProFormList
      name={['expressions']}
      label="表达式集合"
      actionRef={expressionsActionRef}
      itemRender={({ listDom }, { index }) => (
        <ProCard
          boxShadow
          collapsible
          style={{ marginBlockEnd: 8 }}
          key={nanoid()}
          extra={(
            <Space>
              <Button
                icon={<UpOutlined />}
                shape="circle"
                onClick={() => {
                  expressionsActionRef.current?.move(index, index - 1);
                }}
                disabled={index === 0}
              />
              <Button
                icon={<DownOutlined />}
                shape="circle"
                onClick={() => {
                  const list = expressionsActionRef.current?.getList();
                  if (list?.length && list?.length - 1 === index) {
                    return;
                  }
                  expressionsActionRef.current?.move(index, index + 1);
                }}
              />
              <Button
                icon={<DeleteOutlined />}
                shape="circle"
                onClick={() => {
                  expressionsActionRef.current?.remove(index);
                }}
              />
            </Space>
          )}
          bodyStyle={{ paddingBlockEnd: 0 }}
        >
          {listDom}
        </ProCard>
      )}
    >
      <ProFormGroup>
        <ProFormText
          name="name"
          label="表达式名称"
          rules={[
            noSpaceValidationRule,
          ]}
        />
        <ProFormSelect
          name="action"
          label="表达式动作"
          style={{ width: 240 }}
          options={EXPRESSION_ACTION_OPTIONS}
        />
        {fieldClass === FieldClass.FIELD_CLASS_DYNAMIC ? (
          <ProFormText
            name="event_type"
            label="事件类型"
            rules={[
              noSpaceValidationRule,
            ]}
          />
        ) : null}
      </ProFormGroup>
      <ProForm.Item name="condition" label="表达式条件">
        <CodeEditor language="go" />
      </ProForm.Item>
    </ProFormList>
  );
}
