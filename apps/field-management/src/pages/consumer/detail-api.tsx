import { ProDescriptions } from '@ant-design/pro-components';
import { SWITCH_PROPS } from '@/constants/common';
import { API } from '@/types/api';

interface APIDetailProps {
  data?: API;
  hasAuth?: boolean;
  title?: string;
}

export default function ConsumerDetail(props: APIDetailProps ) {
  const { data, hasAuth, title } = props;

  return (
    <ProDescriptions
      title={title}
      bordered
      style={{ marginTop: 16 }}
      column={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 2, xxl: 2 }}
      labelStyle={{ width: 200 }}
    >
      <ProDescriptions.Item label="消费方服务名">
        {data?.caller}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="总库接出侧服务名">
        {data?.service_name}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="API名称">
        {data?.api_name}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="限流频次(QPS)">
        {data?.rate_limit}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="APPKEY">
        {hasAuth ? data?.app_key : '******'}
      </ProDescriptions.Item>
      <ProDescriptions.Item
        label="是否启用APPKEY鉴权"
        valueType='switch'
        fieldProps={SWITCH_PROPS}
      >
        {data?.check_app_key}
      </ProDescriptions.Item>
      <ProDescriptions.Item
        label="是否启用消费方服务名名鉴权"
        valueType='switch'
        fieldProps={SWITCH_PROPS}
      >
        {data?.check_caller}
      </ProDescriptions.Item>
    </ProDescriptions>
  );
}
