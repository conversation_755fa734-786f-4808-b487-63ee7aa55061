import { useMemo } from 'react';
import { PageContainer, ProTable, ProColumns } from '@ant-design/pro-components';
import { Button, Space } from 'antd';
import { AuthButton } from '@/components/auth-button';
import OfflineModal from '@/components/offline-modal';
import { fetchConsumerApps } from '@/services/consumer';
import type { AppSummary } from '@/types/api';
import { useAppList } from '@/hooks/useAppList';
import { searchFormLayout } from '@/constants/common';
import { APP_COLUMNS } from '@/constants/apps';

export default function AppPage() {
  const { handleDetail, canEdit, handleEdit, handleOffline } = useAppList('consumer');

  const appColumns: ProColumns<AppSummary>[] = useMemo(() => [
    ...APP_COLUMNS,
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      fixed: 'right',
      width: 100,
      render: (_, record) => (
        <Space>
          <AuthButton
            authUsers={record.owner}
            adminAccess
            style={{ margin: 0, padding: 0 }}
            type="link"
            disabled={!canEdit(record)}
            onClick={() => handleEdit(record)}
          >
            编辑
          </AuthButton>
          <Button style={{ margin: 0, padding: 0 }} type="link" onClick={() => handleDetail(record)}>详情</Button>
          <AuthButton
            authUsers={record.owner}
            adminAccess
            style={{ margin: 0, padding: 0 }}
            type="link"
            disabled={!canEdit(record)}
            onClick={() => handleOffline(record)}
          >
            下线
          </AuthButton>
        </Space>
      ),
    },
  ], [handleDetail]);
  return (
    <PageContainer title={false}>
      <ProTable<AppSummary>
        columns={appColumns}
        request={fetchConsumerApps}
        rowKey="app_id"
        search={{
          layout: 'vertical',
          labelWidth: 100,
          span: searchFormLayout,
          defaultCollapsed: false,
        }}
        pagination={{
          showSizeChanger: true,
        }}
        scroll={{
          x: 'max-content',
          y: 800,
        }}
      />
      <OfflineModal mode="consumer" />
    </PageContainer>
  );
}
