import { RefObject } from 'react';
import { useMemoizedFn, useUpdate } from 'ahooks';
import {
  ProCard,
  ProFormDependency,
  ProFormSelect,
  ProFormList,
  ProForm,
} from '@ant-design/pro-components';
import type { ProFormInstance } from '@ant-design/pro-components';
import FieldSelect from '@/components/field-select';
import { fetchEntityTypes } from '@/services/field';
import { FIELD_CLASS_OPTIONS } from '@/constants/field';
import { FIELD_FILTER_MODE_OPTIONS } from '@/constants/consumer';
import { FieldClass, TransformFieldSet } from '@/types/api';
import { FormatConsumerRequest } from '@/types/consumer';
import './form.css';

const proCardStyle = {
  marginBlockEnd: 16,
  width: '100%',
};

export interface ApiFormProps {
  subFieldClass: FieldClass;
  formRef: RefObject<ProFormInstance<FormatConsumerRequest> | undefined>;
  isRestoringFromDraft: boolean;
  isEdit: boolean;
}

export default function ApiForm(props: ApiFormProps) {
  const { subFieldClass, formRef, isRestoringFromDraft } = props;

  const update = useUpdate();

  const getFieldClassOptions = useMemoizedFn(() => {
    if (subFieldClass === FieldClass.FIELD_CLASS_DYNAMIC) {
      return FIELD_CLASS_OPTIONS.filter(option => option.value === FieldClass.FIELD_CLASS_DYNAMIC);
    }
    return FIELD_CLASS_OPTIONS;
  });

  const getEntityTypeOptions = useMemoizedFn(async (params: { field_class: FieldClass, keyWords: string }) => {
    const fieldSets = formRef.current?.getFieldValue([
      'consumer',
      'subscribe_fields',
      'field_sets',
    ]);
    const res = await fetchEntityTypes({
      field_class: params.field_class,
      keyWords: params.keyWords,
    });
    return res?.filter(item => !fieldSets?.some((fieldSet: TransformFieldSet) => fieldSet.entity_type === item.value && fieldSet.field_class === params.field_class));
  });

  return (
    <ProCard
      title='订阅字段配置'
      bordered
      headerBordered
      collapsible
      style={proCardStyle}
    >
      <ProFormList
        name={['consumer', 'subscribe_fields', 'field_sets']}
        label='订阅字段集合'
        tooltip='同一字段类型下介质类型不能重复'
        rules={[
          {
            validator: async (_, value) => {
              // 按字段类型分组
              const groupByFieldClass: Record<string, string[]> = value.reduce((acc: Record<string, string[]>, item: any) => {
                const fieldClass = item.field_class;
                if (!acc[fieldClass]) {
                  acc[fieldClass] = [];
                }
                acc[fieldClass].push(item.entity_type);
                return acc;
              }, {});
              // 检查每个字段类型下的介质类型是否有重复
              for (const [fieldClass, entityTypes] of Object.entries(groupByFieldClass)) {
                const uniqueEntityTypes = new Set<string>(entityTypes);
                if (uniqueEntityTypes.size < entityTypes.length) {
                  return Promise.reject(new Error(`字段类型 ${fieldClass} 下存在重复的介质类型`));
                }
              }
            },
          },
        ]}
        copyIconProps={false}
        itemRender={({ listDom, action }, { index }) => (
          <ProCard
            boxShadow
            collapsible
            style={{ marginBlockEnd: 8 }}
            title={`订阅字段集合${index + 1}（介质类型不能重复）`}
            extra={action}
            bodyStyle={{ paddingBlockEnd: 0 }}
          >
            {listDom}
          </ProCard>
        )}
      >
        <ProFormSelect
          name={['field_class']}
          label='字段分类'
          options={getFieldClassOptions()}
          placeholder='请选择字段分类'
          tooltip='切换字段分类后，订阅字段会重置'
          rules={[{ required: true, message: '请选择字段分类' }]}
        />
        <ProFormDependency
          name={['field_class']}
          key='dependency-field-class'
        >
          {({ field_class }) => (
            <ProFormSelect
              name={['entity_type']}
              label='介质类型'
              placeholder='请选择介质类型'
              tooltip='切换介质类型后，订阅字段会重置'
              rules={[{ required: true, message: '请选择介质类型' }]}
              onChange={update}
              request={getEntityTypeOptions}
              showSearch
              params={{
                field_class,
              }}
            />
          )}
        </ProFormDependency>
        <ProFormDependency name={['field_class', 'entity_type']}>
          {({ field_class, entity_type }) => (
            <ProForm.Item
              name={['fieldsInfo']}
              label='字段列表'
            >
              <FieldSelect
                fieldClass={field_class}
                entityType={entity_type}
                isConsumer
                hasDependentOnly
                skipClearOnParamsChange={isRestoringFromDraft}
              />
            </ProForm.Item>
          )}
        </ProFormDependency>
      </ProFormList>
      <ProFormSelect
        name={['consumer', 'subscribe_control', 'field_filter_mode']}
        label='字段过滤模式'
        options={FIELD_FILTER_MODE_OPTIONS}
        rules={[{ required: true, message: '请选择字段过滤模式' }]}
      />
    </ProCard>
  );
}
