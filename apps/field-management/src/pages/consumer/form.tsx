import { RefObject, useRef, useState } from 'react';
import dayjs from 'dayjs';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import omit from 'lodash/omit';
import merge from 'lodash/merge';
import { App } from 'antd';
import { useMemoizedFn, useAsyncEffect, useCreation } from 'ahooks';
import {
  PageContainer,
  ProCard,
  StepsForm,
  ProFormText,
  ProFormTextArea,
  ProFormSelect,
  ProFormRadio,
  ProFormGroup,
  ProFormDigit,
} from '@ant-design/pro-components';
import type { ProFormInstance } from '@ant-design/pro-components';
import type { RadioChangeEvent } from 'antd/es/radio';
import { useDraft } from '@/hooks/useDraft';
import { useAccess } from '@/hooks/useAccess';
import { getUsers } from '@/services/user';
import { getFormatFormValue, getFormatRequestValue } from '@/utils/consumer';
import {
  createConsumer,
  updateConsumer,
  fetchConsumerDetail,
} from '@/services/consumer';
import { FIELD_CLASS_OPTIONS } from '@/constants/field';
import {
  SUBSCRIBE_SERVICE_TYPE_OPTIONS,
  kafkaInitialConsumer,
  apiInitialConsumer,
} from '@/constants/consumer';
import {
  httpUrlValidationRule,
  appNameValidationRule,
  noSpaceValidationRule,
} from '@/constants/rules';
import {
  InvokeType,
  FieldClass,
  Consumer,
} from '@/types/api';
import { FormatConsumerRequest } from '@/types/consumer';
import KafkaForm from './form-kafka';
import ApiForm from './form-api';
import './form.css';

const proCardStyle = {
  marginBlockEnd: 16,
  width: '100%',
};

export default function ConsumerPage() {
  const [invokeType, setInvokeType] = useState<InvokeType>(InvokeType.INVOKE_TYPE_KAFKA);
  const [isRestoringFromDraft, setIsRestoringFromDraft] = useState(false);
  const [subFieldClass, setSubFieldClass] = useState<FieldClass>(FieldClass.FIELD_CLASS_STATIC);
  const formMapRef = useRef<RefObject<ProFormInstance<FormatConsumerRequest> | undefined>[]>([]);
  const formRef = useRef<ProFormInstance<FormatConsumerRequest> | undefined>(undefined);
  const { isAdmin, userInfo } = useAccess();
  const { id } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const isEdit = location.pathname.includes('edit') && Boolean(id);
  const { message } = App.useApp();
  const initialValues = useCreation<FormatConsumerRequest>(
    () => ({
      application: {
        // @ts-ignore
        owner: [userInfo.name],
      },
      order: {
        applicant: userInfo.name,
      },
      consumer: kafkaInitialConsumer,
    }),
    [userInfo.name],
  );

  useAsyncEffect(async () => {
    if (isEdit) {
      const res = await fetchConsumerDetail({ app_name: id });
      if (!res) {
        message.error('获取消费方信息失败');
        return;
      }
      const { consumer, application } = res;
      const owners = application?.owner?.split(';') || [];
      const hasAuth = isAdmin() || owners.includes(userInfo.name);
      if (!hasAuth) {
        message.error('您没有权限编辑该消费方信息');
        setTimeout(() => {
          navigate('/403');
        }, 1000);
        return;
      }
      const formatConsumer = getFormatFormValue(consumer as Consumer);
      if (formatConsumer.observability?.log_sample?.start_time) {
        // @ts-ignore
        formatConsumer.observability.log_sample.start_time = dayjs.unix(Number(formatConsumer.observability.log_sample.start_time));
      }
      const newInitialValues = {
        ...omit(initialValues, ['consumer', 'application']),
        application,
        consumer: formatConsumer,
      };
      setInvokeType(consumer?.subscribe_service?.invoke_type as InvokeType);
      setSubFieldClass(consumer?.subscribe_service?.field_class as FieldClass);
      setIsRestoringFromDraft(true);
      formMapRef.current?.forEach((form) => {
        form.current?.setFieldsValue(newInitialValues);
      });
      setTimeout(() => {
        setIsRestoringFromDraft(false);
      }, 1000);
    } else {
      formMapRef.current?.forEach((form) => {
        form.current?.setFieldsValue(initialValues);
      });
    }
  }, [isEdit, id, initialValues, userInfo.name]);

  const handleLoadDraft = useMemoizedFn((draft: FormatConsumerRequest) => {
    setIsRestoringFromDraft(true);
    formMapRef.current?.forEach((form) => {
      form.current?.setFieldsValue(draft);
    });
    if (draft.consumer?.subscribe_service?.invoke_type) {
      setInvokeType(draft.consumer.subscribe_service.invoke_type as InvokeType);
    }
    setSubFieldClass(draft?.consumer?.subscribe_service?.field_class as FieldClass);
    setTimeout(() => {
      setIsRestoringFromDraft(false);
    }, 0);
  });
  const { saveDraft } = useDraft<FormatConsumerRequest>({
    key: 'consumer_create_draft',
    disabled: isEdit,
    onLoadDraft: handleLoadDraft,
  });

  const handleInvokeTypeChange = useMemoizedFn((e: RadioChangeEvent) => {
    const newInvokeType = e.target.value as InvokeType;
    setInvokeType(newInvokeType);
    const initialConsumer = newInvokeType === InvokeType.INVOKE_TYPE_KAFKA ? kafkaInitialConsumer : apiInitialConsumer;
    formMapRef.current?.forEach((form) => {
      form.current?.setFieldValue(['consumer'], {
        ...initialConsumer,
        subscribe_service: {
          ...initialConsumer.subscribe_service,
          invoke_type: newInvokeType,
        },
      });
    });
  });
  // 字段分类改变
  const handleSubFieldClassChange = useMemoizedFn((subscribeFieldClass: FieldClass) => {
    setSubFieldClass(subscribeFieldClass);
    const initialConsumer = invokeType === InvokeType.INVOKE_TYPE_KAFKA ? kafkaInitialConsumer : apiInitialConsumer;
    formMapRef.current?.forEach((form) => {
      form.current?.setFieldValue(['consumer'], {
        ...initialConsumer,
        subscribe_service: {
          ...initialConsumer.subscribe_service,
          field_class: subscribeFieldClass,
        },
      });
    });
  });

  const handleFormChange = useMemoizedFn(() => {
    const formValues = formMapRef.current?.map(form => form.current?.getFieldsValue());
    const mergedValues = merge({}, ...formValues);
    // 缓存草稿
    saveDraft(mergedValues);
  });

  const handleSubmit = useMemoizedFn(async (values: FormatConsumerRequest) => {
    const formatConsumer = getFormatRequestValue(values.consumer, subFieldClass);
    if (formatConsumer.observability?.log_sample?.start_time) {
      const { start_time: startTime } = formatConsumer.observability.log_sample;
      console.log('startTime', startTime);
      if (String(startTime).includes('-')) {
        formatConsumer.observability.log_sample.start_time = dayjs(startTime).unix();
      } else {
        formatConsumer.observability.log_sample.start_time = startTime;
      }
    }
    const params = {
      ...values,
      consumer: formatConsumer,
    };
    // return;
    const res = isEdit
      ? await updateConsumer(params)
      : await createConsumer(params);
    if (res.success) {
      const tips = '已经提交工单，即将跳转至工单列表';
      message.success(isEdit ? `更新成功，${tips}` : `创建成功，${tips}`);
      setTimeout(() => {
        navigate('/consumer/my-orders');
      }, 1000);
    } else {
      message.error(res.message);
    }
  });

  return (
    <PageContainer title={false}>
      <StepsForm<FormatConsumerRequest>
        formMapRef={formMapRef}
        onFormChange={handleFormChange}
        onFinish={handleSubmit}
      >
        <StepsForm.StepForm
          title='基础信息'
          key='base-info'
          name='base-info'
          layout='horizontal'
          initialValues={initialValues}
        >
          <ProCard
            title='工单信息'
            bordered
            headerBordered
            collapsible
            style={proCardStyle}
          >
            <ProFormText name={['order', 'applicant']} label='申请人' hidden />
            <ProFormText
              name={['order', 'tapd']}
              label='TAPD地址'
              rules={[
                { required: true, message: '请输入TAPD地址' },
                httpUrlValidationRule,
              ]}
            />
            <ProFormTextArea name={['order', 'description']} label='工单描述' />
          </ProCard>
          <ProCard
            title='消费方信息'
            bordered
            headerBordered
            collapsible
            style={proCardStyle}
          >
            <ProFormGroup>
              <ProFormText
                name={['application', 'app_name']}
                disabled={isEdit}
                label='应用名称'
                rules={[
                  { required: true, message: '请输入应用名称' },
                  appNameValidationRule,
                ]}
              />
              <ProFormText
                name={['application', 'app_name_ch']}
                label='应用中文名'
                rules={[
                  { required: true, message: '请输入应用中文名' },
                  noSpaceValidationRule,
                ]}
              />
            </ProFormGroup>
            <ProFormSelect
              name={['application', 'owner']}
              label='负责人'
              request={getUsers}
              showSearch
              mode='multiple'
              convertValue={(value: string) => {
                if (typeof value === 'string') {
                  return value?.split(';');
                }
                return value;
              }}
              transform={(value: string[] | string) => {
                if (Array.isArray(value)) {
                  return value?.join(';');
                }
                return value;
              }}
              rules={[{ required: true, message: '请选择负责人' }]}
            />
            <ProFormTextArea
              name={['application', 'description']}
              label='应用描述'
              rules={[
                { required: true, message: '请输入应用描述' },
              ]}
            />
          </ProCard>
        </StepsForm.StepForm>
        <StepsForm.StepForm
          title='订阅信息配置'
          key='field-info'
          name='field-info'
          layout='horizontal'
          labelWrap
          formRef={formRef}
          initialValues={initialValues}
          validateTrigger={['onChange', 'onBlur']}
        >
          <ProCard
            title='订阅信息'
            bordered
            headerBordered
            collapsible
            style={proCardStyle}
          >
            <ProFormRadio.Group
              name={['consumer', 'subscribe_service', 'invoke_type']}
              label='对接方式'
              rules={[{ required: true, message: '请选择对接方式' }]}
              options={SUBSCRIBE_SERVICE_TYPE_OPTIONS}
              disabled={isEdit}
              tooltip='切换对接方式后，订阅信息会重置'
              fieldProps={{
                onChange: handleInvokeTypeChange,
              }}
            />
            <ProFormSelect
              name={['consumer', 'subscribe_service', 'field_class']}
              label='字段分类'
              disabled={isEdit}
              options={FIELD_CLASS_OPTIONS}
              rules={[{ required: true, message: '请选择字段分类' }]}
              tooltip='切换字段分类后，订阅字段会重置'
              fieldProps={{
                onChange: handleSubFieldClassChange,
              }}
            />
            {invokeType === InvokeType.INVOKE_TYPE_API && (
              <>
                <ProFormText
                  name={['consumer', 'subscribe_service', 'api', 'caller']}
                  label='主调方'
                  rules={[
                    { required: true, message: '请输入主调方' },
                    noSpaceValidationRule,
                  ]}
                />
                <ProFormDigit
                  name={['consumer', 'subscribe_service', 'api', 'rate_limit']}
                  label='限流'
                  rules={[{ required: true, message: '请输入限流' }]}
                />
              </>
            )}
          </ProCard>
          {invokeType === InvokeType.INVOKE_TYPE_KAFKA ? (
            <KafkaForm
              subFieldClass={subFieldClass as FieldClass}
              formRef={formRef}
              isRestoringFromDraft={isRestoringFromDraft}
              isEdit={isEdit}
            />
          ) : (
            <ApiForm
              subFieldClass={subFieldClass as FieldClass}
              formRef={formRef}
              isRestoringFromDraft={isRestoringFromDraft}
              isEdit={isEdit}
            />
          )}
        </StepsForm.StepForm>
      </StepsForm>
    </PageContainer>
  );
}
