import { useMemo } from 'react';
import { useParams } from 'react-router-dom';
import { useRequest, useMemoizedFn } from 'ahooks';
import { Button, Typography, Modal } from 'antd';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { fetchConsumerDetail } from '@/services/consumer';
import { ORDER_COLUMNS } from '@/constants/order';
import { OrderDetail, InvokeType } from '@/types/api';
import ConsumerDetail from './detail-consumer';

export default function ConsumerDetailPage() {
  const { id } = useParams();
  const { data, loading } = useRequest(fetchConsumerDetail, {
    defaultParams: [{ app_name: id }],
  });

  const handleOrderDetail = useMemoizedFn((order: OrderDetail) => {
    window.open(`/consumer/order/${order.order_id}`);
  });

  const isKafkaConsumer = useMemo(() => data?.consumer?.subscribe_service?.invoke_type === InvokeType.INVOKE_TYPE_KAFKA, [data]);

  const xyUrl = 'https://xy.woa.com/xy/app/';

  const handleJump = useMemoizedFn(() => {
    Modal.confirm({
      title: '是否跳转管理面板？',
      okText: '正式环境',
      cancelText: '测试环境',
      cancelButtonProps: {
        type: 'primary',
      },
      onOk: () => {
        window.open(`${xyUrl}prod/qq_news_content_databus/distribute_stream_control_tmp?name=${application?.app_name}`);
      },
      onCancel: () => {
        window.open(`${xyUrl}test/qq_news_content_databus/distribute_stream_control_tmp?name=${application?.app_name}`);
      },
    });
  });

  const orderColumns = useMemo(() => ORDER_COLUMNS.map((column) => {
    if (column.dataIndex === 'app_name') {
      return {
        ...column,
        title: '应用名称',
      };
    }
    return column;
  }).concat({
    title: '操作',
    dataIndex: 'action',
    valueType: 'option',
    fixed: 'right',
    width: 100,
    render: (_, record) => (
      <Button type="link" onClick={() => handleOrderDetail(record)}>
        详情
      </Button>
    ),
  }), []);

  const { consumer, application, orders } = data || {};

  return (
    <PageContainer title={false} loading={loading}>
      <ConsumerDetail
        consumer={consumer}
        application={application}
      />
      <Typography.Title level={5} style={{ marginTop: 16 }}>历史工单</Typography.Title>
      <ProTable
        style={{ marginTop: 16 }}
        columns={orderColumns}
        loading={loading}
        dataSource={orders}
        pagination={false}
        search={false}
        toolBarRender={false}
        scroll={{ x: 'max-content', y: 300 }}
      />
      {isKafkaConsumer && (
        <Button
          type="primary"
          onClick={handleJump}
          style={{ position: 'fixed', zIndex: 1000, bottom: 20, right: 20 }}
        >
          跳转到管理面板
        </Button>
      )}
    </PageContainer>
  );
}
