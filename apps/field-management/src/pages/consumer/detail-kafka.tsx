import { ProDescriptions } from '@ant-design/pro-components';
import {
  KAFKA_PARTITIONER_MAP,
  KAFKA_COMPRESSION_MAP,
} from '@/constants/consumer';
import { Kafka } from '@/types/api';

interface KafkaDetailProps {
  data?: Kafka;
  hasAuth?: boolean;
  title?: string;
  isAdmin?: boolean;
}

export default function KafkaDetail(props: KafkaDetailProps) {
  const { data, hasAuth, title, isAdmin } = props;

  return (
    <ProDescriptions
      title={title}
      bordered
      style={{ marginTop: 16 }}
      column={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 2, xxl: 2 }}
      labelStyle={{ width: 200 }}
    >
      <ProDescriptions.Item label="KAFKA地址">
        {data?.address}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="KAFKA主题">
        {data?.topic}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="KAFKA消费组">
        {data?.group}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="KAFKA生产者ID">
        {data?.producer_id}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="KAFKA压缩方式" valueEnum={KAFKA_COMPRESSION_MAP} valueType='select'>
        {data?.compression}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="KAFKA分区策略" valueEnum={KAFKA_PARTITIONER_MAP} valueType='select'>
        {data?.partitioner}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="是否启用读ACL策略">
        {data?.read_acl_strategy?.status ? '启用' : '禁用'}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="读 ACL 用户名" copyable>
        {data?.read_acl_strategy?.user}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="读 ACL 密码" copyable={hasAuth}>
        {hasAuth ? data?.read_acl_strategy?.password : '******'}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="是否启用写ACL策略">
        {data?.write_acl_strategy?.status ? '启用' : '禁用'}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="写 ACL 用户名" copyable>
        {data?.write_acl_strategy?.user}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="写 ACL 密码" copyable={isAdmin}>
        {isAdmin ? data?.write_acl_strategy?.password : '******'}
      </ProDescriptions.Item>
      <ProDescriptions.Item label="是否只打印日志，不实际下发">
        {data?.log_only ? '是' : '否'}
      </ProDescriptions.Item>
    </ProDescriptions>
  );
}
