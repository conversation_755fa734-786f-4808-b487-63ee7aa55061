import { useParams } from 'react-router-dom';
import { useRequest, useMemoizedFn } from 'ahooks';
import { Row, Col, Typography } from 'antd';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { fetchConsumerDetail, fetchConsumerOrderDetail } from '@/services/consumer';
import OrderInfo from '@/components/order-detail';
import ConsumerDetail from './detail-consumer';

export default function ConsumerDiffPage() {
  const { id } = useParams();

  const getDiffConsumer = useMemoizedFn(async () => {
    const orderRes = await fetchConsumerOrderDetail({ order_id: Number(id) });
    const { consumer: newConsumer, order, application } = orderRes || {};
    const { app_name: appName } = newConsumer || {};
    const oldConsumerRes = await fetchConsumerDetail({ app_name: appName });
    const { consumer: oldConsumer, application: oldApplication } = oldConsumerRes || {};
    return {
      newConsumer,
      oldConsumer,
      order,
      application,
      oldApplication,
    };
  });
  const { data, loading } = useRequest(getDiffConsumer, {
    refreshDeps: [id],
  });

  const { newConsumer, oldConsumer, order, application, oldApplication } = data || {};

  return (
    <PageContainer title={false} loading={loading}>
      <OrderInfo order={order} />
      <Typography.Title level={5} style={{ marginTop: 16 }}>消费方对比</Typography.Title>
      <Row style={{ marginTop: 16 }} gutter={16}>
        <Col span={12}>
          <ProCard title="当前生效的消费方详情" bordered headerBordered direction='column'>
            <ConsumerDetail consumer={oldConsumer} application={oldApplication} />
          </ProCard>
        </Col>
        <Col span={12}>
          <ProCard title="本次更新的消费方详情" bordered headerBordered direction='column'>
            <ConsumerDetail consumer={newConsumer} application={application} />
          </ProCard>
        </Col>
      </Row>
    </PageContainer>
  );
}
