import { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useRequest, useMemoizedFn } from 'ahooks';
import { Typography, Tabs } from 'antd';
import { PageContainer } from '@ant-design/pro-components';
import { fetchConsumerDetail, fetchConsumerOrderDetail } from '@/services/consumer';
import OrderInfo from '@/components/order-detail';
import JsonDiff from '@/components/json-diff';
import EnhancedDiff from '@/components/enhanced-diff';
import ConsumerDetail from './detail-consumer';

export default function ConsumerDiffPage() {
  const { id } = useParams();
  const [activeTab, setActiveTab] = useState<string>('ui-diff');

  const getDiffConsumer = useMemoizedFn(async () => {
    const orderRes = await fetchConsumerOrderDetail({ order_id: Number(id) });
    const { consumer: newConsumer, order, application } = orderRes || {};
    const { app_name: appName } = newConsumer || {};
    const oldConsumerRes = await fetchConsumerDetail({ app_name: appName });
    const { consumer: oldConsumer, application: oldApplication } = oldConsumerRes || {};
    return {
      newConsumer,
      oldConsumer,
      order,
      application,
      oldApplication,
    };
  });
  const { data, loading } = useRequest(getDiffConsumer, {
    refreshDeps: [id],
  });

  const { newConsumer, oldConsumer, order, application, oldApplication } = data || {};

  // UI对比视图组件
  const UICompareView = () => (
    <div style={{ marginTop: 16 }}>
      <EnhancedDiff
        oldData={{
          consumer: oldConsumer,
          application: oldApplication,
        }}
        newData={{
          consumer: newConsumer,
          application: application,
        }}
        leftTitle="当前生效的消费方详情"
        rightTitle="本次更新的消费方详情"
        leftContent={<ConsumerDetail consumer={oldConsumer} application={oldApplication} />}
        rightContent={<ConsumerDetail consumer={newConsumer} application={application} />}
        showDiffStats={true}
      />
    </div>
  );

  // JSON对比视图组件
  const JsonCompareView = () => (
    <div style={{ marginTop: 16 }}>
      <JsonDiff
        oldData={{
          consumer: oldConsumer,
          application: oldApplication,
        }}
        newData={{
          consumer: newConsumer,
          application: application,
        }}
        leftTitle="当前生效的消费方详情"
        rightTitle="本次更新的消费方详情"
      />
    </div>
  );

  const tabItems = [
    {
      key: 'ui-diff',
      label: 'UI对比视图',
      children: <UICompareView />,
    },
    {
      key: 'json-diff',
      label: 'JSON差异视图',
      children: <JsonCompareView />,
    },
  ];

  return (
    <PageContainer title={false} loading={loading}>
      <OrderInfo order={order} />
      <Typography.Title level={5} style={{ marginTop: 16 }}>消费方对比</Typography.Title>
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        style={{ marginTop: 16 }}
      />
    </PageContainer>
  );
}
