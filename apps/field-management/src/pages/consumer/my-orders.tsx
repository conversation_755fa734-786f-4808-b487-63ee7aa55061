import { useMemo } from 'react';
import { Button, Space } from 'antd';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-components';
import { fetchMyConsumerOrders } from '@/services/consumer';
import type { OrderDetail } from '@/types/api';
import { useOrderList } from '@/hooks/useOrderList';
import ViewResourceModal from '@/components/alloc-resource/view';

export default function OrderPage() {
  const {
    handleFlowDetail,
    handleOrderDetial,
    handleViewTestResource,
    handleViewProdResource,
    canViewTestResource,
    canViewProdResource,
    columns,
  } = useOrderList('consumer');

  const orderColumns = useMemo<ProColumns<OrderDetail>[]>(() => [
    ...columns,
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      fixed: 'right',
      width: 200,
      render: (_, record) => (
        <Space wrap>
          <Button
            style={{ margin: 0, padding: 0 }}
            type="link"
            onClick={() => handleFlowDetail(record)}
          >
            审批进度
          </Button>
          <Button
            style={{ margin: 0, padding: 0 }}
            type="link"
            onClick={() => handleOrderDetial(record)}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ], [
    columns,
    handleFlowDetail,
    handleOrderDetial,
    handleViewTestResource,
    handleViewProdResource,
    canViewTestResource,
    canViewProdResource,
  ]);

  return (
    <PageContainer
      title={false}
    >
      <ProTable<OrderDetail>
        columns={orderColumns}
        request={fetchMyConsumerOrders}
        rowKey="order_id"
        search={false}
        pagination={{
          showSizeChanger: true,
        }}
        scroll={{
          x: 'max-content',
          y: 800,
        }}
      />
      <ViewResourceModal mode="consumer" />
    </PageContainer>
  );
}
