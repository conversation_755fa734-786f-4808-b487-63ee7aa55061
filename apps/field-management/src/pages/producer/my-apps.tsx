import { useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { PageContainer, ProTable, ProColumns } from '@ant-design/pro-components';
import { Button, Space } from 'antd';
import { fetchMyProducerApps } from '@/services/producer';
import type { AppSummary } from '@/types/api';
import { useAppList } from '@/hooks/useAppList';
import OfflineModal from '@/components/offline-modal';
import { APP_COLUMNS } from '@/constants/apps';

export default function AppPage() {
  const navigate = useNavigate();
  const { handleEdit, handleDetail, handleOffline, canEdit } = useAppList('producer');

  const appColumns: ProColumns<AppSummary>[] = useMemo(() => [
    ...APP_COLUMNS,
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      fixed: 'right',
      width: 200,
      render: (_, record) => (
        <Space>
          <Button style={{ margin: 0, padding: 0 }} type="link" disabled={!canEdit(record)} onClick={() => handleEdit(record)}>编辑</Button>
          <Button style={{ margin: 0, padding: 0 }} type="link" onClick={() => handleDetail(record)}>详情</Button>
          <Button style={{ margin: 0, padding: 0 }} type="link" disabled={!canEdit(record)} onClick={() => handleOffline(record)}>下线</Button>
        </Space>
      ),
    },
  ], [handleEdit, handleDetail, handleOffline]);

  const handleAdd = useCallback(() => {
    navigate('/producer/create');
  }, []);

  return (
    <PageContainer title={false}>
      <ProTable<AppSummary>
        columns={appColumns}
        request={fetchMyProducerApps}
        rowKey="app_id"
        search={false}
        pagination={{
          showSizeChanger: true,
        }}
        toolBarRender={() => [
          <Button type="primary" key="add" onClick={handleAdd}>
            新增
          </Button>,
        ]}
        scroll={{
          x: 'max-content',
          y: 800,
        }}
      />
      <OfflineModal mode="producer" />
    </PageContainer>
  );
}
