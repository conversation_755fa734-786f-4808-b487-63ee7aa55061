import { useMemo, useState } from 'react';
import { Button, Typography, Modal } from 'antd';
import { useMemoizedFn } from 'ahooks';
import { ProCard, ProTable, ProColumns } from '@ant-design/pro-components';
import { SELECT_FIELD_COLUMNS } from '@/constants/field';
import FieldDetail from '@/components/field-detail';
import { FieldDetail as FieldDetailType, GetProducerOrderDetailReplyFieldDetailSet, Producer } from '@/types/api';

interface FieldSetsProps {
  fieldDetailSets?: GetProducerOrderDetailReplyFieldDetailSet[];
  producer?: Producer;
}

export default function FieldSets({ fieldDetailSets, producer }: FieldSetsProps) {
  const [open, setOpen] = useState(false);
  const [curField, setCurField] = useState<FieldDetailType>();

  const handleDetail = useMemoizedFn((record: FieldDetailType) => {
    setCurField(record);
    setOpen(true);
  });

  const optionColumn: ProColumns<FieldDetailType> = useMemo(() => ({
    title: '操作',
    dataIndex: 'action',
    width: 100,
    fixed: 'right',
    render: (_, record) => <Button type='link' onClick={() => handleDetail(record)}>详情</Button>,
  }), [handleDetail]);

  const fieldColumns: ProColumns<FieldDetailType>[] = useMemo(() => [
    ...SELECT_FIELD_COLUMNS.map((item) => {
      if (item.dataIndex === 'entity_type' || item.dataIndex === 'column_family') {
        return {
          ...item,
          params: {
            field_class: producer?.field_class,
          },
        };
      }
      return item;
    }),
    optionColumn,
  ], [producer?.field_class, optionColumn]);

  const addFieldColumns: ProColumns<FieldDetailType>[] = useMemo(() => [
    ...SELECT_FIELD_COLUMNS.map((item) => {
      if (item.dataIndex === 'entity_type' || item.dataIndex === 'column_family') {
        return {
          ...item,
          params: {
            field_class: producer?.field_class,
          },
        };
      }
      if (item.dataIndex === 'status') {
        return {
          ...item,
          hideInTable: true,
        };
      }
      return item;
    }),
    optionColumn,
  ], [producer?.field_class, optionColumn]);

  return (
    <>
      {fieldDetailSets?.map((fieldSet, index) => (
        <ProCard
          key={index}
          title={`字段集合 - ${fieldSet.entity_type}`}
          bordered
          headerBordered
          style={{ marginTop: 16, width: '100%' }}
          direction='column'
        >
          <Typography.Title level={5}>生产字段</Typography.Title>
          <ProTable
            dataSource={fieldSet.exist_fields}
            columns={fieldColumns}
            search={false}
            options={false}
            scroll={{ x: 'max-content', y: 300 }}
            pagination={{
              pageSize: 20,
            }}
          />
          <Typography.Title level={5}>新增字段</Typography.Title>
          <ProTable
            dataSource={fieldSet.add_fields}
            columns={addFieldColumns}
            search={false}
            options={false}
            scroll={{ x: 'max-content', y: 300 }}
            pagination={{
              pageSize: 20,
            }}
          />
        </ProCard>
      ))}
      <Modal
        open={open}
        onCancel={() => setOpen(false)}
        width={800}
        footer={false}
      >
        <FieldDetail field={curField} span={24} isOrder />
      </Modal>
    </>
  );
}
