import { useCallback, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import { Button, Typography } from 'antd';
import { useRequest } from 'ahooks';
import { PageContainer, ProCard, ProTable } from '@ant-design/pro-components';
import { fetchProducerDetail } from '@/services/producer';
import { ORDER_COLUMNS } from '@/constants/order';
import FieldView from '@/components/field-select/view';
import { parseProducerFields } from '@/utils';
import { FieldSet, OrderDetail } from '@/types/api';
import ProducerDetail from './producer-detail';

export default function ProducerDetailPage() {
  const { id } = useParams();
  const { data, loading } = useRequest(fetchProducerDetail, {
    refreshOnWindowFocus: true,
    defaultParams: [{ app_name: id }],
  });

  const { producer, orders } = data || {};

  // 解析字段集合
  const fieldSets: FieldSet[] = parseProducerFields(producer?.produce_fields);

  const handleOrderDetail = useCallback((order: OrderDetail) => {
    window.open(`/producer/order/${order.order_id}`, '_blank');
  }, []);

  const orderColumns = useMemo(() => ORDER_COLUMNS.map((column) => {
    if (column.dataIndex === 'app_name') {
      return {
        ...column,
        title: '应用名称',
      };
    }
    return column;
  }).concat({
    title: '操作',
    dataIndex: 'action',
    valueType: 'option',
    fixed: 'right',
    width: 100,
    render: (_, record) => (
      <Button type="link" onClick={() => handleOrderDetail(record)}>
        详情
      </Button>
    ),
  }), []);

  return (
    <PageContainer title={false} loading={loading}>
      <ProducerDetail producer={producer} />
      {fieldSets.map((fieldSet: FieldSet, index: number) => (
        <ProCard
          key={index}
          title={`字段集合 - ${fieldSet.entity_type}`}
          bordered
          headerBordered
          style={{ marginTop: 16 }}
        >
          <FieldView
            fieldNames={fieldSet.fields}
            entityType={fieldSet.entity_type}
            fieldClass={producer?.field_class}
          />
        </ProCard>
      ))}
      <Typography.Title level={5} style={{ marginTop: 16 }}>历史工单</Typography.Title>
      <ProTable
        style={{ marginTop: 16 }}
        columns={orderColumns}
        dataSource={orders}
        loading={loading}
        pagination={false}
        search={false}
        scroll={{ x: 'max-content', y: 300 }}
      />
    </PageContainer>
  );
}
