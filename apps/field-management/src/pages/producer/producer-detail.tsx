import { ProDescriptions } from '@ant-design/pro-components';
import { useAccess } from '@/hooks/useAccess';
import { SUBSCRIBE_SERVICE_TYPE_MAP } from '@/constants/consumer';
import { FIELD_CLASS_MAP } from '@/constants/field';
import { APP_STATUS_MAP } from '@/constants/apps';
import { InvokeType, Producer } from '@/types/api';

export interface ProducerDetailProps {
  producer?: Producer;
  marginTop?: number;
}

export default function ProducerDetailPage({ producer, marginTop = 0 }: ProducerDetailProps) {
  const { isAdmin, userInfo } = useAccess();

  const isOwner = userInfo.name === producer?.owner;
  const hasAUth = isOwner || isAdmin();

  return (
    <>
      <ProDescriptions
        title='生产方基本信息'
        bordered
        column={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 2, xxl: 2 }}
        style={{ marginTop }}
        labelStyle={{ width: 200 }}
      >
        <ProDescriptions.Item label='应用 ID'>
          {producer?.app_id}
        </ProDescriptions.Item>
        <ProDescriptions.Item label='应用名称'>
          {producer?.app_name}
        </ProDescriptions.Item>
        <ProDescriptions.Item label='应用中文名'>
          {producer?.app_name_ch}
        </ProDescriptions.Item>
        <ProDescriptions.Item label='应用密钥' copyable>
          {hasAUth ? producer?.app_key : '******'}
        </ProDescriptions.Item>
        <ProDescriptions.Item label='测试应用密钥' copyable>
          {hasAUth ? producer?.test_app_key : '******'}
        </ProDescriptions.Item>
        <ProDescriptions.Item label='应用状态' valueEnum={APP_STATUS_MAP} valueType='select'>
          {producer?.status}
        </ProDescriptions.Item>
        <ProDescriptions.Item label='负责人'>
          {producer?.owner}
        </ProDescriptions.Item>
        <ProDescriptions.Item label='描述' span={24}>
          {producer?.description}
        </ProDescriptions.Item>
      </ProDescriptions>
      <ProDescriptions
        title='字段信息'
        bordered
        style={{ marginTop: 16 }}
        column={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 2, xxl: 2 }}
        labelStyle={{ width: 200 }}
      >
        <ProDescriptions.Item label='字段分类' valueEnum={FIELD_CLASS_MAP} valueType='select'>
          {producer?.field_class}
        </ProDescriptions.Item>
        <ProDescriptions.Item label='对接方式' valueEnum={SUBSCRIBE_SERVICE_TYPE_MAP} valueType='select'>
          {producer?.invoke_type}
        </ProDescriptions.Item>
        {producer?.invoke_type === InvokeType.INVOKE_TYPE_KAFKA && (
          <>
            <ProDescriptions.Item label='正式KAFKA地址'>
              {producer?.kafka_addr}
            </ProDescriptions.Item>
            <ProDescriptions.Item label='正式KAFKA主题'>
              {producer?.kafka_topic}
            </ProDescriptions.Item>
            <ProDescriptions.Item label='测试KAFKA地址'>
              {producer?.test_kafka_addr}
            </ProDescriptions.Item>
            <ProDescriptions.Item label='测试KAFKA主题'>
              {producer?.test_kafka_topic}
            </ProDescriptions.Item>
          </>
        )}
        {producer?.invoke_type === InvokeType.INVOKE_TYPE_API && (
          <ProDescriptions.Item label='API名称'>
            {producer?.api_name}
          </ProDescriptions.Item>
        )}
        <ProDescriptions.Item label='生产方服务名'>
          {producer?.producer_service}
        </ProDescriptions.Item>
        <ProDescriptions.Item label='限流频次(QPS)'>
          {producer?.rate_limit}
        </ProDescriptions.Item>
        <ProDescriptions.Item label='总库接入侧服务名'>
          {producer?.receiver_service}
        </ProDescriptions.Item>
        <ProDescriptions.Item label='是否生产所有字段'>
          {producer?.is_all_fields ? '是' : '否'}
        </ProDescriptions.Item>
      </ProDescriptions>
    </>
  );
}
