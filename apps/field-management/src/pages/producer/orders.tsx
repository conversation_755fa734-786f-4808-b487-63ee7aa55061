import { useMemo } from 'react';
import { Button, Space } from 'antd';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-components';
import { fetchProducerOrders } from '@/services/producer';
import AllocResourceModal from '@/components/alloc-resource';
import ViewResourceModal from '@/components/alloc-resource/view';
import type { OrderDetail } from '@/types/api';
import { useOrderList } from '@/hooks/useOrderList';
import { PRODUCER_ORDER_TYPE_OPTIONS } from '@/constants/order';

export default function OrderPage() {
  const {
    handleAllocResource,
    handleFlowDetail,
    handleOrderDetial,
    handleViewTestResource,
    handleViewProdResource,
    canAllocResource,
    canViewTestResource,
    canViewProdResource,
    columns,
  } = useOrderList('producer');

  const orderColumns = useMemo<ProColumns<OrderDetail>[]>(() => [
    ...columns.map((column) => {
      if (column.dataIndex === 'type') {
        return {
          ...column,
          fieldProps: {
            options: PRODUCER_ORDER_TYPE_OPTIONS,
          },
        };
      }
      return column;
    }),
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      fixed: 'right',
      width: 200,
      render: (_, record) => (
        <Space wrap>
          <Button
            style={{ margin: 0, padding: 0 }}
            type="link"
            onClick={() => handleFlowDetail(record)}
          >
            审批进度
          </Button>
          <Button
            style={{ margin: 0, padding: 0 }}
            type="link"
            onClick={() => handleOrderDetial(record)}
          >
            详情
          </Button>
          <Button
            style={{ margin: 0, padding: 0 }}
            type="link"
            disabled={!canAllocResource(record)}
            onClick={() => handleAllocResource(record)}
          >
            资源分配
          </Button>
        </Space>
      ),
    },
  ], [
    columns,
    handleAllocResource,
    handleFlowDetail,
    handleOrderDetial,
    canAllocResource,
    handleViewTestResource,
    handleViewProdResource,
    canViewTestResource,
    canViewProdResource,
  ]);

  return (
    <PageContainer
      title={false}
    >
      <ProTable<OrderDetail>
        columns={orderColumns}
        request={fetchProducerOrders}
        rowKey="order_id"
        search={{
          defaultCollapsed: false,
        }}
        pagination={{
          showSizeChanger: true,
        }}
        scroll={{
          x: 'max-content',
          y: 800,
        }}
      />
      <AllocResourceModal mode="producer" />
      <ViewResourceModal mode="producer" />
    </PageContainer>
  );
}
