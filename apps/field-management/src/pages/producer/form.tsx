import { useRef, useState } from 'react';
import { use<PERSON>ara<PERSON>, useLocation, useNavigate } from 'react-router-dom';
import { App } from 'antd';
import { useMemoizedFn, useCreation, useAsyncEffect } from 'ahooks';
import {
  PageContainer,
  ProCard,
  ProForm,
  ProFormText,
  ProFormTextArea,
  ProFormSelect,
  ProFormRadio,
  ProFormGroup,
  ProFormDependency,
  ProFormDigit,
  ProFormList,
  ProFormSwitch,
} from '@ant-design/pro-components';
import type { ProFormInstance } from '@ant-design/pro-components';
import { useDraft } from '@/hooks/useDraft';
import FieldSelect from '@/components/field-select';
import AddField from '@/components/field-select/add-field';
import { getUsers } from '@/services/user';
import {
  createProducer,
  updateProducer,
  fetchProducerDetail,
} from '@/services/producer';
import { fetchEntityTypes } from '@/services/field';
import { FIELD_CLASS_OPTIONS } from '@/constants/field';
import { SUBSCRIBE_SERVICE_TYPE_OPTIONS } from '@/constants/consumer';
import { httpUrlValidationRule, appNameValidationRule, noSpaceValidationRule } from '@/constants/rules';
import { useAccess } from '@/hooks/useAccess';
import { InvokeType, CreateProducerRequest, FieldClass, FieldSet } from '@/types/api';

const proCardStyle = {
  marginBlockEnd: 16,
  width: '100%',
};

export default function ProducerPage() {
  const formRef = useRef<ProFormInstance<CreateProducerRequest>>(null);
  const { id } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const { isAdmin, userInfo } = useAccess();
  const isEdit = location.pathname.includes('edit') && Boolean(id);
  const { message } = App.useApp();
  const [isRestoringFromDraft, setIsRestoringFromDraft] = useState(false);
  const isAdminRole = isAdmin();

  const initialValues = useCreation<CreateProducerRequest>(
    () => ({
      application: {
        owner: userInfo.name,
      },
      order: {
        applicant: userInfo.name,
      },
      producer: {
        field_class: FieldClass.FIELD_CLASS_STATIC,
        invoke_type: InvokeType.INVOKE_TYPE_KAFKA,
        field_sets: [],
        add_fields: [],
        // @ts-ignore
        is_all_fields: false,
      },
    }),
    [userInfo],
  );

  useAsyncEffect(async () => {
    if (isEdit) {
      const res = await fetchProducerDetail({ app_name: id });
      if (!res) {
        message.error('获取生产方信息失败');
        return;
      }
      const { formattedProducer, producer } = res;
      const owners = producer?.owner?.split(';') || [];
      const hasAuth = isAdmin() || owners.includes(userInfo.name);
      if (!hasAuth) {
        message.error('您没有权限编辑该生产方信息');
        setTimeout(() => {
          navigate('/403');
        }, 1000);
        return;
      }
      setIsRestoringFromDraft(true);
      formRef.current?.setFieldsValue({
        application: {
          app_name: producer.app_name,
          app_name_ch: producer.app_name_ch,
          owner: producer.owner,
          description: producer.description,
        },
        producer: formattedProducer,
        order: initialValues.order,
      });
      setTimeout(() => {
        setIsRestoringFromDraft(false);
      }, 0);
    } else {
      formRef.current?.setFieldsValue(initialValues);
    }
  }, [isEdit, id, initialValues, userInfo.name]);

  const handleLoadDraft = useMemoizedFn((draft: CreateProducerRequest) => {
    setIsRestoringFromDraft(true);
    formRef.current?.setFieldsValue(draft);
    setTimeout(() => {
      setIsRestoringFromDraft(false);
    }, 0);
  });

  const { saveDraft } = useDraft<CreateProducerRequest>({
    key: 'producer_create_draft',
    disabled: isEdit,
    onLoadDraft: handleLoadDraft,
  });

  const handleFinish = useMemoizedFn(async (values: CreateProducerRequest) => {
    const fieldSets = values?.producer?.field_sets;
    // 判断每个fieldSets下至少存在add_fields或fields
    const hasAddFields = fieldSets?.every(fieldSet => fieldSet?.add_fields?.length || fieldSet?.fields?.length);
    if (!hasAddFields) {
      message.error('请至少配置一个生产字段集合或新增字段');
      return;
    }
    // 判断相同介质类型下，add_fields和fields不能存在重复字段
    const hasDuplicateFields = fieldSets?.some((fieldSet) => {
      const addFields = fieldSet?.add_fields;
      const fields = fieldSet?.fields;
      return addFields?.some(addField => fields?.includes(addField?.field_path || ''));
    });
    if (hasDuplicateFields) {
      message.error('新增字段和生产字段不能存在重复字段');
      return;
    }
    const submitFunc = isEdit ? updateProducer : createProducer;
    const res = await submitFunc(values);
    if (res.success) {
      message.success(`${isEdit ? '更新' : '创建'}生产方成功, 即将跳转至工单列表`);
      setTimeout(() => {
        navigate('/producer/my-orders');
      }, 1000);
    } else {
      message.error(res.message);
    }
  });

  const handleFieldClassChange = useMemoizedFn(() => {
    formRef.current?.setFieldValue(['producer', 'field_sets'], []);
  });

  const handleInvokeTypeChange = useMemoizedFn(() => {
    formRef.current?.setFieldValue(['producer', 'field_sets'], []);
  });

  const handleValuesChange = useMemoizedFn((_, values: CreateProducerRequest) => {
    if (!isEdit) {
      saveDraft(values);
    }
  });

  const getEntityTypeOptions = useMemoizedFn(async (params: { field_class: FieldClass, keyWords?: string }) => {
    const fieldSets = formRef.current?.getFieldValue(['producer', 'field_sets']);
    const res = await fetchEntityTypes({
      field_class: params.field_class,
      keyWords: params.keyWords,
    });
    return res?.filter(item => !fieldSets?.some((fieldSet: FieldSet) => fieldSet.entity_type === item.value));
  });

  return (
    <PageContainer title={isEdit ? '编辑生产方' : '创建生产方'}>
      <ProForm<CreateProducerRequest>
        formRef={formRef}
        initialValues={initialValues}
        onFinish={handleFinish}
        onValuesChange={handleValuesChange}
        layout="horizontal"
        labelWrap
      >
        <ProCard
          title="工单信息"
          bordered
          headerBordered
          collapsible
          style={proCardStyle}
        >
          <ProFormText name={['order', 'applicant']} label="申请人" hidden />
          <ProFormText
            name={['order', 'tapd']}
            label="TAPD 地址"
            rules={[
              { required: true, message: '请输入 TAPD 地址' },
              httpUrlValidationRule,
            ]}
          />
          <ProFormTextArea
            name={['order', 'description']}
            label="工单描述"
            rules={[{ required: true, message: '请输入工单描述' }]}
          />
        </ProCard>
        <ProCard
          title="生产方信息"
          bordered
          headerBordered
          collapsible
          style={proCardStyle}
        >
          <ProFormGroup>
            <ProFormText
              name={['application', 'app_name']}
              disabled={isEdit}
              label="应用名称"
              rules={[
                { required: true, message: '请输入应用名称' },
                appNameValidationRule,
              ]}
            />
            <ProFormText
              name={['application', 'app_name_ch']}
              label="应用中文名"
              rules={[
                { required: true, message: '请输入应用中文名' },
                noSpaceValidationRule,
              ]}
            />
            <ProFormSelect
              name={['application', 'owner']}
              label="负责人"
              request={getUsers}
              showSearch
              width={200}
              mode='multiple'
              convertValue={(value: string) => {
                if (typeof value === 'string') {
                  return value?.split(';');
                }
                return value;
              }}
              transform={(value: string[] | string) => {
                if (Array.isArray(value)) {
                  return value?.join(';');
                }
                return value;
              }}
              rules={[{ required: true, message: '请选择负责人' }]}
            />
          </ProFormGroup>
          <ProFormTextArea
            name={['application', 'description']}
            label="应用描述"
            rules={[{ required: true, message: '请输入应用描述' }]}
          />
        </ProCard>
        <ProCard
          title="字段信息配置"
          bordered
          headerBordered
          collapsible
          style={proCardStyle}
        >
          <ProFormRadio.Group
            name={['producer', 'field_class']}
            label="字段分类"
            options={FIELD_CLASS_OPTIONS}
            placeholder="请选择字段分类"
            disabled={isEdit}
            rules={[{ required: true, message: '请选择字段分类' }]}
            fieldProps={{
              onChange: handleFieldClassChange,
            }}
          />
          <ProFormRadio.Group
            name={['producer', 'invoke_type']}
            label="对接方式"
            rules={[{ required: true, message: '请选择对接方式' }]}
            options={SUBSCRIBE_SERVICE_TYPE_OPTIONS}
            disabled={isEdit}
            fieldProps={{
              onChange: handleInvokeTypeChange,
            }}
          />
          <ProFormDependency name={['producer', 'invoke_type']}>
            {({ producer }) => {
              if (producer?.invoke_type === InvokeType.INVOKE_TYPE_API) {
                return (
                  <>
                    <ProFormText
                      name={['producer', 'api', 'caller']}
                      label="主调方"
                      rules={[
                        { required: true, message: '请输入主调方' },
                        noSpaceValidationRule,
                      ]}
                    />
                    <ProFormDigit
                      name={['producer', 'rate_limit']}
                      label="限流频次(QPS)"
                      min={0}
                      rules={[{ required: true, message: '请输入限流频次' }]}
                    />
                  </>
                );
              }
              return null;
            }}
          </ProFormDependency>
          <ProFormList
            name={['producer', 'field_sets']}
            label="生产字段集合"
            rules={[
              {
                validator: async (_, value) => {
                  if (!value?.length) {
                    return Promise.reject(new Error('请至少配置一个生产字段集合'));
                  }
                },
              },
              {
                validator: async (_, value) => {
                  const entityTypes = value.map((item: any) => item.entity_type);
                  if (new Set(entityTypes).size < entityTypes.length) {
                    return Promise.reject(new Error('介质类型不能重复'));
                  }
                },
              },
            ]}
            copyIconProps={false}
            itemRender={({ listDom, action }, { index }) => (
              <ProCard
                boxShadow
                collapsible
                style={{ marginBlockEnd: 8 }}
                title={`生产字段集合${index + 1}（介质类型不能重复）`}
                extra={action}
                bodyStyle={{ paddingBlockEnd: 0 }}
              >
                {listDom}
              </ProCard>
            )}
          >
            <ProFormDependency name={['producer', 'field_class']} ignoreFormListField>
              {({ producer }) => (
                <>
                  <ProFormSelect
                    name={['entity_type']}
                    label="介质类型"
                    placeholder="请选择介质类型"
                    rules={[{ required: true, message: '请选择介质类型' }]}
                    request={getEntityTypeOptions}
                    showSearch
                    params={{
                      field_class: producer.field_class,
                    }}
                  />
                  <ProFormDependency name={['entity_type']}>
                    {({ entity_type }) => (
                      <>
                        <ProForm.Item
                          name={['fields']}
                          label="字段列表"
                        >
                          <FieldSelect
                            fieldClass={producer.field_class}
                            entityType={entity_type}
                            skipClearOnParamsChange={isRestoringFromDraft}
                          />
                        </ProForm.Item>
                        <ProForm.Item
                          name={['add_fields']}
                          label="新增字段"
                        >
                          <AddField
                            fieldClass={producer.field_class}
                            entityType={entity_type}
                            skipClearOnParamsChange={isRestoringFromDraft}
                          />
                        </ProForm.Item>
                      </>
                    )}
                  </ProFormDependency>
                </>
              )}
            </ProFormDependency>
          </ProFormList>
          <ProFormSwitch
            name={['producer', 'is_all_fields']}
            label="是否生产所有字段"
            disabled={!isAdminRole}
            transform={(value: boolean) => Number(value || false)}
            convertValue={(value: number) => Boolean(value)}
          />
        </ProCard>
      </ProForm>
    </PageContainer>
  );
}
