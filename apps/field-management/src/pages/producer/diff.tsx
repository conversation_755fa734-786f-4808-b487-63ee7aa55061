import { useParams } from 'react-router-dom';
import { useRequest, useMemoizedFn } from 'ahooks';
import { Row, Col, Typography } from 'antd';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { fetchProducerDetail, fetchProducerOrderDetail } from '@/services/producer';
import FieldView from '@/components/field-select/view';
import { parseProducerFields } from '@/utils';
import OrderInfo from '@/components/order-detail';
import { FieldSet } from '@/types/api';
import ProducerDetail from './producer-detail';
import FieldSets from './field-sets';

export default function ProducerDiffPage() {
  const { id } = useParams();

  const getDiffProducer = useMemoizedFn(async () => {
    const orderRes = await fetchProducerOrderDetail({ order_id: Number(id) });
    const { producer: newProducer, fieldDetailSets, order } = orderRes || {};
    const { app_name: appName } = newProducer || {};
    const oldProducerRes = await fetchProducerDetail({ app_name: appName });
    const { producer: oldProducer } = oldProducerRes || {};
    return {
      newProducer,
      oldProducer,
      fieldDetailSets,
      order,
    };
  });

  const { data, loading } = useRequest(getDiffProducer, {
    refreshDeps: [id],
  });

  const { newProducer, oldProducer, fieldDetailSets, order } = data || {};

  // 解析字段集合
  const fieldSets: FieldSet[] = parseProducerFields(oldProducer?.produce_fields);

  return (
    <PageContainer title={false} loading={loading}>
      <OrderInfo order={order} />
      <Typography.Title level={5} style={{ marginTop: 16 }}>生产方对比</Typography.Title>
      <Row style={{ marginTop: 16 }} gutter={16}>
        <Col span={12}>
          <ProCard title="当前生效的生产方详情" bordered headerBordered direction='column'>
            <ProducerDetail producer={oldProducer} />
            <Typography.Title level={5} style={{ marginTop: 16 }}>生产字段集合</Typography.Title>
            {fieldSets.map((fieldSet: FieldSet, index: number) => (
              <ProCard
                key={index}
                title={`字段集合 - ${fieldSet.entity_type}`}
                bordered
                headerBordered
                style={{ marginTop: 16 }}
              >
                <FieldView
                  fieldNames={fieldSet.fields}
                  entityType={fieldSet.entity_type}
                  fieldClass={oldProducer?.field_class}
                />
              </ProCard>
            ))}
          </ProCard>
        </Col>
        <Col span={12}>
          <ProCard title="本次更新的生产方详情" bordered headerBordered direction='column'>
            <ProducerDetail producer={newProducer} />
            <Typography.Title level={5} style={{ marginTop: 16 }}>生产字段集合</Typography.Title>
            <FieldSets fieldDetailSets={fieldDetailSets} producer={newProducer} />
          </ProCard>
        </Col>
      </Row>
    </PageContainer>
  );
}
