import { useParams } from 'react-router-dom';
import { useRequest } from 'ahooks';
import { Typography } from 'antd';
import { PageContainer } from '@ant-design/pro-components';
import { fetchProducerOrderDetail } from '@/services/producer';
import OrderInfo from '@/components/order-detail';
import ProducerDetail from './producer-detail';
import FieldSets from './field-sets';

export default function ProducerOrderDetailPage() {
  const { id } = useParams();

  const { data, loading } = useRequest(fetchProducerOrderDetail, {
    defaultParams: [{ order_id: Number(id) }],
  });

  const { order, producer, fieldDetailSets } = data || {};

  return (
    <PageContainer title={false} loading={loading}>
      <OrderInfo order={order} />
      <ProducerDetail producer={producer} marginTop={16} />
      <Typography.Title level={5} style={{ marginTop: 16 }}>生产字段集合</Typography.Title>
      <FieldSets fieldDetailSets={fieldDetailSets} producer={producer} />
    </PageContainer>
  );
}
