import { Button, Result } from 'antd';
import { useNavigate } from 'react-router-dom';

export default function NotFoundPage() {
  const navigate = useNavigate();

  const handleClick = () => {
    void navigate('/');
  };

  return (
    <Result
      status="500"
      title="500"
      subTitle="抱歉，服务器发生错误"
      extra={(
        <Button type="primary" onClick={handleClick}>
          返回首页
        </Button>
      )}
    />
  );
}
