import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from 'antd';
import { PageContainer, CheckCard } from '@ant-design/pro-components';
import type { CheckCardGroupProps } from '@ant-design/pro-components';

export default function ProducerPage() {
  const [selected, setSelected] = useState<CheckCardGroupProps['value']>(0);
  const navigate = useNavigate();

  const handleConfirm = useCallback(() => {
    if (selected === 0) {
      navigate('/producer/create');
    } else {
      navigate('/consumer/create');
    }
  }, [selected]);
  const handleChange = useCallback((value: CheckCardGroupProps['value']) => {
    setSelected(value);
  }, []);
  return (
    <PageContainer
      title="欢迎来到总库字段管理系统"
      content="请选择您要接入的角色"
    >
      <CheckCard.Group
        value={selected}
        onChange={handleChange}
        defaultValue="A"
      >
        <CheckCard size='large' title="生产方接入" description="字段生产方接入" value={0} />
        <CheckCard size='large' title="消费方接入" description="字段消费方接入" value={1} />
      </CheckCard.Group>
      <div style={{ marginTop: 24 }}>
        <Button type="primary" onClick={handleConfirm}>
          确认
        </Button>
      </div>
    </PageContainer>
  );
}
