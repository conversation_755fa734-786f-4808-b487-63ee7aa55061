import {
  CreateConsumerRequest,
  ConsumerUpdateData,
  SubscribeFields,
  TransformFieldSet,
  Trigger,
  Consumer,
} from './api';

export interface FormatTransformFieldSet extends Omit<TransformFieldSet, 'fields' | 'dependent_fields'> {
  trigger_type?: Trigger['trigger_type'];
  expressions?: Trigger['expressions'];
  fieldsInfo?: {
    fields: TransformFieldSet['fields'];
    dependent_fields: TransformFieldSet['dependent_fields'];
  };
}
export interface FormatSubscribeFields extends Omit<SubscribeFields, 'field_sets'> {
  field_sets: FormatTransformFieldSet[];
}

export interface FormatComsumer extends Omit<ConsumerUpdateData, 'subscribe_fields'> {
  subscribe_fields: FormatSubscribeFields;
}

export interface FormatConsumerRequest extends Omit<CreateConsumerRequest, 'consumer'> {
  consumer: FormatComsumer;
}

export interface FormatDetailFieldSet extends TransformFieldSet {
  trigger_type?: Trigger['trigger_type'];
  expressions?: Trigger['expressions'];
}

export interface FormatDetailSubscribeFields extends Omit<SubscribeFields, 'field_sets'> {
  field_sets: FormatDetailFieldSet[];
}

export interface FormatDetailConsumer extends Omit<Consumer, 'subscribe_fields'> {
  subscribe_fields: FormatDetailSubscribeFields;
}
