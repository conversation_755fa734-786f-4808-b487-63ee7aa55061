import { Key } from 'react';

export interface Pagination {
  /** 页码 */
  current: number;
  /** 总条数 */
  total_num: number;
  /** 总页数 */
  total_page: number;
  /** 每页大小 */
  page_size: number;
}

export interface Sort {
  field: Key | Key[],
  order: string | null
}

export interface TablePage {
  pageSize?: number;
  current?: number;
}

export interface OptionItem {
  label: string;
  value: string | number;
}

export interface ProducerFields {
  [key: string]: string[];
}

export type BusinessMode = 'consumer' | 'producer';

export type OrderBusinessMode = BusinessMode | 'field';
