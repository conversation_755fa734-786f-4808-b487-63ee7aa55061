export enum FieldClass {
  /** 未知分类 */
  FIELD_CLASS_UNKNOWN = 0,
  /** 动态字段 */
  FIELD_CLASS_DYNAMIC = 1,
  /** 静态字段 */
  FIELD_CLASS_STATIC = 2,
}

export enum InvokeType {
  /** 未知对接方式 */
  INVOKE_TYPE_UNKNOWN = 0,
  /** Kafka 对接 */
  INVOKE_TYPE_KAFKA = 1,
  /** API 对接 */
  INVOKE_TYPE_API = 2,
}

export enum FieldStatus {
  /** 未知状态 */
  FIELD_STATUS_UNKNOWN = 0,
  /** 下线状态 */
  FIELD_STATUS_OFFLINE = -1,
  /** 在线状态 */
  FIELD_STATUS_ONLINE = 1,
  /** 不可消费状态 */
  FIELD_STATUS_UNCONSUMABLE = 2,
}

export enum OrderStatus {
  /** 未知状态，主要用于检索时的查询条件 */
  ORDER_STATUS_UNKNOWN = 0,
  /** 通过 */
  ORDER_STATUS_APPROVE = 1,
  /** 拒绝 */
  ORDER_STATUS_REFUSAL = -1,
  /** 审核中 */
  ORDER_STATUS_REVIEW = 2,
}

export enum OrderStep {
  /** 准备中... */
  ORDER_STEP_READY = 0,
  /** 最终状态 */
  ORDER_STEP_FINAL = 9999,
  /** 字段负责人审核 */
  FIELD_UPDATE_STEP_FIELD_OWNER_AUDIT = 1101,
  /** 总库初审 */
  FIELD_UPDATE_STEP_DATABUS_OWNER_AUDIT = 1102,
  /** 工单发起方联调测试确认 */
  FIELD_UPDATE_STEP_DEBUG_CONFIRM = 1103,
  /** 总库终审 */
  FIELD_UPDATE_STEP_DATABUS_OWNER_FINAL_AUDIT = 1104,
  /** 介质负责人审核 */
  FIELD_OFFLINE_STEP_FIELD_OWNER_AUDIT = 1201,
  /** 总库审核 */
  FIELD_OFFLINE_STEP_DATABUS_OWNER_AUDIT = 1202,
  /** 观察业务反馈确认 */
  FIELD_OFFLINE_STEP_FEEDBACK_CONFIRM = 1203,
  /** 生产方上级审核 */
  PRODUCER_CREATE_STEP_PRODUCER_LEADER_AUDIT = 1301,
  /** 介质负责人审批 */
  PRODUCER_CREATE_STEP_ENTITY_OWNER_AUDIT = 1302,
  /** 总库初审 */
  PRODUCER_CREATE_STEP_DATABUS_OWNER_AUDIT = 1303,
  /** 分配测试资源 */
  PRODUCER_CREATE_STEP_ALLOC_TEST_RESOURCE = 1304,
  /** 生产方联调测试 */
  PRODUCER_CREATE_STEP_DEBUG_CONFIRM = 1305,
  /** 总库终审 */
  PRODUCER_CREATE_STEP_DATABUS_OWNER_FINAL_AUDIT = 1306,
  /** 分配正式资源 */
  PRODUCER_CREATE_STEP_ALLOC_PROD_RESOURCE = 1307,
  /** 生产方上级审核 */
  PRODUCER_UPDATE_STEP_PRODUCER_LEADER_AUDIT = 1401,
  /** 介质负责人审批 */
  PRODUCER_UPDATE_STEP_ENTITY_OWNER_AUDIT = 1402,
  /** 总库初审 */
  PRODUCER_UPDATE_STEP_DATABUS_OWNER_AUDIT = 1403,
  /** 分配测试资源 */
  PRODUCER_UPDATE_STEP_ALLOC_TEST_RESOURCE = 1404,
  /** 生产方联调测试 */
  PRODUCER_UPDATE_STEP_DEBUG_CONFIRM = 1405,
  /** 总库终审 */
  PRODUCER_UPDATE_STEP_DATABUS_OWNER_FINAL_AUDIT = 1406,
  /** 分配正式资源 */
  PRODUCER_UPDATE_STEP_ALLOC_PROD_RESOURCE = 1407,
  /** 生产方上级审核 */
  PRODUCER_OFFLINE_STEP_PRODUCER_LEADER_AUDIT = 1501,
  /** 总库审核 */
  PRODUCER_OFFLINE_STEP_DATABUS_OWNER_AUDIT = 1502,
  /** 消费方上级审核 */
  CONSUMER_CREATE_STEP_CONSUMER_LEADER_AUDIT = 1601,
  /** 总库审核 */
  CONSUMER_CREATE_STEP_DATABUS_OWNER_AUDIT = 1602,
  /** 分配测试资源 */
  CONSUMER_CREATE_STEP_ALLOC_TEST_RESOURCE = 1603,
  /** 联调成功确认 */
  CONSUMER_CREATE_STEP_DEBUG_CONFIRM = 1604,
  /** 分配测试资源 */
  CONSUMER_CREATE_STEP_ALLOC_PROD_RESOURCE = 1605,
  /** 消费方上级审核 */
  CONSUMER_UPDATE_STEP_CONSUMER_LEADER_AUDIT = 1701,
  /** 总库审核 */
  CONSUMER_UPDATE_STEP_DATABUS_OWNER_AUDIT = 1702,
  /** 分配测试资源 */
  CONSUMER_UPDATE_STEP_ALLOC_TEST_RESOURCE = 1703,
  /** 联调成功确认 */
  CONSUMER_UPDATE_STEP_DEBUG_CONFIRM = 1704,
  /** 分配测试资源 */
  CONSUMER_UPDATE_STEP_ALLOC_PROD_RESOURCE = 1705,
  /** 消费方上级审核 */
  CONSUMER_OFFLINE_STEP_CONSUMER_LEADER_AUDIT = 1801,
  /** 总库审核 */
  CONSUMER_OFFLINE_STEP_DATABUS_OWNER_AUDIT = 1802,
  /** 观察业务反馈确认 */
  CONSUMER_OFFLINE_STEP_FEEDBACK_CONFIRM = 1803,
}

export interface FieldDetail {
  /** 字段分类（取值：动态字段、静态字段） */
  field_class?: FieldClass;
  /** 介质类型 */
  entity_type?: string;
  /** 列簇名称 */
  column_family?: string;
  /** 原字段路径 */
  src_field_path?: string;
  /** 字段路径 */
  field_path?: string;
  /** 字段名称 */
  field_name?: string;
  /** 字段类型 */
  value_type?: string;
  /** 校验规则 */
  validation?: string;
  /** 字段描述 */
  description?: string;
  /** 字段示例 */
  example?: string;
  /** 生产逻辑 */
  produce_logic?: string;
  /** 字段状态(1：在线状态; -1:下线状态; 2：不可消费状态) */
  status?: FieldStatus;
  /** 负责人 */
  owner?: string;
  /** TAPD链接 */
  tapd?: string;
  /** 拦截规则: -1-不拦截; 0-拦截校验失败的字段; 1-拦截一级字段; 2-拦截二级字段 */
  block_rule?: number;
}

export interface FieldSet {
  /** 字段分类 */
  field_class?: FieldClass;
  /** 实体类型: article/video 等 */
  entity_type?: string;
  /** 字段列表 */
  fields?: string[];
}

export interface AppUpdateData {
  /** 应用名称[注：用户输入][规则：三段式，小写字段加下划线。如：aaa_bbb_ccc] */
  app_name?: string;
  /** 应用中文名称[注：用户输入] */
  app_name_ch?: string;
  /** 负责人[注：用户输入] */
  owner?: string;
  /** 说明[注：用户输入] */
  description?: string;
}

export enum AppStatus {
  /** 未知状态 */
  APP_STATUS_UNKNOWN = 0,
  /** 下线状态 */
  APP_STATUS_OFFLINE = -1,
  /** 在线状态 */
  APP_STATUS_ONLINE = 1,
}

export interface AppSummary {
  /** 应用 ID，申请通过后分配 */
  app_id?: number;
  /** 应用名称 */
  app_name?: string;
  /** 应用中文名称 */
  app_name_ch?: string;
  /** 负责人 */
  owner?: string;
  /** 说明 */
  description?: string;
  /** 应用密钥，后台生成 */
  app_key?: string;
  /** 应用状态 */
  status?: AppStatus;
  /** 字段类型 */
  field_class?: FieldClass;
  /** 对接方式 */
  invoke_type?: InvokeType;
}

export interface AuditNotifyRequest {
  /** 工单 ID */
  order_id?: number;
  /** 工单类型 + 审核步骤拼出的 ID */
  step_id?: number;
  /** 下一步的step_id */
  next_step_id?: number;
  /** 审核状态 */
  status?: OrderStatus;
  /** 审核意见[注：审核人员输入] */
  comment?: string;
}

export interface AuditNotifyReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
}

export enum OrderType {
  /** 未知工单类型 */
  ORDER_TYPE_UNKNOWN = 0,
  /** 静态字段更新 */
  ORDER_TYPE_STATIC_FIELD_UPDATE = 101,
  /** 静态字段下线 */
  ORDER_TYPE_STATIC_FIELD_OFFLINE = 102,
  /** 动态字段更新 */
  ORDER_TYPE_DYNAMIC_FIELD_UPDATE = 201,
  /** 动态字段下线 */
  ORDER_TYPE_DYNAMIC_FIELD_OFFLINE = 202,
  /** 生产者创建 */
  ORDER_TYPE_PRODUCER_CREATE = 301,
  /** 生产者更新 */
  ORDER_TYPE_PRODUCER_UPDATE = 302,
  /** 生产者下线 */
  ORDER_TYPE_PRODUCER_OFFLINE = 303,
  /** 消费方创建 */
  ORDER_TYPE_CONSUMER_CREATE = 401,
  /** 消费方更新 */
  ORDER_TYPE_CONSUMER_UPDATE = 402,
  /** 消费方下线 */
  ORDER_TYPE_CONSUMER_OFFLINE = 403,
}

export interface OrderUpdateData {
  /** 申请人[注：由前端采集] */
  applicant?: string;
  /** 工单描述[注：用户输入] */
  description?: string;
  /** TAPD 信息[注：用户输入] */
  tapd?: string;
}

export interface OrderDetail {
  /** 工单 ID[注：提交后由后台生成] */
  order_id?: number;
  /** 工单类型[注：由前端生成] */
  type?: OrderType;
  /** 工单步骤[注：由后端生成] */
  step?: OrderStep;
  /** 申请人[注：由前端采集] */
  applicant?: string;
  /** 审批流ID[注：由流云系统生成] */
  workflow_id?: number;
  /** 工单描述[注：用户输入] */
  description?: string;
  /** 工单审核意见[注：用户输入] */
  comment?: string;
  /** TAPD 信息[注：用户输入] */
  tapd?: string;
  /** 工单状态[注：由后端生成] */
  status?: OrderStatus;
  /** 生产方、消费方、字段的名字 */
  app_name?: string;
  /** 创建时间 */
  create_time?: string;
  /** 更新时间 */
  update_time?: string;
}

export interface GetOrdersRequest {
  /** 工单id */
  order_id?: number;
  /** 页码[注：用户输入] */
  page_num?: number;
  /** 每页条数[注：用户输入] */
  page_size?: number;
  /** 工单类型[注：用户输入] */
  type?: OrderType;
  /** 申请人[注：用户输入] */
  applicant?: string;
  /** 工单状态[注：用户输入][状态：进行中、驳回、通过] */
  status?: OrderStatus;
}

export interface GetOrdersReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 总数目 */
  total?: number;
  /** 查询页对应的order列表 */
  orders?: OrderDetail[];
}

export interface GetMyConsumersRequest {
  /** 页码 */
  page_num?: number;
  /** 每页条数 */
  page_size?: number;
  /** 负责人（My企微名） */
  owner?: string;
}

export interface GetMyConsumersReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 总条目数 */
  total?: number;
  /** 应用列表 */
  apps?: AppSummary[];
}

export interface GetMyProducersRequest {
  /** 页码 */
  page_num?: number;
  /** 每页条数 */
  page_size?: number;
  /** 负责人（My企微名） */
  owner?: string;
}

export interface GetMyProducersReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 总条目数 */
  total?: number;
  /** 应用列表 */
  apps?: AppSummary[];
}

export interface GetMyOrdersRequest {
  /** 页码 */
  page_num?: number;
  /** 每页条数 */
  page_size?: number;
  /** 申请人（My企微名） */
  applicant?: string;
}

export interface GetMyOrdersReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 总条目数 */
  total?: number;
  /** 工单列表 */
  orders?: OrderDetail[];
}

export enum ResourceType {
  RESOURCE_TYPE_UNKNOWN = 0,
  RESOURCE_TYPE_TEST = 1,
  RESOURCE_TYPE_PRODUCTION = 2,
}

export interface ProducerAllocResourceRequestAPI {
  /** API 名称 */
  api_name?: string[];
}

export interface ProducerAllocResourceRequestKafka {
  /** Kafka 域名 */
  address?: string;
  /** Kafka 主题 */
  topic?: string;
}

export interface ProducerAllocResourceRequest {
  /** 工单id */
  order_id?: number;
  /** API资源信息 */
  api?: ProducerAllocResourceRequestAPI;
  /** KAFKA资源 */
  kafka?: ProducerAllocResourceRequestKafka;
  /** 分配资源类型，1 测试资源、2 正式资源 */
  resource_type?: ResourceType;
}

export interface ConsumerAllocResourceRequestAPI {
  /** 是否启用 AppKey 鉴权方式 */
  check_app_key?: boolean;
  /** AppKey */
  app_key?: string;
  /** 是否启用主调方名鉴权方式 */
  check_caller?: boolean;
  /** 服务名称 */
  service_name?: string;
  /** API 名称 */
  api_name?: string;
}

export interface ConsumerAllocResourceRequest {
  /** 工单id */
  order_id?: number;
  /** API资源信息 */
  api?: ConsumerAllocResourceRequestAPI;
  /** KAFKA资源 */
  kafka?: Kafka;
  resource_type?: ResourceType;
}

export interface AllocResourceReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
}

export enum BlockRule {
  /** 0-拦截校验失败的字段 */
  BLOCK_RULE_BLOCK_ERR = 0,
  /** -1-不拦截 */
  BLOCK_RULE_NO = -1,
  /** 1-拦截一级字段 */
  BLOCK_RULE_BLOCK_FIRST = 1,
  /** 2-拦截二级字段 */
  BLOCK_RULE_BLOCK_SECOND = 2,
}

export interface FieldSchemaUpdateData {
  /** 介质类型 */
  entity_type?: string;
  /** 列簇名称 */
  column_family?: string;
  /** 字段路径 */
  field_path?: string;
  /** 原字段路径 */
  src_field_path?: string;
  /** 中文名 */
  field_name?: string;
  /** 字段类型 */
  value_type?: string;
  /** 字段校验信息，json字符串 */
  validation?: string;
  /** 字段描述 */
  description?: string;
  /** 字段示例，json字符串 */
  example?: string;
  /** 生产逻辑 */
  produce_logic?: string;
  /** 负责人，仅一人 */
  owner?: string;
  /** tapd链接 */
  tapd?: string;
  /** 拦截规则: -1-不拦截; 0-拦截校验失败的字段; 1-拦截一级字段; 2-拦截二级字段 */
  block_rule?: BlockRule;
}

export interface GetEntityTypesRequest {
  /** 字段分类(注：静态字段或动态字段) */
  field_class?: FieldClass;
}

export interface GetEntityTypesReplyEntity {
  /** 介质类型(如:article, video, question, live_streaming, long_video...) */
  type?: string;
  /** 介质名称(如：图文, 视频, 问答, 直播, 长视频...) */
  name_ch?: string;
}

export interface GetEntityTypesReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  entities?: GetEntityTypesReplyEntity[];
}

export enum ColumnFamilyStatus {
  /** 未知状态 */
  COLUMN_FAMILY_STATUS_UNKNOWN = 0,
  /** 生效 */
  COLUMN_FAMILY_STATUS_ONLINE = 1,
  /** 不生效 */
  COLUMN_FAMILY_STATUS_OFFLINE = -1,
}

export interface GetColumnFamiliesRequest {
  /** 介质类型 */
  entity_type?: string;
  /** 字段分类(注：静态字段或动态字段) */
  field_class?: FieldClass;
  /** 列簇状态 */
  column_family_status?: ColumnFamilyStatus;
}

export interface GetColumnFamiliesReplyColumnFamily {
  /** 列簇名称(如：default) */
  name?: string;
  /** 描述信息 */
  description?: string;
  /** 列簇状态 */
  column_family_status?: ColumnFamilyStatus;
}

export interface GetColumnFamiliesReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 列簇列表 */
  column_families?: GetColumnFamiliesReplyColumnFamily[];
}

export interface UpdateFieldRequest {
  /** 工单信息 */
  order?: OrderUpdateData;
  /** 字段分类(注：静态字段或动态字段) */
  field_class?: FieldClass;
  /** 更新字段列表 */
  fields?: FieldSchemaUpdateData[];
}

export interface UpdateFieldReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 审批流ID，来自流云系统 */
  workflow_id?: number;
}

export interface OfflineFieldRequestField {
  /** 介质类型 */
  entity_type?: string;
  /** 列簇名称 */
  column_family?: string;
  /** 字段路径 */
  field_path?: string;
}

export interface OfflineFieldRequest {
  /** 工单信息 */
  order?: OrderUpdateData;
  /** 字段分类(注：静态字段或动态字段) */
  field_class?: FieldClass;
  /** 字段信息 */
  fields?: OfflineFieldRequestField[];
}

export interface OfflineFieldReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 审批流ID，来自流云系统 */
  workflow_id?: number;
}

export interface GetFieldsRequest {
  /** 页码-从1开始[注：用户输入] */
  page_num?: number;
  /** 每页条数（当为-1时，表示拉取全量数据）[注：用户输入] */
  page_size?: number;
  /** 字段分类 */
  field_class?: FieldClass;
  /** 实体类型，也即介质类型 */
  entity_type?: string;
  /** 列簇名称 */
  column_family?: string;
  /** 字段路径[注：支持"模糊匹配"] */
  field_path?: string;
  /** 源字段路径 */
  src_field_path?: string;
  /** 字段名称[注：支持"模糊匹配"] */
  field_name?: string;
  /** 字段状态 */
  status?: FieldStatus;
  /** 负责人 */
  owner?: string;
}

export interface GetFieldsReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 总条目数 */
  total?: number;
  /** 字段列表 */
  field_details?: FieldDetail[];
}

export interface GetFieldDetailRequest {
  /** 字段分类，根据动静态，区分对接方式 1-KAFKA(动态支持), 2-API/SDK(静态支持) */
  field_class?: FieldClass;
  /** 实体类型，也即介质类型 */
  entity_type?: string;
  /** 字段路径 */
  field_path?: string;
}

export interface GetFieldDetailReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 字段详情 */
  field?: FieldDetail;
  /** 关联生产方信息 */
  producers?: AppSummary[];
  /** 关联消费方信息 */
  consumers?: AppSummary[];
}

export interface GetFieldProducibleRequest {
  /** 页码-从1开始[注：用户输入] */
  page_num?: number;
  /** 每页条数（当为-1时，表示拉取全量数据）[注：用户输入] */
  page_size?: number;
  /** 字段分类，根据动静态，区分对接方式 1-KAFKA(动态支持), 2-API/SDK(静态支持) */
  field_class?: FieldClass;
  /** 实体类型，也即介质类型 */
  entity_type?: string;
  /** 列簇名称 */
  column_family?: string;
  /** 字段路径[注：支持"模糊匹配"] */
  field_path?: string;
}

export interface GetFieldProducibleReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 总条目数 */
  total?: number;
  /** 字段列表 */
  fields?: FieldDetail[];
}

export interface GetFieldExportableRequest {
  /** 页码-从1开始[注：用户输入] */
  page_num?: number;
  /** 每页条数（当为-1时，表示拉取全量数据）[注：用户输入] */
  page_size?: number;
  /** 字段分类，根据动静态，区分对接方式 1-KAFKA(动态支持), 2-API/SDK(静态支持) */
  field_class?: FieldClass;
  /** 实体类型，也即介质类型 */
  entity_type?: string;
  /** 列簇名称 */
  column_family?: string;
  /** 字段路径[注：支持"模糊匹配"] */
  field_path?: string;
}

export interface GetFieldExportableReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 总条目数 */
  total?: number;
  /** 可分发字段列表 */
  fields?: FieldDetail[];
}

export interface GetFieldOrderDetailRequest {
  /** 工单 ID */
  order_id?: number;
}

export interface GetFieldOrderDetailReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 总条目数 */
  total?: number;
  /** 关联的历史工单信息 */
  order?: OrderDetail;
  /** 字段列表 */
  fields?: FieldDetail[];
}

export interface ProducerUpdateDataAPI {
  /** 主调方[注：用户输入] */
  caller?: string;
}

export interface ProducerUpdateDataFieldSet {
  /** 介质类型 */
  entity_type?: string;
  /** 勾选字段列表[注：由用户勾选] */
  fields?: string[];
  /** 新增字段列表[注：用户输入] */
  add_fields?: FieldSchemaUpdateData[];
}

export interface ProducerUpdateData {
  /** 字段分类：动态或静态[注：用户输入] */
  field_class?: FieldClass;
  /** 对接类型: kafka/API[注：由用户勾选] */
  invoke_type?: InvokeType;
  /** API 配置 */
  api?: ProducerUpdateDataAPI;
  /** 限流频次(QPS)[注：由用户输入] */
  rate_limit?: number;
  /** 字段集合(按介质类型分组) */
  field_sets?: ProducerUpdateDataFieldSet[];
  /** 是否生产所有字段 */
  is_all_fields?: number;
}

export interface Producer {
  /** 应用 ID，申请通过后分配 */
  app_id?: number;
  /** 应用名称 */
  app_name?: string;
  /** 应用中文名称 */
  app_name_ch?: string;
  /** 负责人 */
  owner?: string;
  /** 说明 */
  description?: string;
  /** 应用密钥，后台生成 */
  app_key?: string;
  /** 应用状态 */
  status?: AppStatus;
  /** 字段分类：动态或静态 */
  field_class?: FieldClass;
  /** 对接类型: kafka/API */
  invoke_type?: InvokeType;
  /** 总库接入侧的服务名 */
  receiver_service?: string;
  /** API名称 */
  api_name?: string;
  /** kafka地址 */
  kafka_addr?: string;
  /** kafka topic */
  kafka_topic?: string;
  /** kafka地址 */
  test_kafka_addr?: string;
  /** kafka topic */
  test_kafka_topic?: string;
  /** 生产方服务名 */
  producer_service?: string;
  /** 限流频次(QPS) */
  rate_limit?: number;
  /** 生产字段，json字符串 */
  produce_fields?: string;
  /** 申请人的直接leader */
  leader?: string;
  /** 申请人团队 */
  team?: string;
  /** 新增的生产字段（只在生产方审核表中有） */
  add_fields?: string;
  /** 测试应用密钥，后台生成 */
  test_app_key?: string;
  /** 是否生产所有字段 */
  is_all_fields?: number;
}

export interface CreateProducerRequest {
  /** 工单申请信息 */
  order?: OrderUpdateData;
  /** 应用信息 */
  application?: AppUpdateData;
  /** 生产方信息 */
  producer?: ProducerUpdateData;
}

export interface CreateProducerReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 审批流ID，来自流云系统 */
  workflow_id?: number;
}

export interface UpdateProducerRequest {
  /** 工单申请信息 */
  order?: OrderUpdateData;
  /** 应用信息 */
  application?: AppUpdateData;
  /** 生产方信息 */
  producer?: ProducerUpdateData;
}

export interface UpdateProducerReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 审批流ID，来自流云系统 */
  workflow_id?: number;
}

export interface OfflineProducerRequest {
  /** 工单申请信息 */
  order?: OrderUpdateData;
  /** 应用名称[注：用户输入] */
  app_name?: string;
}

export interface OfflineProducerReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 审批流ID，来自流云系统 */
  workflow_id?: number;
}

export interface GetProducersRequest {
  /** 页码[注：用户输入] */
  page_num?: number;
  /** 每页条数[注：用户输入] */
  page_size?: number;
  /** app_id[注：用户输入] */
  app_id?: number;
  /** 字段分类(动态或静态)[注：用户输入] */
  field_class?: FieldClass;
  /** 对接方式（API或KAFKA）[注：用户输入] */
  invoke_type?: InvokeType;
  /** 生产方名称[注：用户输入] */
  app_name?: string;
  /** 生产方中文名称[注：用户输入] */
  app_name_ch?: string;
  /** 负责人[注：用户输入] */
  owner?: string;
  /** app状态[注：用户输入] */
  status?: AppStatus;
}

export interface GetProducersReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 总数目 */
  total?: number;
  /** 生产方应用列表 */
  apps?: AppSummary[];
}

export interface GetProducerDetailRequest {
  /** 应用名称 */
  app_name?: string;
}

export interface GetProducerDetailReplyFieldWithConsumer {
  field_detail?: FieldDetail;
  consumer_count?: number;
}

export interface GetProducerDetailReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 生产方信息 */
  producer?: Producer;
  /** 关联的历史工单信息 */
  orders?: OrderDetail[];
  /** 相关联的字段详情携带消费方数量 */
  field_details?: GetProducerDetailReplyFieldWithConsumer[];
}

export interface GetProducerOrderDetailRequest {
  /** 工单 ID */
  order_id?: number;
}

export interface GetProducerOrderDetailReplyFieldDetailSet {
  /** 介质类型 */
  entity_type?: string;
  /** 已有字段详情列表 */
  exist_fields?: FieldDetail[];
  /** 新增字段详情列表 */
  add_fields?: FieldDetail[];
}

export interface GetProducerOrderDetailReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 工单信息 */
  order?: OrderDetail;
  /** 工单中关联的生产方信息(注意：需从生产方审核表查询) */
  producer?: Producer;
  /** 相关联的字段详情(注意：新增字段需从字段审核表查询，非新增字段从字段SCHEMA表中查询) */
  field_detail_sets?: GetProducerOrderDetailReplyFieldDetailSet[];
}

export interface GetConsumersRequest {
  /** 页码[注：用户输入] */
  page_num?: number;
  /** 每页条数[注：用户输入] */
  page_size?: number;
  /** app_id[注：用户输入] */
  app_id?: number;
  /** 字段分类(动态或静态)[注：用户输入] */
  field_class?: FieldClass;
  /** 对接方式（API或KAFKA）[注：用户输入] */
  invoke_type?: InvokeType;
  /** 消费方名称[注：用户输入] */
  app_name?: string;
  /** 消费方中文名称[注：用户输入] */
  app_name_ch?: string;
  /** 负责人[注：用户输入] */
  owner?: string;
  status?: AppStatus;
}

export interface GetConsumersReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 总条目数 */
  total?: number;
  /** 消费方应用列表 */
  apps?: AppSummary[];
}

export interface GetConsumerDetailRequest {
  /** 应用名称[注：用户输入] */
  app_name?: string;
}

export interface GetConsumerDetailReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 消费方信息 */
  consumer?: Consumer;
  /** 应用信息 */
  application?: AppSummary;
  /** 关联的历史工单信息 */
  orders?: OrderDetail[];
}

export interface SubscribeServiceAPI {
  /** 主调服务名[注：用户输入] */
  caller?: string;
  rate_limit?: number;
}

export interface ConsumerUpdateDataSubscribeService {
  /** 对接类型: kafka/API[注：用户勾选] -- 不可修改 */
  invoke_type?: InvokeType;
  /** 订阅字段类型 */
  field_class?: FieldClass;
  /** API 配置（注：对接类型为API时 才需配置） */
  api?: SubscribeServiceAPI;
}

export interface ConsumerUpdateData {
  /** 分发服务相关配置，Kafka 或 API */
  subscribe_service?: ConsumerUpdateDataSubscribeService;
  /** 订阅字段配置 */
  subscribe_fields?: SubscribeFields;
  /** 订阅条件配置 */
  subscribe_control?: SubscribeControl;
  /** 可观测性配置 */
  observability?: Observability;
}

export interface CreateConsumerRequest {
  /** 工单申请信息 */
  order?: OrderUpdateData;
  /** 应用信息 */
  application?: AppUpdateData;
  /** 消费方信息 */
  consumer?: ConsumerUpdateData;
}

export interface CreateConsumerReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 审批流ID，来自流云系统 */
  workflow_id?: number;
}

export interface UpdateConsumerRequest {
  /** 工单申请信息 */
  order?: OrderUpdateData;
  /** 应用信息 */
  application?: AppUpdateData;
  /** 消费方信息 */
  consumer?: ConsumerUpdateData;
}

export interface UpdateConsumerReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 审批流ID，来自流云系统 */
  workflow_id?: number;
}

export interface OfflineConsumerRequest {
  /** 工单申请信息 */
  order?: OrderUpdateData;
  /** 应用名称 */
  app_name?: string;
}

export interface OfflineConsumerReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 审批流ID，来自流云系统 */
  workflow_id?: number;
}

export interface GetConsumerOrderDetailRequest {
  /** 工单 ID */
  order_id?: number;
}

export interface GetConsumerOrderDetailReply {
  /** 返回码 */
  code?: number;
  /** 返回消息 */
  message?: string;
  /** 工单信息 */
  order?: OrderDetail;
  /** 应用信息 */
  application?: AppUpdateData;
  /** 工单中关联的消费方信息 */
  consumer?: Consumer;
}

export interface Consumer {
  /** 应用名称，唯一 */
  app_name?: string;
  /** 分发服务相关配置，Kafka 或 API */
  subscribe_service?: SubscribeService;
  /** 订阅字段配置 */
  subscribe_fields?: SubscribeFields;
  /** 订阅条件配置 */
  subscribe_control?: SubscribeControl;
  /** 可观测性配置 */
  observability?: Observability;
}

export enum SubscribeType {
  /** 未知订阅类型 */
  SUBSCRIBE_TYPE_UNKNOWN = 0,
  /** 普通订阅类型 */
  SUBSCRIBE_TYPE_NORMAL = 1,
  /** 事件订阅类型 */
  SUBSCRIBE_TYPE_EVENT = 2,
}

export enum FieldFilterMode {
  /** 未知模式 */
  FIELD_FILTER_MODE_UNKNOWN = 0,
  /** 普通模式 */
  FIELD_FILTER_MODE_NORMAL = 1,
  /** 黑名单模式 */
  FIELD_FILTER_MODE_BLACK = 2,
  /** 全量模式 */
  FIELD_FILTER_MODE_ALL = 3,
}

export enum ProtocolVersion {
  PROTOCOL_VERSION_UNKNOWN = 0,
  PROTOCOL_VERSION_V11 = 101,
  PROTOCOL_VERSION_V12 = 102,
  PROTOCOL_VERSION_V20 = 200,
}

export interface SubscribeControl {
  /** 是否过滤清洗流量 */
  is_clean_filter?: boolean;
  /** 触发器集合 */
  triggers?: Trigger[];
  /** 全局过滤器 */
  global_trigger?: GlobalTrigger;
  /** 数据来源,increment:总库变更流,api:手动触发,refresh:刷库任务,relation:关系流,dyanmic:动态字段边缘触发 */
  sources?: string[];
  /** 订阅类型 */
  subscribe_type?: SubscribeType;
  /** 字段筛选模式 */
  field_filter_mode?: FieldFilterMode;
  /** 是否需要全量数据 */
  need_full_data?: boolean;
  /** 是否需要前值 */
  need_pre_data?: boolean;
  /** 协议版本 */
  protocol_version?: ProtocolVersion;
}

export interface Trigger {
  /** 数据类型：静态特征/动态特征 */
  field_class?: FieldClass;
  /** 实体类型: article/video 等 */
  entity_type?: string;
  /** 触发器类型：change(特征变化时下发upsert消息),expr(条件表达式),pass(由全局下发条件决定),view(视图条件表达式) */
  trigger_type?: string;
  /** 表达式集合 */
  expressions?: Expression[];
}

export enum ExpressionAction {
  /** 未知动作 */
  ACTION_UNKNOWN = 0,
  /** 更新或插入 */
  ACTION_UPSERT = 1,
  /** 删除 */
  ACTION_DELETE = 2,
  /** 不下发 */
  ACTION_STOP = 3,
}

export interface Expression {
  /** 表达式名称 */
  name?: string;
  /** 表达式动作 */
  action?: ExpressionAction;
  /** 表达式条件 */
  condition?: string;
  /** 动态特征，边缘触发的 business_id */
  event_type?: string;
  /** 动态特征，边缘触发的 wxp_boutique_pool_in */
  event_id?: string;
}

export interface GlobalTrigger {
  /** 是否启用 */
  status?: boolean;
  /** 字段是否改变 */
  field_changed?: boolean;
  /** 变更事件判断条件：expr表达式，golang语法 */
  upsert?: string;
  /** 删除事件判断条件：expr表达式，golang语法 */
  delete?: string;
}

export interface SubscribeService {
  /** 对接类型: kafka/API */
  invoke_type?: InvokeType;
  /** 订阅字段类型 */
  field_class?: FieldClass;
  /** 测试API 配置 */
  test_api?: API;
  /** 正式API 配置 */
  prod_api?: API;
  /** 测试Kafka 配置 */
  test_kafka?: Kafka;
  /** 正式Kafka 配置 */
  prod_kafka?: Kafka;
}

export interface API {
  /** 是否启用 AppKey 鉴权方式 */
  check_app_key?: boolean;
  /** AppKey */
  app_key?: string;
  /** 是否启用主调方名鉴权方式 */
  check_caller?: boolean;
  /** 主调方 */
  caller?: string;
  /** 服务名称 */
  service_name?: string;
  /** API 名称 */
  api_name?: string;
  rate_limit?: number;
}

export interface Kafka {
  /** Kafka 域名 */
  address?: string;
  /** Kafka 主题 */
  topic?: string;
  /** Kafka 消费组 */
  group?: string;
  /** Kafka 生产者 ID */
  producer_id?: string;
  /** 压缩方式:lz4,gzip,none */
  compression?: string;
  /** 分区策略: hash, random */
  partitioner?: string;
  /** 读 ACL 策略 */
  read_acl_strategy?: ACLStrategy;
  /** 写 ACL 策略 */
  write_acl_strategy?: ACLStrategy;
  /** 是否只打印日志，不实际下发 */
  log_only?: boolean;
}

export interface ACLStrategy {
  /** 是否启用 ACL */
  status?: boolean;
  /** 用户名 */
  user?: string;
  /** 密码 */
  password?: string;
}

export enum SerializationType {
  /** 未知序列化类型 */
  SERIALIZATION_TYPE_UNKNOWN = 0,
  /** JSON 序列化 */
  SERIALIZATION_TYPE_JSON = 1,
  /** Protocol Buffers 序列化 */
  SERIALIZATION_TYPE_PB = 2,
}

export enum EncodeType {
  /** 未知编码类型 */
  ENCODE_TYPE_UNKNOWN = 0,
  /** 不需要编码 */
  ENCODE_TYPE_NONE = 1,
  /** Base64编码 */
  ENCODE_TYPE_BASE64 = 2,
  /** Snappy编码 */
  ENCODE_TYPE_SNAPPY = 3,
}

export interface TransformFieldSet {
  /** 字段分类 */
  field_class?: FieldClass;
  /** 实体类型: article/video 等 */
  entity_type?: string;
  /** 字段列表 */
  fields?: string[];
  /** 依赖但不下发的字段列表 */
  dependent_fields?: string[];
  /** 数据转换配置 */
  transform?: Transform;
}

export interface SubscribeFields {
  /** 订阅字段集合(按介质类型归类) */
  field_sets?: TransformFieldSet[];
  /** 数据序列化类型 */
  serialization_type?: SerializationType;
  /** 数据编码类型 */
  encode_type?: EncodeType;
  /** 全局数据转换配置 */
  global_transform?: GlobalTransform;
  /** 数据关联配置 */
  joins?: Join[];
}

export interface Transform {
  /** JSON转换插件(many)的表达式，json语法 */
  many?: string;
}

export interface GlobalTransform {
  /** JSON转换插件(many)的表达式，json语法 */
  many?: string;
  /** JSON转换插件(component)的表达式，json语法 */
  component?: string;
}

export interface Join {
  /** 连接名称 */
  name?: string;
  /** 关联实体ID字段路径 */
  id_path?: string;
  /** 连接条件 */
  condition?: string;
  /** 字段列表 */
  fields?: FieldSet;
}

export interface Observability {
  /** 日志采样 */
  log_sample?: LogSample;
  /** 字段监控 */
  field_monitors?: FieldMonitor[];
}

export interface LogSample {
  /** 采样率 */
  sample_rate?: number;
  /** 采样开始时间 */
  start_time?: number;
}

export interface FieldMonitorConfig {
  /** 字段路径 */
  path?: string;
  /** 可能的取值 */
  values?: string[];
}

export interface FieldMonitor {
  /** 监控名称 */
  name?: string;
  /** 实体类型 */
  entity_types?: string[];
  /** 配置 */
  config?: FieldMonitorConfig[];
}
