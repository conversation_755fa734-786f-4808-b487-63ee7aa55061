/* eslint-disable @typescript-eslint/no-require-imports */
/* eslint-env node */
const path = require('path');
const fs = require('fs');
const shell = require('shelljs');
const tupload2 = require('@tencent/tupload2');

const config = {
  site: 'mat1.gtimg.com',
  baseUrl: `/qqcdn/${process.env.CMS_APP_NAME}/`,
  token: process.env.CMS_CDN_TOKEN,
};

const dir = path.join(__dirname, '..', 'build');

// 并发控制函数
async function batchProcess(items, processFn, batchSize = 5) {
  const results = [];
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchResults = await Promise.all(batch.map(item => processFn(item)));
    results.push(...batchResults);
  }
  return results;
}

async function uploadFile(file, sourceDir, targetDir) {
  const relativePath = path.relative(sourceDir, file);
  const targetPath = path.join(targetDir, relativePath).replace(/\\/g, '/');

  try {
    console.log(`Uploading: ${relativePath} -> ${targetPath}`);
    await tupload2.upload(file, targetPath, config);
    return { file, success: true };
  } catch (error) {
    console.error(`Failed to upload ${file}:`, error);
    return { file, success: false, error };
  }
}

async function uploadDirectory(sourceDir, targetDir) {
  const files = shell.find(sourceDir).filter(file => fs.statSync(file).isFile());

  console.log(`Found ${files.length} files to upload`);

  const results = await batchProcess(
    files,
    file => uploadFile(file, sourceDir, targetDir),
    5, // 每批处理5个文件
  );

  // 统计上传结果
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;

  console.log('\nUpload Summary:');
  console.log(`Total files: ${files.length}`);
  console.log(`Successful: ${successful}`);
  console.log(`Failed: ${failed}`);

  if (failed > 0) {
    console.log('\nFailed files:');
    results.filter(r => !r.success).forEach((r) => {
      console.log(`- ${r.file}`);
    });
  }
}

// 上传整个 public 目录
uploadDirectory(dir, config.baseUrl).catch(console.error);
