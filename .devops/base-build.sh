#!/bin/bash

configure_npm_registry() {
  npm config set registry https://mirrors.tencent.com/npm/
}

install_dependencies() {
  local pnpm_version
  local node_version
  local turbo_version

  pnpm_version=$(node -e "console.log(require('./package.json').volta.pnpm);")
  node_version=$(node -e "console.log(require('./package.json').volta.node);")
  turbo_version=$(node -e "console.log(require('./package.json').devDependencies.turbo);")

  echo "node version in docker: $(node -v)"
  echo "pnpm version in docker: $(pnpm -v)"
  #  echo "turbo version: $(turbo --version)"
  #  echo "pnpm version in workspace: $(npx pnpm@"${pnpm_version}" -v)"
  echo $(node -e "console.log('custom build DEPLOY_ENV', process.env.DEPLOY_ENV)")

  npx pnpm@"${pnpm_version}" install --frozen-lockfile
  export PATH=$(./node_modules/.bin/pnpm bin):$PATH
}
